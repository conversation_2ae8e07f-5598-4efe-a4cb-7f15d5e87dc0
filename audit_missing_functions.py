#!/usr/bin/env python3
"""
Comprehensive Audit of Missing Functions
Checks what specific functionality is missing from each page
"""

import requests
import json
import time

# Configuration
BACKEND_URL = "http://localhost:8000"

class FunctionAuditor:
    def __init__(self):
        self.missing_functions = []
        self.token = None

    def get_auth_token(self):
        """Get authentication token"""
        try:
            response = requests.post(
                f"{BACKEND_URL}/api/auth/login/",
                json={"username": "superadmin", "password": "password123"},
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('access')
                return True
            return False
        except Exception as e:
            print(f"❌ Authentication failed: {str(e)}")
            return False

    def log_missing_function(self, page, function, description, priority="HIGH"):
        self.missing_functions.append({
            'page': page,
            'function': function,
            'description': description,
            'priority': priority
        })
        print(f"❌ {priority}: {page} - Missing {function}: {description}")

    def audit_employee_management(self):
        """Audit Employee Management functionality"""
        print("\n🔍 Auditing Employee Management...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        # Test basic CRUD operations
        try:
            # GET employees
            response = requests.get(f"{BACKEND_URL}/api/employees/", headers=headers, timeout=5)
            if response.status_code == 200:
                employees = response.json().get('results', [])
                print(f"✅ Employee listing: {len(employees)} employees found")
                
                if employees:
                    employee_id = employees[0]['id']
                    
                    # Test individual employee details
                    detail_response = requests.get(f"{BACKEND_URL}/api/employees/{employee_id}/", headers=headers, timeout=5)
                    if detail_response.status_code == 200:
                        print("✅ Employee detail view working")
                    else:
                        self.log_missing_function("Employee Management", "Detail View", "Cannot view individual employee details")
                    
                    # Test employee update
                    update_data = {"firstName": "Test Update"}
                    update_response = requests.patch(f"{BACKEND_URL}/api/employees/{employee_id}/", 
                                                   json=update_data, headers=headers, timeout=5)
                    if update_response.status_code == 200:
                        print("✅ Employee update working")
                    else:
                        self.log_missing_function("Employee Management", "Update Function", "Cannot update employee information")
                else:
                    self.log_missing_function("Employee Management", "Sample Data", "No employees in database to test with")
            else:
                self.log_missing_function("Employee Management", "List View", "Cannot retrieve employee list")
                
            # Test employee creation
            import random
            unique_id = f'TEST{random.randint(1000, 9999)}'
            new_employee_data = {
                "firstName": "Test",
                "lastName": "Employee",
                "email": f"test.employee{random.randint(100, 999)}@example.com",
                "employee_id": unique_id,
                "position": "Test Position",
                "position_ar": "منصب تجريبي",
                "department": 24,  # Human Resources
                "gender": "M",
                "hire_date": "2024-01-15",
                "salary": 5000
            }
            create_response = requests.post(f"{BACKEND_URL}/api/employees/", 
                                          json=new_employee_data, headers=headers, timeout=5)
            if create_response.status_code == 201:
                print("✅ Employee creation working")
                # Clean up - delete the test employee
                new_employee_id = create_response.json().get('id')
                if new_employee_id:
                    requests.delete(f"{BACKEND_URL}/api/employees/{new_employee_id}/", headers=headers)
            else:
                self.log_missing_function("Employee Management", "Create Function", "Cannot create new employees")
                
        except Exception as e:
            self.log_missing_function("Employee Management", "API Connection", f"API error: {str(e)}")

    def audit_dashboard_functionality(self):
        """Audit Dashboard functionality"""
        print("\n🔍 Auditing Dashboard Functionality...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        # Test dashboard stats
        try:
            response = requests.get(f"{BACKEND_URL}/api/dashboard-stats/", headers=headers, timeout=5)
            if response.status_code == 200:
                stats = response.json()
                required_stats = ['total_employees', 'total_departments', 'active_projects', 'pending_tasks']
                
                missing_stats = [stat for stat in required_stats if stat not in stats]
                if missing_stats:
                    self.log_missing_function("Dashboard", "Statistics", f"Missing stats: {missing_stats}")
                else:
                    print("✅ Dashboard statistics working")
                    
                # Check if stats have real data
                if all(stats.get(stat, 0) == 0 for stat in required_stats):
                    self.log_missing_function("Dashboard", "Real Data", "All statistics showing zero - no real data")
                else:
                    print("✅ Dashboard showing real data")
            else:
                self.log_missing_function("Dashboard", "Stats API", "Dashboard statistics not loading")
                
        except Exception as e:
            self.log_missing_function("Dashboard", "API Connection", f"Dashboard API error: {str(e)}")

    def audit_project_management(self):
        """Audit Project Management functionality"""
        print("\n🔍 Auditing Project Management...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        try:
            # Test projects listing
            response = requests.get(f"{BACKEND_URL}/api/projects/", headers=headers, timeout=5)
            if response.status_code == 200:
                projects = response.json().get('results', [])
                print(f"✅ Project listing: {len(projects)} projects found")
                
                # Test project creation
                new_project_data = {
                    "name": "Test Project",
                    "name_ar": "مشروع تجريبي",
                    "description": "Test project description",
                    "start_date": "2024-01-01",
                    "end_date": "2024-12-31",
                    "status": "PLANNING"
                }
                create_response = requests.post(f"{BACKEND_URL}/api/projects/", 
                                              json=new_project_data, headers=headers, timeout=5)
                if create_response.status_code == 201:
                    print("✅ Project creation working")
                    # Clean up
                    project_id = create_response.json().get('id')
                    if project_id:
                        requests.delete(f"{BACKEND_URL}/api/projects/{project_id}/", headers=headers)
                else:
                    self.log_missing_function("Project Management", "Create Function", "Cannot create new projects")
            else:
                self.log_missing_function("Project Management", "List View", "Cannot retrieve project list")
                
        except Exception as e:
            self.log_missing_function("Project Management", "API Connection", f"Project API error: {str(e)}")

    def audit_hr_functionality(self):
        """Audit HR functionality"""
        print("\n🔍 Auditing HR Functionality...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        # Test leave requests
        try:
            response = requests.get(f"{BACKEND_URL}/api/leave-requests/", headers=headers, timeout=5)
            if response.status_code == 200:
                print("✅ Leave requests listing working")
            else:
                self.log_missing_function("HR Management", "Leave Requests", "Cannot retrieve leave requests")
                
            # Test attendance
            response = requests.get(f"{BACKEND_URL}/api/attendance/", headers=headers, timeout=5)
            if response.status_code == 200:
                print("✅ Attendance tracking working")
            else:
                self.log_missing_function("HR Management", "Attendance", "Attendance tracking not working")
                
            # Test performance reviews
            response = requests.get(f"{BACKEND_URL}/api/performance-reviews/", headers=headers, timeout=5)
            if response.status_code == 200:
                print("✅ Performance reviews working")
            else:
                self.log_missing_function("HR Management", "Performance Reviews", "Performance review system not working")
                
        except Exception as e:
            self.log_missing_function("HR Management", "API Connection", f"HR API error: {str(e)}")

    def audit_financial_functionality(self):
        """Audit Financial functionality"""
        print("\n🔍 Auditing Financial Functionality...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        try:
            # Test budgets
            response = requests.get(f"{BACKEND_URL}/api/budgets/", headers=headers, timeout=5)
            if response.status_code == 200:
                print("✅ Budget management working")
            else:
                self.log_missing_function("Financial Management", "Budgets", "Budget management not working")
                
            # Test expenses
            response = requests.get(f"{BACKEND_URL}/api/expenses/", headers=headers, timeout=5)
            if response.status_code == 200:
                print("✅ Expense tracking working")
            else:
                self.log_missing_function("Financial Management", "Expenses", "Expense tracking not working")
                
            # Test invoices
            response = requests.get(f"{BACKEND_URL}/api/invoices/", headers=headers, timeout=5)
            if response.status_code == 200:
                print("✅ Invoice management working")
            else:
                self.log_missing_function("Financial Management", "Invoices", "Invoice management not working")
                
        except Exception as e:
            self.log_missing_function("Financial Management", "API Connection", f"Financial API error: {str(e)}")

    def audit_customer_service(self):
        """Audit Customer Service functionality"""
        print("\n🔍 Auditing Customer Service...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        try:
            # Test support tickets
            response = requests.get(f"{BACKEND_URL}/api/customer-service/tickets/", headers=headers, timeout=5)
            if response.status_code == 200:
                print("✅ Support tickets working")
            else:
                self.log_missing_function("Customer Service", "Support Tickets", "Support ticket system not working")
                
            # Test live chat
            response = requests.get(f"{BACKEND_URL}/api/customer-service/live-chat-sessions/", headers=headers, timeout=5)
            if response.status_code == 200:
                print("✅ Live chat working")
            else:
                self.log_missing_function("Customer Service", "Live Chat", "Live chat system not working")
                
            # Test knowledge base
            response = requests.get(f"{BACKEND_URL}/api/customer-service/kb-articles/", headers=headers, timeout=5)
            if response.status_code == 200:
                print("✅ Knowledge base working")
            else:
                self.log_missing_function("Customer Service", "Knowledge Base", "Knowledge base not working")
                
        except Exception as e:
            self.log_missing_function("Customer Service", "API Connection", f"Customer service API error: {str(e)}")

    def run_comprehensive_audit(self):
        """Run comprehensive functionality audit"""
        print("🔍 Starting Comprehensive Functionality Audit...")
        print("=" * 70)
        
        # Get authentication
        if not self.get_auth_token():
            print("❌ Cannot proceed without authentication")
            return
        
        # Audit all major areas
        self.audit_dashboard_functionality()
        self.audit_employee_management()
        self.audit_project_management()
        self.audit_hr_functionality()
        self.audit_financial_functionality()
        self.audit_customer_service()
        
        # Generate report
        self.generate_functionality_report()

    def generate_functionality_report(self):
        """Generate comprehensive functionality report"""
        print("\n" + "=" * 70)
        print("📋 FUNCTIONALITY AUDIT REPORT")
        print("=" * 70)
        
        if not self.missing_functions:
            print("\n🎉 ALL CORE FUNCTIONALITY IS WORKING!")
            print("The EMS application has complete functionality across all modules.")
        else:
            print(f"\n🚨 MISSING FUNCTIONALITY ({len(self.missing_functions)} issues):")
            
            # Group by priority
            high_priority = [f for f in self.missing_functions if f['priority'] == 'HIGH']
            medium_priority = [f for f in self.missing_functions if f['priority'] == 'MEDIUM']
            low_priority = [f for f in self.missing_functions if f['priority'] == 'LOW']
            
            if high_priority:
                print(f"\n🔥 HIGH PRIORITY ({len(high_priority)} issues):")
                for func in high_priority:
                    print(f"  • {func['page']}: {func['function']}")
                    print(f"    {func['description']}")
                    print()
            
            if medium_priority:
                print(f"\n⚠️ MEDIUM PRIORITY ({len(medium_priority)} issues):")
                for func in medium_priority:
                    print(f"  • {func['page']}: {func['function']}")
                    print(f"    {func['description']}")
                    print()
            
            if low_priority:
                print(f"\n💡 LOW PRIORITY ({len(low_priority)} issues):")
                for func in low_priority:
                    print(f"  • {func['page']}: {func['function']}")
                    print(f"    {func['description']}")
                    print()
        
        print(f"\n📈 SUMMARY:")
        print(f"Total functionality issues found: {len(self.missing_functions)}")
        if len(self.missing_functions) > 0:
            print(f"Recommended next steps: Fix high priority issues first")

if __name__ == "__main__":
    auditor = FunctionAuditor()
    auditor.run_comprehensive_audit()
