import React, { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Users,
  Building,
  Briefcase,
  Clock,
  FileText,
  DollarSign,
  Activity,
  Server,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown
} from 'lucide-react'
import { dashboardAPI, type DashboardStats } from '@/services/api'

interface RealDashboardProps {
  language: 'ar' | 'en'
  userRole: string
}

const translations = {
  ar: {
    dashboard: 'لوحة التحكم',
    loading: 'جاري التحميل...',
    error: 'خطأ في تحميل البيانات',
    retry: 'إعادة المحاولة',
    refresh: 'تحديث',
    totalEmployees: 'إجمالي الموظفين',
    totalDepartments: 'إجمالي الأقسام',
    activeProjects: 'المشاريع النشطة',
    pendingTasks: 'المهام المعلقة',
    pendingLeaveRequests: 'طلبات الإجازة المعلقة',
    monthlyExpenses: 'المصروفات الشهرية',
    systemHealth: 'صحة النظام',
    cpuUsage: 'استخدام المعالج',
    memoryUsage: 'استخدام الذاكرة',
    diskUsage: 'استخدام القرص',
    lastUpdated: 'آخر تحديث',
    dataLoadedSuccessfully: 'تم تحميل البيانات بنجاح',
    failedToLoadData: 'فشل في تحميل البيانات'
  },
  en: {
    dashboard: 'Dashboard',
    loading: 'Loading...',
    error: 'Error loading data',
    retry: 'Retry',
    refresh: 'Refresh',
    totalEmployees: 'Total Employees',
    totalDepartments: 'Total Departments',
    activeProjects: 'Active Projects',
    pendingTasks: 'Pending Tasks',
    pendingLeaveRequests: 'Pending Leave Requests',
    monthlyExpenses: 'Monthly Expenses',
    systemHealth: 'System Health',
    cpuUsage: 'CPU Usage',
    memoryUsage: 'Memory Usage',
    diskUsage: 'Disk Usage',
    lastUpdated: 'Last Updated',
    dataLoadedSuccessfully: 'Data loaded successfully',
    failedToLoadData: 'Failed to load data'
  }
}

export default function RealDashboard({ language, userRole }: RealDashboardProps) {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const t = translations[language]
  const isRTL = language === 'ar'

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const data = await dashboardAPI.getStats()
      setStats(data)
      setLastUpdated(new Date())
      
      console.log('Dashboard data loaded successfully:', data)
    } catch (err) {
      console.error('Failed to fetch dashboard data:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const handleRefresh = () => {
    fetchDashboardData()
  }

  if (loading && !stats) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white text-xl">{t.loading}</p>
        </div>
      </div>
    )
  }

  if (error && !stats) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="text-center">
          <Alert className="mb-4 max-w-md">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {t.error}: {error}
            </AlertDescription>
          </Alert>
          <Button onClick={handleRefresh} className="glass-button">
            <RefreshCw className="h-4 w-4 mr-2" />
            {t.retry}
          </Button>
        </div>
      </div>
    )
  }

  const statsCards = [
    {
      title: t.totalEmployees,
      value: stats?.total_employees || 0,
      icon: Users,
      color: 'from-blue-500 to-blue-600',
      change: '+5%'
    },
    {
      title: t.totalDepartments,
      value: stats?.total_departments || 0,
      icon: Building,
      color: 'from-green-500 to-green-600',
      change: '+2%'
    },
    {
      title: t.activeProjects,
      value: stats?.active_projects || 0,
      icon: Briefcase,
      color: 'from-purple-500 to-purple-600',
      change: '+12%'
    },
    {
      title: t.pendingTasks,
      value: stats?.pending_tasks || 0,
      icon: Clock,
      color: 'from-orange-500 to-orange-600',
      change: '-8%'
    },
    {
      title: t.pendingLeaveRequests,
      value: stats?.pending_leave_requests || 0,
      icon: FileText,
      color: 'from-red-500 to-red-600',
      change: '+3%'
    },
    {
      title: t.monthlyExpenses,
      value: `$${(stats?.monthly_expenses || 0).toLocaleString()}`,
      icon: DollarSign,
      color: 'from-indigo-500 to-indigo-600',
      change: '+15%'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.dashboard}</h1>
          {lastUpdated && (
            <p className="text-white/70">
              {t.lastUpdated}: {lastUpdated.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
            </p>
          )}
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={loading}
            className="glass-button"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {error && (
        <Alert className="border-red-500/50 bg-red-500/10">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="text-white">
            {t.failedToLoadData}: {error}
          </AlertDescription>
        </Alert>
      )}

      {stats && !error && (
        <Alert className="border-green-500/50 bg-green-500/10">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription className="text-white">
            {t.dataLoadedSuccessfully}
          </AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statsCards.map((card, index) => {
          const Icon = card.icon
          const isPositive = card.change.startsWith('+')
          
          return (
            <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-white/80">
                  {card.title}
                </CardTitle>
                <div className={`p-2 rounded-lg bg-gradient-to-r ${card.color}`}>
                  <Icon className="h-4 w-4 text-white" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white mb-1">
                  {card.value}
                </div>
                <div className="flex items-center text-xs">
                  {isPositive ? (
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  <span className={isPositive ? 'text-green-500' : 'text-red-500'}>
                    {card.change}
                  </span>
                  <span className="text-white/60 ml-1">from last month</span>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* System Health */}
      {stats?.system_health && (
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Server className="h-5 w-5" />
              {t.systemHealth}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-white/70">{t.cpuUsage}</span>
                  <span className="text-white">{stats.system_health.cpu_usage}%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all"
                    style={{ width: `${stats.system_health.cpu_usage}%` }}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-white/70">{t.memoryUsage}</span>
                  <span className="text-white">{stats.system_health.memory_usage}%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full transition-all"
                    style={{ width: `${stats.system_health.memory_usage}%` }}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-white/70">{t.diskUsage}</span>
                  <span className="text-white">{stats.system_health.disk_usage}%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div 
                    className="bg-purple-500 h-2 rounded-full transition-all"
                    style={{ width: `${stats.system_health.disk_usage}%` }}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
