import React, { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { toast } from 'react-hot-toast'
import {
  Plus,
  MoreVertical,
  Calendar,
  User,
  Clock,
  Flag,
  MessageSquare,
  Paperclip,
  CheckCircle,
  Circle,
  AlertTriangle
} from 'lucide-react'

interface KanbanBoardProps {
  language: 'ar' | 'en'
  userRole: string
}

interface Task {
  id: string
  title: string
  description: string
  assignee: {
    id: string
    name: string
    avatar?: string
  }
  priority: 'low' | 'medium' | 'high' | 'urgent'
  dueDate: Date
  tags: string[]
  comments: number
  attachments: number
  progress: number
  status: 'todo' | 'in_progress' | 'review' | 'done'
}

interface Column {
  id: string
  title: string
  tasks: Task[]
  color: string
  limit?: number
}

const translations = {
  ar: {
    kanbanBoard: 'لوحة المهام',
    addTask: 'إضافة مهمة',
    todo: 'للقيام',
    inProgress: 'قيد التنفيذ',
    review: 'للمراجعة',
    done: 'مكتمل',
    priority: 'الأولوية',
    dueDate: 'تاريخ الاستحقاق',
    assignee: 'المكلف',
    comments: 'تعليقات',
    attachments: 'مرفقات',
    progress: 'التقدم',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    urgent: 'عاجل',
    overdue: 'متأخر',
    today: 'اليوم',
    tomorrow: 'غداً',
    thisWeek: 'هذا الأسبوع',
    addColumn: 'إضافة عمود',
    taskLimit: 'حد المهام'
  },
  en: {
    kanbanBoard: 'Kanban Board',
    addTask: 'Add Task',
    todo: 'To Do',
    inProgress: 'In Progress',
    review: 'Review',
    done: 'Done',
    priority: 'Priority',
    dueDate: 'Due Date',
    assignee: 'Assignee',
    comments: 'Comments',
    attachments: 'Attachments',
    progress: 'Progress',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    urgent: 'Urgent',
    overdue: 'Overdue',
    today: 'Today',
    tomorrow: 'Tomorrow',
    thisWeek: 'This Week',
    addColumn: 'Add Column',
    taskLimit: 'Task Limit'
  }
}

export default function KanbanBoard({ language, userRole }: KanbanBoardProps) {
  const [columns, setColumns] = useState<Column[]>([])
  const [draggedTask, setDraggedTask] = useState<Task | null>(null)
  const [loading, setLoading] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Initialize columns and tasks
  useEffect(() => {
    const mockTasks: Task[] = [
      {
        id: '1',
        title: language === 'ar' ? 'تطوير واجهة المستخدم' : 'Develop User Interface',
        description: language === 'ar' ? 'تصميم وتطوير واجهة المستخدم الجديدة' : 'Design and develop new user interface',
        assignee: { id: '1', name: 'أحمد محمد' },
        priority: 'high',
        dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
        tags: ['Frontend', 'UI/UX'],
        comments: 3,
        attachments: 2,
        progress: 75,
        status: 'in_progress'
      },
      {
        id: '2',
        title: language === 'ar' ? 'مراجعة الكود' : 'Code Review',
        description: language === 'ar' ? 'مراجعة كود الميزة الجديدة' : 'Review new feature code',
        assignee: { id: '2', name: 'سارة أحمد' },
        priority: 'medium',
        dueDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000),
        tags: ['Review', 'Quality'],
        comments: 1,
        attachments: 0,
        progress: 0,
        status: 'todo'
      },
      {
        id: '3',
        title: language === 'ar' ? 'اختبار التطبيق' : 'Application Testing',
        description: language === 'ar' ? 'اختبار شامل للتطبيق' : 'Comprehensive application testing',
        assignee: { id: '3', name: 'محمد علي' },
        priority: 'urgent',
        dueDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        tags: ['Testing', 'QA'],
        comments: 5,
        attachments: 1,
        progress: 90,
        status: 'review'
      }
    ]

    const initialColumns: Column[] = [
      {
        id: 'todo',
        title: t.todo,
        tasks: mockTasks.filter(task => task.status === 'todo'),
        color: 'from-gray-500 to-gray-600',
        limit: 5
      },
      {
        id: 'in_progress',
        title: t.inProgress,
        tasks: mockTasks.filter(task => task.status === 'in_progress'),
        color: 'from-blue-500 to-blue-600',
        limit: 3
      },
      {
        id: 'review',
        title: t.review,
        tasks: mockTasks.filter(task => task.status === 'review'),
        color: 'from-yellow-500 to-yellow-600',
        limit: 2
      },
      {
        id: 'done',
        title: t.done,
        tasks: mockTasks.filter(task => task.status === 'done'),
        color: 'from-green-500 to-green-600'
      }
    ]

    setColumns(initialColumns)
  }, [language, t])

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-green-500'
      case 'medium': return 'bg-yellow-500'
      case 'high': return 'bg-orange-500'
      case 'urgent': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent': return <AlertTriangle className="h-3 w-3" />
      case 'high': return <Flag className="h-3 w-3" />
      default: return <Circle className="h-3 w-3" />
    }
  }

  const getDueDateStatus = (dueDate: Date) => {
    const now = new Date()
    const diffTime = dueDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays < 0) return { status: 'overdue', color: 'text-red-400' }
    if (diffDays === 0) return { status: 'today', color: 'text-yellow-400' }
    if (diffDays === 1) return { status: 'tomorrow', color: 'text-orange-400' }
    if (diffDays <= 7) return { status: 'thisWeek', color: 'text-blue-400' }
    return { status: '', color: 'text-white/60' }
  }

  const handleDragStart = (task: Task) => {
    setDraggedTask(task)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent, columnId: string) => {
    e.preventDefault()
    if (!draggedTask) return

    setColumns(prev => prev.map(column => {
      if (column.id === columnId) {
        return {
          ...column,
          tasks: [...column.tasks, { ...draggedTask, status: columnId as any }]
        }
      } else {
        return {
          ...column,
          tasks: column.tasks.filter(task => task.id !== draggedTask.id)
        }
      }
    }))

    setDraggedTask(null)

    // Show success message
    toast.success(
      language === 'ar'
        ? `تم نقل المهمة "${draggedTask.title}" بنجاح`
        : `Task "${draggedTask.title}" moved successfully`
    )
  }

  // Add new task handler
  const handleAddTask = async (columnId?: string) => {
    try {
      setLoading(true)

      const newTask: Task = {
        id: `task-${Date.now()}`,
        title: language === 'ar' ? 'مهمة جديدة' : 'New Task',
        description: language === 'ar' ? 'وصف المهمة الجديدة' : 'New task description',
        assignee: {
          id: 'current-user',
          name: language === 'ar' ? 'المستخدم الحالي' : 'Current User'
        },
        priority: 'medium',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        tags: [],
        comments: 0,
        attachments: 0,
        progress: 0,
        status: (columnId as any) || 'todo'
      }

      setColumns(prev => prev.map(column => {
        if (column.id === (columnId || 'todo')) {
          return {
            ...column,
            tasks: [...column.tasks, newTask]
          }
        }
        return column
      }))

      toast.success(
        language === 'ar'
          ? 'تم إضافة المهمة الجديدة بنجاح'
          : 'New task added successfully'
      )
    } catch (error) {
      console.error('Add task error:', error)
      toast.error(
        language === 'ar'
          ? 'فشل في إضافة المهمة'
          : 'Failed to add task'
      )
    } finally {
      setLoading(false)
    }
  }

  // Add new column handler
  const handleAddColumn = async () => {
    try {
      setLoading(true)

      const newColumn: Column = {
        id: `column-${Date.now()}`,
        title: language === 'ar' ? 'عمود جديد' : 'New Column',
        tasks: [],
        color: 'from-purple-500 to-purple-600',
        limit: 5
      }

      setColumns(prev => [...prev, newColumn])

      toast.success(
        language === 'ar'
          ? 'تم إضافة العمود الجديد بنجاح'
          : 'New column added successfully'
      )
    } catch (error) {
      console.error('Add column error:', error)
      toast.error(
        language === 'ar'
          ? 'فشل في إضافة العمود'
          : 'Failed to add column'
      )
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="glass-card border-white/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-white text-xl flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            {t.kanbanBoard}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              className="glass-button"
              onClick={() => handleAddTask()}
              disabled={loading}
            >
              <Plus className="h-4 w-4 mr-2" />
              {t.addTask}
            </Button>
            <Button
              variant="outline"
              className="glass-button"
              onClick={handleAddColumn}
              disabled={loading}
            >
              <Plus className="h-4 w-4 mr-2" />
              {t.addColumn}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {columns.map((column) => (
            <div
              key={column.id}
              className="space-y-4"
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, column.id)}
            >
              {/* Column Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${column.color}`}></div>
                  <h3 className="text-white font-medium">{column.title}</h3>
                  <Badge variant="outline" className="text-xs">
                    {column.tasks.length}
                    {column.limit && `/${column.limit}`}
                  </Badge>
                </div>
                <Button variant="ghost" size="sm" className="glass-button p-1">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </div>

              {/* Tasks */}
              <div className="space-y-3 min-h-[200px]">
                {column.tasks.map((task) => {
                  const dueDateStatus = getDueDateStatus(task.dueDate)

                  return (
                    <div
                      key={task.id}
                      draggable
                      onDragStart={() => handleDragStart(task)}
                      className="p-4 glass-card border-white/10 hover:border-white/30 transition-all duration-300 cursor-move group"
                    >
                      {/* Task Header */}
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${getPriorityColor(task.priority)}`}></div>
                          {getPriorityIcon(task.priority)}
                        </div>
                        <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity p-1">
                          <MoreVertical className="h-3 w-3" />
                        </Button>
                      </div>

                      {/* Task Content */}
                      <h4 className="text-white font-medium text-sm mb-2 line-clamp-2">
                        {task.title}
                      </h4>
                      <p className="text-white/70 text-xs mb-3 line-clamp-2">
                        {task.description}
                      </p>

                      {/* Progress Bar */}
                      {task.progress > 0 && (
                        <div className="mb-3">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-white/60 text-xs">{t.progress}</span>
                            <span className="text-white text-xs">{task.progress}%</span>
                          </div>
                          <div className="w-full bg-white/20 rounded-full h-1">
                            <div
                              className={`bg-gradient-to-r ${column.color} h-1 rounded-full transition-all duration-300`}
                              style={{ width: `${task.progress}%` }}
                            ></div>
                          </div>
                        </div>
                      )}

                      {/* Tags */}
                      {task.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-3">
                          {task.tags.map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}

                      {/* Task Footer */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1">
                            <MessageSquare className="h-3 w-3 text-white/60" />
                            <span className="text-xs text-white/60">{task.comments}</span>
                          </div>
                          {task.attachments > 0 && (
                            <div className="flex items-center gap-1">
                              <Paperclip className="h-3 w-3 text-white/60" />
                              <span className="text-xs text-white/60">{task.attachments}</span>
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3 text-white/60" />
                            <span className={`text-xs ${dueDateStatus.color}`}>
                              {dueDateStatus.status && t[dueDateStatus.status as keyof typeof t]}
                            </span>
                          </div>
                          <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs font-medium">
                              {(task.assignee?.name || '').charAt(0) || '?'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}

                {/* Add Task Button */}
                <Button
                  variant="ghost"
                  className="w-full glass-card border-white/10 border-dashed hover:border-white/30 transition-all duration-300 h-12"
                  onClick={() => handleAddTask(column.id)}
                  disabled={loading}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  {t.addTask}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
