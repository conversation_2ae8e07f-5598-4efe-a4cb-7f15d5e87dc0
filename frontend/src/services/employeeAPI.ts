/**
 * Employee Management API Services
 */

import { apiClient, ApiResponse } from './api'

// Backend API Response Types
export interface EmployeeAPIResponse {
  id: number
  user: {
    id: number
    username: string
    email: string
    first_name: string
    last_name: string
    is_active: boolean
    date_joined: string
  }
  employee_id: string
  department: number
  department_name: string
  department_name_ar: string
  position: string
  position_ar: string
  phone: string
  gender: 'M' | 'F'
  hire_date: string
  salary?: number
  employment_status: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERN' | 'CONSULTANT'
  is_active: boolean
  created_at: string
  updated_at: string
}

// Frontend Employee Interface (unified)
export interface Employee {
  id: number
  firstName: string
  firstNameAr: string
  lastName: string
  lastNameAr: string
  name: string
  nameAr: string
  email: string
  employeeId: string
  position: string
  positionAr: string
  department: string
  departmentAr: string
  departmentId: number
  salary: number
  hireDate: string
  joinDate: string
  phone: string
  gender: 'M' | 'F'
  isActive: boolean
  status: 'active' | 'inactive' | 'terminated' | 'on-leave'
  performance: 'excellent' | 'good' | 'average' | 'needsImprovement'
  username: string
  userId: number
  createdAt: string
  updatedAt: string
}

// Data transformer function
export function transformEmployeeData(apiData: EmployeeAPIResponse): Employee {
  return {
    id: apiData.id,
    firstName: apiData.user.first_name,
    firstNameAr: apiData.user.first_name, // Use same for now, can be enhanced later
    lastName: apiData.user.last_name,
    lastNameAr: apiData.user.last_name, // Use same for now, can be enhanced later
    name: `${apiData.user.first_name} ${apiData.user.last_name}`.trim(),
    nameAr: `${apiData.user.first_name} ${apiData.user.last_name}`.trim(), // Use same for now
    email: apiData.user.email,
    employeeId: apiData.employee_id,
    position: apiData.position,
    positionAr: apiData.position_ar,
    department: apiData.department_name,
    departmentAr: apiData.department_name_ar,
    departmentId: apiData.department,
    salary: Number(apiData.salary) || 0,
    hireDate: apiData.hire_date,
    joinDate: apiData.hire_date,
    phone: apiData.phone || '',
    gender: apiData.gender,
    isActive: apiData.is_active,
    status: apiData.is_active ? 'active' : 'inactive',
    performance: 'average', // Default value, can be enhanced later
    username: apiData.user.username,
    userId: apiData.user.id,
    createdAt: apiData.created_at,
    updatedAt: apiData.updated_at
  }
}

export interface CreateEmployeeData {
  user: {
    username: string
    email: string
    first_name: string
    last_name: string
    password: string
  }
  employee_id: string
  department: number
  position: string
  position_ar: string
  phone?: string
  gender: 'M' | 'F'
  hire_date: string
  salary?: number
  employment_status: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERN' | 'CONSULTANT'
}

export interface UpdateEmployeeData {
  department?: number
  position?: string
  position_ar?: string
  phone?: string
  salary?: number
  employment_status?: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERN' | 'CONSULTANT'
  is_active?: boolean
}

export interface EmployeeFilters {
  department?: number
  employment_status?: string
  is_active?: boolean
  search?: string
}

// Department Types
export interface Department {
  id: number
  name: string
  name_ar: string
  description: string
  description_ar: string
  employee_count: number
  created_at: string
  updated_at: string
}

export interface CreateDepartmentData {
  name: string
  name_ar: string
  description?: string
  description_ar?: string
  budget_amount?: number
  location?: string
  phone?: string
  email?: string
}

// Employee API
export const employeeAPI = {
  // Get all employees
  getAll: async (filters?: EmployeeFilters): Promise<Employee[]> => {
    const response = await apiClient.get<{
      count: number
      next: string | null
      previous: string | null
      results: EmployeeAPIResponse[]
    }>('/employees/', { params: filters })

    // Handle paginated response
    const employees = response.data.results || response.data
    return Array.isArray(employees)
      ? employees.map(transformEmployeeData)
      : []
  },

  // Get employee by ID
  getById: async (id: number): Promise<Employee> => {
    const response = await apiClient.get<EmployeeAPIResponse>(`/employees/${id}/`)
    return transformEmployeeData(response.data)
  },

  // Create new employee
  create: async (employeeData: CreateEmployeeData): Promise<Employee> => {
    const response = await apiClient.post<EmployeeAPIResponse>('/employees/', employeeData)
    return transformEmployeeData(response.data)
  },

  // Update employee
  update: async (id: number, employeeData: UpdateEmployeeData): Promise<Employee> => {
    const response = await apiClient.patch<EmployeeAPIResponse>(`/employees/${id}/`, employeeData)
    return transformEmployeeData(response.data)
  },

  // Delete employee
  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/employees/${id}/`)
  },

  // Get employee statistics
  getStats: async (): Promise<{
    total: number
    active: number
    by_department: Record<string, number>
    by_status: Record<string, number>
  }> => {
    const response = await apiClient.get('/employees/stats/')
    return response.data
  },
}

// Department API
export const departmentAPI = {
  // Get all departments
  getAll: async (): Promise<Department[]> => {
    const response = await apiClient.get<Department[]>('/departments/')
    return response.data
  },

  // Get department by ID
  getById: async (id: number): Promise<Department> => {
    const response = await apiClient.get<Department>(`/departments/${id}/`)
    return response.data
  },

  // Create new department
  create: async (departmentData: CreateDepartmentData): Promise<Department> => {
    const response = await apiClient.post<Department>('/departments/', departmentData)
    return response.data
  },

  // Update department
  update: async (id: number, departmentData: Partial<CreateDepartmentData>): Promise<Department> => {
    const response = await apiClient.patch<Department>(`/departments/${id}/`, departmentData)
    return response.data
  },

  // Delete department
  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/departments/${id}/`)
  },

  // Get department employees
  getEmployees: async (id: number): Promise<Employee[]> => {
    const response = await apiClient.get<Employee[]>(`/departments/${id}/employees/`)
    return response.data
  },
}

// Activity Types
export interface Activity {
  id: number
  user: number
  user_name: string
  activity_type: string
  description: string
  description_ar: string
  timestamp: string
  ip_address?: string
}

// Activity API
export const activityAPI = {
  // Get all activities
  getAll: async (limit?: number): Promise<Activity[]> => {
    const params = limit ? { limit } : {}
    const response = await apiClient.get<Activity[]>('/activities/', { params })
    return response.data
  },

  // Get activities by user
  getByUser: async (userId: number): Promise<Activity[]> => {
    const response = await apiClient.get<Activity[]>('/activities/', { 
      params: { user: userId } 
    })
    return response.data
  },

  // Get recent activities
  getRecent: async (limit: number = 10): Promise<Activity[]> => {
    const response = await apiClient.get<Activity[]>('/activities/', { 
      params: { limit, ordering: '-timestamp' } 
    })
    return response.data
  },
}

export default {
  employeeAPI,
  departmentAPI,
  activityAPI,
}
