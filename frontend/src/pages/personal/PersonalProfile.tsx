/**
 * Personal Profile Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Briefcase,
  Edit,
  Eye,
  Trash2,
  Building,
  Users,
  Star,
  Award,
  Clock,
  Shield
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { personalProfileService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface PersonalProfileProps {
  language: 'ar' | 'en'
}

interface UserProfile {
  id: number
  firstName: string
  firstNameAr: string
  lastName: string
  lastNameAr: string
  email: string
  phone: string
  position: string
  positionAr: string
  department: string
  departmentAr: string
  manager: string
  managerAr: string
  joinDate: string
  location: string
  locationAr: string
  avatar?: string
  bio: string
  bioAr: string
  skills: string
  skillsAr: string
  workHours: string
  employeeId: string
  status: 'active' | 'inactive' | 'on_leave'
  salary?: number
  emergencyContact?: string
  nationalId?: string
}

const translations = {
  ar: {
    personalProfile: 'ملفي الشخصي',
    addProfile: 'إضافة ملف شخصي',
    editProfile: 'تعديل الملف الشخصي',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا الملف الشخصي؟',
    searchPlaceholder: 'البحث في الملفات الشخصية...',
    firstName: 'الاسم الأول',
    lastName: 'اسم العائلة',
    email: 'البريد الإلكتروني',
    phone: 'رقم الهاتف',
    position: 'المنصب',
    department: 'القسم',
    manager: 'المدير المباشر',
    joinDate: 'تاريخ الانضمام',
    location: 'الموقع',
    bio: 'نبذة شخصية',
    skills: 'المهارات',
    workHours: 'ساعات العمل',
    employeeId: 'رقم الموظف',
    status: 'الحالة',
    salary: 'الراتب',
    emergencyContact: 'جهة الاتصال الطارئة',
    nationalId: 'رقم الهوية الوطنية',
    active: 'نشط',
    inactive: 'غير نشط',
    on_leave: 'في إجازة'
  },
  en: {
    personalProfile: 'Personal Profiles',
    addProfile: 'Add Profile',
    editProfile: 'Edit Profile',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this profile?',
    searchPlaceholder: 'Search profiles...',
    firstName: 'First Name',
    lastName: 'Last Name',
    email: 'Email',
    phone: 'Phone',
    position: 'Position',
    department: 'Department',
    manager: 'Manager',
    joinDate: 'Join Date',
    location: 'Location',
    bio: 'Bio',
    skills: 'Skills',
    workHours: 'Work Hours',
    employeeId: 'Employee ID',
    status: 'Status',
    salary: 'Salary',
    emergencyContact: 'Emergency Contact',
    nationalId: 'National ID',
    active: 'Active',
    inactive: 'Inactive',
    on_leave: 'On Leave'
  }
}

export default function PersonalProfile({ language }: PersonalProfileProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: profiles,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<UserProfile>({
    service: personalProfileService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'on_leave':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const calculateYearsOfService = (joinDate: string) => {
    const join = new Date(joinDate)
    const now = new Date()
    const years = now.getFullYear() - join.getFullYear()
    return years
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<UserProfile>[] = [
    {
      key: 'name',
      label: t.firstName + ' & ' + t.lastName,
      sortable: true,
      render: (item: UserProfile) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
            {item.avatar || (language === 'ar' ?
              `${(item.firstNameAr || '').charAt(0)}${(item.lastNameAr || '').charAt(0)}` :
              `${(item.firstName || '').charAt(0)}${(item.lastName || '').charAt(0)}`
            ) || '??'}
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ?
                `${item.firstNameAr} ${item.lastNameAr}` :
                `${item.firstName} ${item.lastName}`
              }
            </div>
            <div className="text-sm text-white/60 flex items-center gap-1">
              <Mail className="h-3 w-3" />
              {item.email}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'position',
      label: t.position,
      sortable: true,
      render: (item: UserProfile) => (
        <div className="flex items-center gap-1">
          <Briefcase className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.positionAr : item.position}
          </span>
        </div>
      )
    },
    {
      key: 'department',
      label: t.department,
      sortable: true,
      render: (item: UserProfile) => (
        <div className="flex items-center gap-1">
          <Building className="h-3 w-3 text-green-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.departmentAr : item.department}
          </span>
        </div>
      )
    },
    {
      key: 'phone',
      label: t.phone,
      render: (item: UserProfile) => (
        <div className="flex items-center gap-1">
          <Phone className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.phone}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: UserProfile) => (
        <Badge className={getStatusColor(item.status)}>
          {t[item.status as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'joinDate',
      label: t.joinDate,
      sortable: true,
      render: (item: UserProfile) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.joinDate}</span>
          <span className="text-white/50 text-xs">
            ({calculateYearsOfService(item.joinDate)} years)
          </span>
        </div>
      )
    },
    {
      key: 'location',
      label: t.location,
      render: (item: UserProfile) => (
        <div className="flex items-center gap-1">
          <MapPin className="h-3 w-3 text-red-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.locationAr : item.location}
          </span>
        </div>
      )
    },
    {
      key: 'employeeId',
      label: t.employeeId,
      render: (item: UserProfile) => (
        <div className="flex items-center gap-1">
          <Shield className="h-3 w-3 text-yellow-400" />
          <span className="text-white font-medium">{item.employeeId}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: UserProfile) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: UserProfile) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: UserProfile) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.on_leave, value: 'on_leave' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'firstName',
      label: t.firstName,
      type: 'text',
      required: true
    },
    {
      name: 'firstNameAr',
      label: t.firstName + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'lastName',
      label: t.lastName,
      type: 'text',
      required: true
    },
    {
      name: 'lastNameAr',
      label: t.lastName + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'email',
      label: t.email,
      type: 'email',
      required: true
    },
    {
      name: 'phone',
      label: t.phone,
      type: 'tel',
      required: true
    },
    {
      name: 'position',
      label: t.position,
      type: 'text',
      required: true
    },
    {
      name: 'positionAr',
      label: t.position + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'department',
      label: t.department,
      type: 'text',
      required: true
    },
    {
      name: 'departmentAr',
      label: t.department + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'manager',
      label: t.manager,
      type: 'text',
      required: true
    },
    {
      name: 'managerAr',
      label: t.manager + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'joinDate',
      label: t.joinDate,
      type: 'date',
      required: true
    },
    {
      name: 'location',
      label: t.location,
      type: 'text',
      required: true
    },
    {
      name: 'locationAr',
      label: t.location + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'bio',
      label: t.bio,
      type: 'textarea',
      required: true
    },
    {
      name: 'bioAr',
      label: t.bio + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'skills',
      label: t.skills,
      type: 'textarea',
      placeholder: 'One skill per line'
    },
    {
      name: 'skillsAr',
      label: t.skills + ' (عربي)',
      type: 'textarea',
      placeholder: 'مهارة واحدة في كل سطر'
    },
    {
      name: 'workHours',
      label: t.workHours,
      type: 'text',
      required: true
    },
    {
      name: 'employeeId',
      label: t.employeeId,
      type: 'text',
      required: true
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.on_leave, value: 'on_leave' }
      ]
    },
    {
      name: 'salary',
      label: t.salary,
      type: 'number',
      min: 0
    },
    {
      name: 'emergencyContact',
      label: t.emergencyContact,
      type: 'tel'
    },
    {
      name: 'nationalId',
      label: t.nationalId,
      type: 'text'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<UserProfile>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('personal-profiles')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.personalProfile}
        data={profiles}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addProfile : modalMode === 'edit' ? t.editProfile : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
