import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  MessageCircle, 
  Users, 
  Clock, 
  CheckCircle,
  AlertCircle,
  User,
  Send,
  Phone,
  Mail,
  Calendar,
  MoreVertical,
  RefreshCw,
  Search,
  Filter
} from 'lucide-react';

interface LiveChatSession {
  id: number;
  session_id: string;
  customer: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
  } | null;
  agent: {
    id: number;
    first_name: string;
    last_name: string;
  } | null;
  subject: string;
  status: 'waiting' | 'active' | 'ended';
  started_at: string;
  ended_at: string | null;
  last_activity_at: string | null;
  unread_messages?: number;
}

interface ChatMessage {
  id: number;
  content: string;
  sender: {
    id: number;
    first_name: string;
    last_name: string;
  } | null;
  message_type: 'text' | 'system' | 'file';
  created_at: string;
  is_read: boolean;
}

interface LiveChatDashboardProps {
  language: 'ar' | 'en';
}

const LiveChatDashboard: React.FC<LiveChatDashboardProps> = ({ language }) => {
  const [sessions, setSessions] = useState<LiveChatSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<LiveChatSession | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'waiting' | 'active' | 'ended'>('all');
  const [stats, setStats] = useState({
    total_sessions: 0,
    waiting_sessions: 0,
    active_sessions: 0,
    my_active_sessions: 0
  });

  useEffect(() => {
    fetchSessions();
    fetchStats();
    
    // Set up polling for real-time updates
    const interval = setInterval(() => {
      fetchSessions();
      if (selectedSession) {
        fetchMessages(selectedSession.id);
      }
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (selectedSession) {
      fetchMessages(selectedSession.id);
    }
  }, [selectedSession]);

  const fetchSessions = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
      const response = await fetch(`${API_BASE_URL}/customer-service/live-chat-sessions/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSessions(data.results || []);
      }
    } catch (error) {
      console.error('Error fetching sessions:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';

      // Fetch waiting sessions
      const waitingResponse = await fetch(`${API_BASE_URL}/customer-service/live-chat-sessions/waiting_sessions/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      // Fetch my active sessions
      const activeResponse = await fetch(`${API_BASE_URL}/customer-service/live-chat-sessions/my_active_sessions/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      if (waitingResponse.ok && activeResponse.ok) {
        const waitingData = await waitingResponse.json();
        const activeData = await activeResponse.json();
        
        setStats(prev => ({
          ...prev,
          waiting_sessions: waitingData.total_waiting || 0,
          my_active_sessions: activeData.total_active || 0
        }));
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchMessages = async (sessionId: number) => {
    try {
      const token = localStorage.getItem('access_token');
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
      const response = await fetch(`${API_BASE_URL}/customer-service/live-chat-sessions/${sessionId}/messages/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const data = await response.json();
        setMessages(data.messages || []);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedSession || sendingMessage) return;

    setSendingMessage(true);
    const messageContent = newMessage.trim();
    setNewMessage('');

    try {
      const token = localStorage.getItem('access_token');
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
      const response = await fetch(`${API_BASE_URL}/customer-service/live-chat-sessions/${selectedSession.id}/send_message/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: messageContent,
          message_type: 'text'
        })
      });

      if (response.ok) {
        await fetchMessages(selectedSession.id);
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setNewMessage(messageContent); // Restore message on error
    } finally {
      setSendingMessage(false);
    }
  };

  const assignToMe = async (sessionId: number) => {
    try {
      const token = localStorage.getItem('access_token');
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
      const response = await fetch(`${API_BASE_URL}/customer-service/live-chat-sessions/${sessionId}/assign_agent/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
      });

      if (response.ok) {
        await fetchSessions();
        await fetchStats();
      }
    } catch (error) {
      console.error('Error assigning session:', error);
    }
  };

  const endSession = async (sessionId: number) => {
    try {
      const token = localStorage.getItem('access_token');
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
      const response = await fetch(`${API_BASE_URL}/customer-service/live-chat-sessions/${sessionId}/end_session/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason: 'Session ended by agent'
        })
      });

      if (response.ok) {
        await fetchSessions();
        await fetchStats();
        if (selectedSession?.id === sessionId) {
          setSelectedSession(null);
          setMessages([]);
        }
      }
    } catch (error) {
      console.error('Error ending session:', error);
    }
  };

  const filteredSessions = sessions.filter(session => {
    const matchesSearch = !searchQuery || 
      session.session_id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      session.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (session.customer?.email || '').toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || session.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'waiting': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      case 'active': return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'ended': return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
      default: return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
    }
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      waiting: language === 'ar' ? 'في الانتظار' : 'Waiting',
      active: language === 'ar' ? 'نشط' : 'Active',
      ended: language === 'ar' ? 'منتهي' : 'Ended'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
      hour: '2-digit',
      minute: '2-digit',
      day: '2-digit',
      month: '2-digit'
    });
  };

  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return language === 'ar' ? 'الآن' : 'now';
    if (diffMins < 60) return `${diffMins}${language === 'ar' ? ' د' : 'm'}`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}${language === 'ar' ? ' س' : 'h'}`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}${language === 'ar' ? ' ي' : 'd'}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              {language === 'ar' ? 'لوحة الدردشة المباشرة' : 'Live Chat Dashboard'}
            </h1>
            <p className="text-white/70">
              {language === 'ar' ? 'إدارة جلسات الدردشة المباشرة مع العملاء' : 'Manage live chat sessions with customers'}
            </p>
          </div>
          <Button
            onClick={() => { fetchSessions(); fetchStats(); }}
            className="bg-white/10 hover:bg-white/20 text-white border-white/20"
            variant="outline"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <MessageCircle className="h-6 w-6 text-blue-400" />
                </div>
                <div>
                  <p className="text-white/70 text-sm">
                    {language === 'ar' ? 'إجمالي الجلسات' : 'Total Sessions'}
                  </p>
                  <p className="text-2xl font-bold text-white">{sessions.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                  <Clock className="h-6 w-6 text-yellow-400" />
                </div>
                <div>
                  <p className="text-white/70 text-sm">
                    {language === 'ar' ? 'في الانتظار' : 'Waiting'}
                  </p>
                  <p className="text-2xl font-bold text-white">{stats.waiting_sessions}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-green-400" />
                </div>
                <div>
                  <p className="text-white/70 text-sm">
                    {language === 'ar' ? 'جلساتي النشطة' : 'My Active'}
                  </p>
                  <p className="text-2xl font-bold text-white">{stats.my_active_sessions}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                  <Users className="h-6 w-6 text-purple-400" />
                </div>
                <div>
                  <p className="text-white/70 text-sm">
                    {language === 'ar' ? 'الجلسات النشطة' : 'Active Sessions'}
                  </p>
                  <p className="text-2xl font-bold text-white">
                    {sessions.filter(s => s.status === 'active').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Sessions List */}
          <div className="lg:col-span-1">
            <Card className="glass-card border-white/20 h-[600px] flex flex-col">
              <CardHeader className="border-b border-white/10">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white">
                    {language === 'ar' ? 'جلسات الدردشة' : 'Chat Sessions'}
                  </CardTitle>
                </div>
                
                {/* Search and Filter */}
                <div className="space-y-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/40" />
                    <Input
                      placeholder={language === 'ar' ? 'البحث...' : 'Search...'}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 bg-white/5 border-white/20 text-white placeholder:text-white/40"
                    />
                  </div>
                  
                  <Tabs value={statusFilter} onValueChange={(value) => setStatusFilter(value as any)}>
                    <TabsList className="grid w-full grid-cols-4 bg-white/5">
                      <TabsTrigger value="all" className="text-xs">
                        {language === 'ar' ? 'الكل' : 'All'}
                      </TabsTrigger>
                      <TabsTrigger value="waiting" className="text-xs">
                        {language === 'ar' ? 'انتظار' : 'Waiting'}
                      </TabsTrigger>
                      <TabsTrigger value="active" className="text-xs">
                        {language === 'ar' ? 'نشط' : 'Active'}
                      </TabsTrigger>
                      <TabsTrigger value="ended" className="text-xs">
                        {language === 'ar' ? 'منتهي' : 'Ended'}
                      </TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
              </CardHeader>
              
              <CardContent className="flex-1 overflow-y-auto p-0">
                {loading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-white/60">
                      {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
                    </div>
                  </div>
                ) : filteredSessions.length === 0 ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-white/60 text-center">
                      <MessageCircle className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>{language === 'ar' ? 'لا توجد جلسات' : 'No sessions found'}</p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-1 p-2">
                    {filteredSessions.map((session) => (
                      <div
                        key={session.id}
                        onClick={() => setSelectedSession(session)}
                        className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                          selectedSession?.id === session.id
                            ? 'bg-blue-500/20 border border-blue-500/30'
                            : 'bg-white/5 hover:bg-white/10'
                        }`}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1 min-w-0">
                            <h4 className="text-white text-sm font-medium truncate">
                              {session.customer 
                                ? `${session.customer.first_name} ${session.customer.last_name}`
                                : session.session_id
                              }
                            </h4>
                            <p className="text-white/60 text-xs truncate">
                              {session.subject}
                            </p>
                          </div>
                          <Badge className={`${getStatusColor(session.status)} text-xs`}>
                            {getStatusText(session.status)}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center justify-between text-xs text-white/60">
                          <span>{formatRelativeTime(session.started_at)}</span>
                          {session.status === 'waiting' && (
                            <Button
                              onClick={(e) => {
                                e.stopPropagation();
                                assignToMe(session.id);
                              }}
                              size="sm"
                              className="h-6 px-2 bg-blue-500 hover:bg-blue-600 text-white text-xs"
                            >
                              {language === 'ar' ? 'تولي' : 'Take'}
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Chat Interface */}
          <div className="lg:col-span-2">
            {selectedSession ? (
              <Card className="glass-card border-white/20 h-[600px] flex flex-col">
                {/* Chat Header */}
                <CardHeader className="border-b border-white/10">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-white font-medium">
                          {selectedSession.customer 
                            ? `${selectedSession.customer.first_name} ${selectedSession.customer.last_name}`
                            : 'Unknown Customer'
                          }
                        </h3>
                        <p className="text-white/60 text-sm">{selectedSession.subject}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(selectedSession.status)}>
                        {getStatusText(selectedSession.status)}
                      </Badge>
                      {selectedSession.status === 'active' && (
                        <Button
                          onClick={() => endSession(selectedSession.id)}
                          size="sm"
                          variant="outline"
                          className="text-red-400 border-red-400 hover:bg-red-400/10"
                        >
                          {language === 'ar' ? 'إنهاء' : 'End'}
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>

                {/* Messages */}
                <CardContent className="flex-1 overflow-y-auto p-4 space-y-3">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex gap-2 ${
                        message.sender ? 'justify-start' : 'justify-center'
                      }`}
                    >
                      {message.sender && (
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                          <User className="h-3 w-3 text-white" />
                        </div>
                      )}

                      <div className={`max-w-[80%] ${!message.sender ? 'text-center' : ''}`}>
                        <div
                          className={`p-2 rounded-lg text-sm ${
                            !message.sender
                              ? 'bg-orange-500/20 text-orange-300 border border-orange-500/30 inline-block'
                              : 'bg-white/10 text-white'
                          }`}
                        >
                          <p>{message.content}</p>
                        </div>

                        {message.sender && (
                          <div className="text-xs text-white/60 mt-1">
                            {message.sender.first_name} {message.sender.last_name} • {formatTime(message.created_at)}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>

                {/* Message Input */}
                {selectedSession.status === 'active' && (
                  <div className="border-t border-white/10 p-4">
                    <div className="flex gap-2">
                      <Input
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                        placeholder={language === 'ar' ? 'اكتب رسالتك...' : 'Type your message...'}
                        className="flex-1 bg-white/5 border-white/20 text-white placeholder:text-white/40"
                        disabled={sendingMessage}
                      />
                      <Button
                        onClick={sendMessage}
                        disabled={!newMessage.trim() || sendingMessage}
                        className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </Card>
            ) : (
              <Card className="glass-card border-white/20 h-[600px] flex items-center justify-center">
                <div className="text-center text-white/60">
                  <MessageCircle className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">
                    {language === 'ar' ? 'اختر جلسة دردشة' : 'Select a Chat Session'}
                  </h3>
                  <p>
                    {language === 'ar' 
                      ? 'اختر جلسة من القائمة لبدء المحادثة'
                      : 'Choose a session from the list to start chatting'
                    }
                  </p>
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiveChatDashboard;
