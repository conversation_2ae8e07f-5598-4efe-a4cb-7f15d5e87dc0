import React, { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import { 
  Users, 
  TrendingUp, 
  DollarSign, 
  Activity, 
  BarChart3, 
  Settings, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  RefreshCw,
  UserCheck,
  Building,
  Briefcase,
  Calendar,
  FileText,
  Target,
  Zap,
  Globe,
  Database,
  Cpu,
  HardDrive,
  Network,
  Server,
  Receipt,
  Search,
  Filter,
  MoreHorizontal
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'
import type { RootState, AppDispatch } from '../../store'
import { fetchDashboardLayout } from '../../store/slices/dashboardSlice'
import { dashboardAPI, DashboardStats } from '../../services/api'
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts'

interface AdminDashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    welcome: 'مرحباً',
    systemOverview: 'نظرة عامة على النظام',
    userManagement: 'إدارة المستخدمين',
    systemHealth: 'صحة النظام',
    securityStatus: 'حالة الأمان',
    performanceMetrics: 'مقاييس الأداء',
    recentActivities: 'الأنشطة الأخيرة',
    quickActions: 'الإجراءات السريعة',
    systemStats: 'إحصائيات النظام',
    userStats: 'إحصائيات المستخدمين',
    financialOverview: 'نظرة عامة مالية',
    operationalMetrics: 'المقاييس التشغيلية',
    totalUsers: 'إجمالي المستخدمين',
    activeUsers: 'المستخدمون النشطون',
    systemUptime: 'وقت تشغيل النظام',
    serverLoad: 'حمولة الخادم',
    databaseSize: 'حجم قاعدة البيانات',
    apiRequests: 'طلبات API',
    securityAlerts: 'تنبيهات الأمان',
    backupStatus: 'حالة النسخ الاحتياطي',
    refresh: 'تحديث',
    viewDetails: 'عرض التفاصيل',
    // Admin Features
    manageEmployees: 'إدارة الموظفين',
    manageDepartments: 'إدارة الأقسام',
    systemSettings: 'إعدادات النظام',
    businessIntelligence: 'ذكاء الأعمال',
    workflowAutomation: 'أتمتة سير العمل',
    reportGenerator: 'مولد التقارير',
    userManagement: 'إدارة المستخدمين',
    vendorManagement: 'إدارة الموردين',
    excellent: 'ممتاز',
    good: 'جيد',
    warning: 'تحذير',
    critical: 'حرج',
    // Metrics
    totalRevenue: 'إجمالي الإيرادات',
    totalExpenses: 'إجمالي المصروفات',
    netProfit: 'صافي الربح',
    totalProjects: 'إجمالي المشاريع',
    activeProjects: 'المشاريع النشطة',
    completedProjects: 'المشاريع المكتملة',
    totalDepartments: 'إجمالي الأقسام',
    totalEmployees: 'إجمالي الموظفين',
    newEmployees: 'الموظفون الجدد',
    pendingTasks: 'المهام المعلقة',
    employeeTurnover: 'دوران الموظفين',
    avgSalary: 'متوسط الراتب',
    budgetUtilization: 'استخدام الميزانية',
    cpuUsage: 'استخدام المعالج',
    memoryUsage: 'استخدام الذاكرة',
    diskUsage: 'استخدام القرص',
    networkTraffic: 'حركة الشبكة',
    responseTime: 'وقت الاستجابة',
    errorRate: 'معدل الأخطاء',
    securityScore: 'نقاط الأمان',
    activeThreats: 'التهديدات النشطة',
    blockedAttacks: 'الهجمات المحجوبة',
    lastBackup: 'آخر نسخة احتياطية'
  },
  en: {
    welcome: 'Welcome',
    systemOverview: 'System Overview',
    userManagement: 'User Management',
    systemHealth: 'System Health',
    securityStatus: 'Security Status',
    performanceMetrics: 'Performance Metrics',
    recentActivities: 'Recent Activities',
    quickActions: 'Quick Actions',
    systemStats: 'System Statistics',
    userStats: 'User Statistics',
    financialOverview: 'Financial Overview',
    operationalMetrics: 'Operational Metrics',
    totalUsers: 'Total Users',
    activeUsers: 'Active Users',
    systemUptime: 'System Uptime',
    serverLoad: 'Server Load',
    databaseSize: 'Database Size',
    apiRequests: 'API Requests',
    securityAlerts: 'Security Alerts',
    backupStatus: 'Backup Status',
    refresh: 'Refresh',
    viewDetails: 'View Details',
    // Admin Features
    manageEmployees: 'Manage Employees',
    manageDepartments: 'Manage Departments',
    systemSettings: 'System Settings',
    businessIntelligence: 'Business Intelligence',
    workflowAutomation: 'Workflow Automation',
    reportGenerator: 'Report Generator',
    userManagement: 'User Management',
    vendorManagement: 'Vendor Management',
    excellent: 'Excellent',
    good: 'Good',
    warning: 'Warning',
    critical: 'Critical',
    // Metrics
    totalRevenue: 'Total Revenue',
    totalExpenses: 'Total Expenses',
    netProfit: 'Net Profit',
    totalProjects: 'Total Projects',
    activeProjects: 'Active Projects',
    completedProjects: 'Completed Projects',
    totalDepartments: 'Total Departments',
    totalEmployees: 'Total Employees',
    newEmployees: 'New Employees',
    pendingTasks: 'Pending Tasks',
    employeeTurnover: 'Employee Turnover',
    avgSalary: 'Average Salary',
    budgetUtilization: 'Budget Utilization',
    cpuUsage: 'CPU Usage',
    memoryUsage: 'Memory Usage',
    diskUsage: 'Disk Usage',
    networkTraffic: 'Network Traffic',
    responseTime: 'Response Time',
    errorRate: 'Error Rate',
    securityScore: 'Security Score',
    activeThreats: 'Active Threats',
    blockedAttacks: 'Blocked Attacks',
    lastBackup: 'Last Backup'
  }
}

export default function AdminDashboard({ language }: AdminDashboardProps) {
  const dispatch = useDispatch<AppDispatch>()
  const { user } = useSelector((state: RootState) => state.auth)
  const { currentLayout, isLoading } = useSelector((state: RootState) => state.dashboard)
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Real dashboard data from API
  const [dashboardData, setDashboardData] = useState<DashboardStats | null>(null)
  const [dataLoading, setDataLoading] = useState(true)
  const [dataError, setDataError] = useState<string | null>(null)

  // Chart data for visualizations
  const [chartData, setChartData] = useState({
    employeeGrowth: [
      { month: 'يناير', employees: 120, newHires: 5 },
      { month: 'فبراير', employees: 125, newHires: 8 },
      { month: 'مارس', employees: 130, newHires: 6 },
      { month: 'أبريل', employees: 135, newHires: 7 },
      { month: 'مايو', employees: 142, newHires: 9 },
      { month: 'يونيو', employees: 150, newHires: 8 }
    ],
    departmentDistribution: [
      { name: 'تقنية المعلومات', value: 35, color: '#3b82f6' },
      { name: 'الموارد البشرية', value: 15, color: '#10b981' },
      { name: 'المالية', value: 20, color: '#f59e0b' },
      { name: 'المبيعات', value: 25, color: '#ef4444' },
      { name: 'التسويق', value: 18, color: '#8b5cf6' }
    ],
    systemHealth: [
      { metric: 'CPU', usage: 45, status: 'جيد' },
      { metric: 'الذاكرة', usage: 68, status: 'متوسط' },
      { metric: 'القرص الصلب', usage: 32, status: 'ممتاز' },
      { metric: 'الشبكة', usage: 55, status: 'جيد' }
    ]
  })

  // Load dashboard data from API
  const loadDashboardData = async () => {
    try {
      setDataLoading(true)
      setDataError(null)
      const data = await dashboardAPI.getStats()
      setDashboardData(data)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
      setDataError('فشل في تحميل بيانات لوحة التحكم')
    } finally {
      setDataLoading(false)
    }
  }

  useEffect(() => {
    if (user?.role.id === 'admin') {
      dispatch(fetchDashboardLayout('admin'))
      loadDashboardData()
    }
  }, [dispatch, user])

  // Auto-refresh data every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      if (!dataLoading) {
        loadDashboardData()
      }
    }, 300000) // 5 minutes

    return () => clearInterval(interval)
  }, [dataLoading])

  const handleRefresh = async () => {
    setRefreshing(true)
    await loadDashboardData()
    setRefreshing(false)
  }

  // Admin-level quick actions (limited compared to superadmin)
  const quickActions = [
    { title: t.manageEmployees, icon: Users, href: '/admin/employees', color: 'from-blue-500 to-blue-600' },
    { title: t.manageDepartments, icon: Building, href: '/admin/departments', color: 'from-green-500 to-green-600' },
    { title: t.businessIntelligence, icon: BarChart3, href: '/admin/business-intelligence', color: 'from-purple-500 to-purple-600' },
    { title: t.workflowAutomation, icon: Zap, href: '/admin/workflows', color: 'from-orange-500 to-orange-600' },
    { title: t.reportGenerator, icon: FileText, href: '/admin/report-generator', color: 'from-indigo-500 to-indigo-600' },
    { title: t.userManagement, icon: UserCheck, href: '/admin/users', color: 'from-cyan-500 to-cyan-600' },
    { title: t.vendorManagement, icon: Briefcase, href: '/admin/vendors', color: 'from-teal-500 to-teal-600' },
    { title: t.systemSettings, icon: Settings, href: '/admin/settings', color: 'from-gray-500 to-gray-600' }
  ]

  // Admin-level overview cards using real API data
  const overviewCards = [
    {
      title: t.totalEmployees,
      value: dashboardData?.total_employees?.toString() || '0',
      change: dataLoading ? '...' : '+5',
      trend: 'up' as const,
      icon: Users,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.totalDepartments,
      value: dashboardData?.total_departments?.toString() || '0',
      change: dataLoading ? '...' : 'مستقر',
      trend: 'stable' as const,
      icon: Building,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.activeProjects,
      value: dashboardData?.active_projects?.toString() || '0',
      change: dataLoading ? '...' : '+3',
      trend: 'up' as const,
      icon: Briefcase,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t.pendingTasks,
      value: dashboardData?.pending_tasks?.toString() || '0',
      change: dataLoading ? '...' : '-2',
      trend: 'down' as const,
      icon: Clock,
      color: 'from-orange-500 to-orange-600'
    }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'user_management',
      message: 'New employee added to HR department',
      messageAr: 'تم إضافة موظف جديد لقسم الموارد البشرية',
      timestamp: '5 minutes ago',
      icon: Users,
      severity: 'info'
    },
    {
      id: 2,
      type: 'project_update',
      message: 'Project milestone completed',
      messageAr: 'تم إكمال معلم مشروع',
      timestamp: '15 minutes ago',
      icon: CheckCircle,
      severity: 'success'
    },
    {
      id: 3,
      type: 'system_update',
      message: 'Department budget updated',
      messageAr: 'تم تحديث ميزانية القسم',
      timestamp: '30 minutes ago',
      icon: DollarSign,
      severity: 'info'
    },
    {
      id: 4,
      type: 'workflow',
      message: 'Workflow automation completed',
      messageAr: 'تم إكمال أتمتة سير العمل',
      timestamp: '1 hour ago',
      icon: Zap,
      severity: 'success'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">
            {t.welcome}, {user?.first_name} {user?.last_name}
          </h1>
          <p className="text-white/70">لوحة تحكم المدير - إدارة العمليات والموظفين</p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button variant="outline" className="glass-button">
            <BarChart3 className="h-4 w-4 mr-2" />
            {t.businessIntelligence}
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {overviewCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                  <p className="text-xs text-white/50">{card.change}</p>
                </div>
                <div className={`p-3 rounded-lg bg-gradient-to-r ${card.color}`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Error State */}
      {dataError && (
        <Card className="glass-card border-red-500/20 bg-red-500/10">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <p className="text-red-400">{dataError}</p>
              <Button
                onClick={loadDashboardData}
                variant="outline"
                size="sm"
                className="mr-auto text-red-400 border-red-400/20"
              >
                إعادة المحاولة
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Employee Growth Chart */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white">نمو الموظفين</CardTitle>
            <CardDescription className="text-white/70">
              تطور عدد الموظفين والتوظيف الجديد
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={chartData.employeeGrowth}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="month" stroke="rgba(255,255,255,0.7)" />
                  <YAxis stroke="rgba(255,255,255,0.7)" />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px'
                    }}
                  />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="employees"
                    stackId="1"
                    stroke="#3b82f6"
                    fill="rgba(59, 130, 246, 0.3)"
                    name="إجمالي الموظفين"
                  />
                  <Area
                    type="monotone"
                    dataKey="newHires"
                    stackId="2"
                    stroke="#10b981"
                    fill="rgba(16, 185, 129, 0.3)"
                    name="التوظيف الجديد"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Department Distribution */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white">توزيع الأقسام</CardTitle>
            <CardDescription className="text-white/70">
              توزيع الموظفين حسب الأقسام
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={chartData.departmentDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {chartData.departmentDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px'
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Health Chart */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white">صحة النظام</CardTitle>
          <CardDescription className="text-white/70">
            مراقبة أداء النظام والموارد
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData.systemHealth}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                <XAxis dataKey="metric" stroke="rgba(255,255,255,0.7)" />
                <YAxis stroke="rgba(255,255,255,0.7)" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '8px'
                  }}
                />
                <Bar
                  dataKey="usage"
                  fill="rgba(59, 130, 246, 0.6)"
                  name="الاستخدام %"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white">{t.quickActions}</CardTitle>
          <CardDescription className="text-white/70">
            إجراءات سريعة لإدارة النظام والعمليات
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <Link key={index} to={action.href}>
                <Button
                  variant="outline"
                  className={`w-full h-20 flex flex-col items-center justify-center gap-2 glass-button bg-gradient-to-r ${action.color} hover:scale-105 transition-transform`}
                >
                  <action.icon className="h-6 w-6" />
                  <span className="text-xs text-center">{action.title}</span>
                </Button>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activities */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white">{t.recentActivities}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-center gap-4 p-3 rounded-lg bg-white/5">
                <div className="p-2 rounded-lg bg-white/10">
                  <activity.icon className="h-4 w-4 text-white" />
                </div>
                <div className="flex-1">
                  <p className="text-white text-sm">
                    {language === 'ar' ? activity.messageAr : activity.message}
                  </p>
                  <p className="text-white/50 text-xs">{activity.timestamp}</p>
                </div>
                <Badge variant="outline" className="text-white border-white/20">
                  {activity.severity}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
