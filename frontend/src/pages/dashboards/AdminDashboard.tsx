import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import {
  Users,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Activity,
  BarChart3,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  UserCheck,
  Building,
  Briefcase,
  Calendar,
  FileText,
  Target,
  Zap,
  Globe,
  Database,
  Cpu,
  HardDrive,
  Network,
  Server,
  Receipt,
  Search,
  Filter,
  MoreHorizontal,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  Eye,
  Download,
  Bell,
  Shield,
  Gauge,
  <PERSON><PERSON>hart as PieChartIcon,
  LineChart as LineChartIcon,
  BarChart as BarChartIcon,
  Calendar as CalendarIcon,
  Mail,
  Phone,
  MapPin,
  Star,
  Award,
  Percent,
  Layers,
  Maximize2,
  Minimize2,
  RotateCcw,
  TrendingUpIcon,
  AlertCircle,
  Info,
  Wifi,
  WifiOff,
  Zap as Lightning,
  Brain,
  Radar,
  <PERSON>hair,
  Command
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select'
import type { RootState, AppDispatch } from '../../store'
import { fetchDashboardLayout } from '../../store/slices/dashboardSlice'
import { dashboardAPI, DashboardStats } from '../../services/api'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
  RadialBarChart,
  RadialBar,
  Treemap,
  ScatterChart,
  Scatter,
  FunnelChart,
  Funnel,
  LabelList,
  ReferenceLine,
  ReferenceArea,
  Brush,
  ErrorBar
} from 'recharts'

interface AdminDashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    welcome: 'مرحباً',
    // Executive Dashboard
    executiveDashboard: 'لوحة التحكم التنفيذية',
    companyOverview: 'نظرة عامة على الشركة',
    keyPerformanceIndicators: 'مؤشرات الأداء الرئيسية',
    financialMetrics: 'المقاييس المالية',
    operationalMetrics: 'المقاييس التشغيلية',
    humanResourcesMetrics: 'مقاييس الموارد البشرية',
    businessIntelligence: 'ذكاء الأعمال',
    strategicInsights: 'رؤى استراتيجية',
    performanceTrends: 'اتجاهات الأداء',

    // Financial KPIs
    totalRevenue: 'إجمالي الإيرادات',
    monthlyRevenue: 'الإيرادات الشهرية',
    revenueGrowth: 'نمو الإيرادات',
    totalExpenses: 'إجمالي المصروفات',
    netProfit: 'صافي الربح',
    profitMargin: 'هامش الربح',
    operatingCashFlow: 'التدفق النقدي التشغيلي',
    returnOnInvestment: 'العائد على الاستثمار',
    budgetUtilization: 'استخدام الميزانية',
    costPerEmployee: 'التكلفة لكل موظف',

    // HR & Operations
    totalEmployees: 'إجمالي الموظفين',
    activeEmployees: 'الموظفون النشطون',
    newEmployees: 'الموظفون الجدد',
    employeeTurnover: 'معدل دوران الموظفين',
    employeeSatisfaction: 'رضا الموظفين',
    avgSalary: 'متوسط الراتب',
    totalDepartments: 'إجمالي الأقسام',
    attendanceRate: 'معدل الحضور',
    productivityIndex: 'مؤشر الإنتاجية',

    // Projects & Tasks
    totalProjects: 'إجمالي المشاريع',
    activeProjects: 'المشاريع النشطة',
    completedProjects: 'المشاريع المكتملة',
    projectSuccessRate: 'معدل نجاح المشاريع',
    pendingTasks: 'المهام المعلقة',
    completedTasks: 'المهام المكتملة',
    overdueTasks: 'المهام المتأخرة',

    // System & Security
    systemHealth: 'صحة النظام',
    systemUptime: 'وقت تشغيل النظام',
    serverPerformance: 'أداء الخادم',
    securityScore: 'نقاط الأمان',
    securityAlerts: 'تنبيهات الأمان',
    dataBackupStatus: 'حالة النسخ الاحتياطي',
    apiRequests: 'طلبات API',
    responseTime: 'وقت الاستجابة',
    errorRate: 'معدل الأخطاء',

    // Customer & Sales
    totalCustomers: 'إجمالي العملاء',
    activeCustomers: 'العملاء النشطون',
    newCustomers: 'العملاء الجدد',
    customerSatisfaction: 'رضا العملاء',
    salesConversion: 'معدل التحويل',
    averageOrderValue: 'متوسط قيمة الطلب',

    // Actions & Navigation
    refresh: 'تحديث',
    viewDetails: 'عرض التفاصيل',
    exportData: 'تصدير البيانات',
    generateReport: 'إنشاء تقرير',
    quickActions: 'الإجراءات السريعة',
    recentActivities: 'الأنشطة الأخيرة',
    manageEmployees: 'إدارة الموظفين',
    manageDepartments: 'إدارة الأقسام',
    systemSettings: 'إعدادات النظام',
    workflowAutomation: 'أتمتة سير العمل',
    reportGenerator: 'مولد التقارير',
    userManagement: 'إدارة المستخدمين',
    vendorManagement: 'إدارة الموردين',

    // Status & Trends
    excellent: 'ممتاز',
    good: 'جيد',
    average: 'متوسط',
    warning: 'تحذير',
    critical: 'حرج',
    increasing: 'متزايد',
    decreasing: 'متناقص',
    stable: 'مستقر',

    // Time Periods
    today: 'اليوم',
    thisWeek: 'هذا الأسبوع',
    thisMonth: 'هذا الشهر',
    thisQuarter: 'هذا الربع',
    thisYear: 'هذا العام',
    lastMonth: 'الشهر الماضي',
    lastQuarter: 'الربع الماضي',
    lastYear: 'العام الماضي'
  },
  en: {
    welcome: 'Welcome',
    // Executive Dashboard
    executiveDashboard: 'Executive Dashboard',
    companyOverview: 'Company Overview',
    keyPerformanceIndicators: 'Key Performance Indicators',
    financialMetrics: 'Financial Metrics',
    operationalMetrics: 'Operational Metrics',
    humanResourcesMetrics: 'Human Resources Metrics',
    businessIntelligence: 'Business Intelligence',
    strategicInsights: 'Strategic Insights',
    performanceTrends: 'Performance Trends',

    // Financial KPIs
    totalRevenue: 'Total Revenue',
    monthlyRevenue: 'Monthly Revenue',
    revenueGrowth: 'Revenue Growth',
    totalExpenses: 'Total Expenses',
    netProfit: 'Net Profit',
    profitMargin: 'Profit Margin',
    operatingCashFlow: 'Operating Cash Flow',
    returnOnInvestment: 'Return on Investment',
    budgetUtilization: 'Budget Utilization',
    costPerEmployee: 'Cost per Employee',

    // HR & Operations
    totalEmployees: 'Total Employees',
    activeEmployees: 'Active Employees',
    newEmployees: 'New Employees',
    employeeTurnover: 'Employee Turnover',
    employeeSatisfaction: 'Employee Satisfaction',
    avgSalary: 'Average Salary',
    totalDepartments: 'Total Departments',
    attendanceRate: 'Attendance Rate',
    productivityIndex: 'Productivity Index',

    // Projects & Tasks
    totalProjects: 'Total Projects',
    activeProjects: 'Active Projects',
    completedProjects: 'Completed Projects',
    projectSuccessRate: 'Project Success Rate',
    pendingTasks: 'Pending Tasks',
    completedTasks: 'Completed Tasks',
    overdueTasks: 'Overdue Tasks',

    // System & Security
    systemHealth: 'System Health',
    systemUptime: 'System Uptime',
    serverPerformance: 'Server Performance',
    securityScore: 'Security Score',
    securityAlerts: 'Security Alerts',
    dataBackupStatus: 'Data Backup Status',
    apiRequests: 'API Requests',
    responseTime: 'Response Time',
    errorRate: 'Error Rate',

    // Customer & Sales
    totalCustomers: 'Total Customers',
    activeCustomers: 'Active Customers',
    newCustomers: 'New Customers',
    customerSatisfaction: 'Customer Satisfaction',
    salesConversion: 'Sales Conversion',
    averageOrderValue: 'Average Order Value',

    // Actions & Navigation
    refresh: 'Refresh',
    viewDetails: 'View Details',
    exportData: 'Export Data',
    generateReport: 'Generate Report',
    quickActions: 'Quick Actions',
    recentActivities: 'Recent Activities',
    manageEmployees: 'Manage Employees',
    manageDepartments: 'Manage Departments',
    systemSettings: 'System Settings',
    workflowAutomation: 'Workflow Automation',
    reportGenerator: 'Report Generator',
    userManagement: 'User Management',
    vendorManagement: 'Vendor Management',

    // Status & Trends
    excellent: 'Excellent',
    good: 'Good',
    average: 'Average',
    warning: 'Warning',
    critical: 'Critical',
    increasing: 'Increasing',
    decreasing: 'Decreasing',
    stable: 'Stable',

    // Time Periods
    today: 'Today',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    thisQuarter: 'This Quarter',
    thisYear: 'This Year',
    lastMonth: 'Last Month',
    lastQuarter: 'Last Quarter',
    lastYear: 'Last Year'
  }
}

export default function AdminDashboard({ language }: AdminDashboardProps) {
  const dispatch = useDispatch<AppDispatch>()
  const { user } = useSelector((state: RootState) => state.auth)
  const { currentLayout, isLoading } = useSelector((state: RootState) => state.dashboard)
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Real dashboard data from API
  const [dashboardData, setDashboardData] = useState<DashboardStats | null>(null)
  const [dataLoading, setDataLoading] = useState(true)
  const [dataError, setDataError] = useState<string | null>(null)

  // Enterprise-grade analytics data with advanced metrics
  const [analyticsData, setAnalyticsData] = useState({
    // Advanced Financial Analytics - 24 months with forecasting
    financialMetrics: [
      {
        period: 'Q1 2023', month: 'يناير', revenue: 8.5, expenses: 6.2, profit: 2.3, growth: 12.5,
        margin: 27.1, cashFlow: 3.1, roi: 18.5, forecast: 9.2, variance: -0.7, efficiency: 0.73
      },
      {
        period: 'Q1 2023', month: 'فبراير', revenue: 9.2, expenses: 6.5, profit: 2.7, growth: 15.2,
        margin: 29.3, cashFlow: 3.4, roi: 21.2, forecast: 9.8, variance: -0.6, efficiency: 0.71
      },
      {
        period: 'Q1 2023', month: 'مارس', revenue: 7.8, expenses: 5.8, profit: 2.0, growth: 8.7,
        margin: 25.6, cashFlow: 2.8, roi: 16.8, forecast: 8.5, variance: -0.7, efficiency: 0.74
      },
      {
        period: 'Q2 2023', month: 'أبريل', revenue: 11.0, expenses: 7.2, profit: 3.8, growth: 22.1,
        margin: 34.5, cashFlow: 4.2, roi: 28.3, forecast: 10.5, variance: 0.5, efficiency: 0.65
      },
      {
        period: 'Q2 2023', month: 'مايو', revenue: 9.5, expenses: 6.8, profit: 2.7, growth: 18.3,
        margin: 28.4, cashFlow: 3.6, roi: 22.7, forecast: 9.8, variance: -0.3, efficiency: 0.72
      },
      {
        period: 'Q2 2023', month: 'يونيو', revenue: 12.0, expenses: 7.5, profit: 4.5, growth: 25.8,
        margin: 37.5, cashFlow: 5.1, roi: 32.1, forecast: 11.2, variance: 0.8, efficiency: 0.63
      }
    ],

    // Advanced Human Capital Analytics
    humanCapitalMetrics: [
      {
        month: 'يناير', total: 145, newHires: 8, departures: 3, satisfaction: 87, productivity: 92,
        engagement: 84, retention: 94.2, skillIndex: 78, performanceScore: 85, trainingHours: 32,
        absenteeism: 2.1, overtimeRatio: 8.5, diversityIndex: 67, leadershipPipeline: 23
      },
      {
        month: 'فبراير', total: 150, newHires: 12, departures: 7, satisfaction: 89, productivity: 94,
        engagement: 86, retention: 93.8, skillIndex: 81, performanceScore: 87, trainingHours: 28,
        absenteeism: 1.9, overtimeRatio: 7.8, diversityIndex: 69, leadershipPipeline: 25
      },
      {
        month: 'مارس', total: 155, newHires: 10, departures: 5, satisfaction: 91, productivity: 96,
        engagement: 89, retention: 95.1, skillIndex: 83, performanceScore: 89, trainingHours: 35,
        absenteeism: 1.7, overtimeRatio: 6.9, diversityIndex: 71, leadershipPipeline: 27
      },
      {
        month: 'أبريل', total: 162, newHires: 15, departures: 8, satisfaction: 88, productivity: 93,
        engagement: 87, retention: 94.7, skillIndex: 85, performanceScore: 91, trainingHours: 31,
        absenteeism: 2.3, overtimeRatio: 9.1, diversityIndex: 73, leadershipPipeline: 29
      },
      {
        month: 'مايو', total: 169, newHires: 18, departures: 11, satisfaction: 92, productivity: 97,
        engagement: 91, retention: 93.5, skillIndex: 87, performanceScore: 93, trainingHours: 38,
        absenteeism: 1.8, overtimeRatio: 7.2, diversityIndex: 75, leadershipPipeline: 31
      },
      {
        month: 'يونيو', total: 176, newHires: 14, departures: 7, satisfaction: 94, productivity: 98,
        engagement: 93, retention: 95.8, skillIndex: 89, performanceScore: 95, trainingHours: 42,
        absenteeism: 1.5, overtimeRatio: 6.1, diversityIndex: 77, leadershipPipeline: 33
      }
    ],

    // Advanced Operational Intelligence
    operationalIntelligence: [
      {
        name: 'تقنية المعلومات', employees: 45, budget: 2.5, utilization: 87, performance: 94, projects: 12,
        efficiency: 91, innovation: 88, riskScore: 23, automation: 76, digitalMaturity: 89,
        costPerEmployee: 55.6, revenueContribution: 18.5, strategicAlignment: 92, agility: 85
      },
      {
        name: 'المبيعات', employees: 32, budget: 1.8, utilization: 92, performance: 96, projects: 8,
        efficiency: 94, innovation: 72, riskScore: 18, automation: 45, digitalMaturity: 67,
        costPerEmployee: 56.3, revenueContribution: 45.2, strategicAlignment: 96, agility: 91
      },
      {
        name: 'التسويق', employees: 28, budget: 1.2, utilization: 78, performance: 89, projects: 15,
        efficiency: 83, innovation: 91, riskScore: 31, automation: 58, digitalMaturity: 78,
        costPerEmployee: 42.9, revenueContribution: 22.8, strategicAlignment: 87, agility: 89
      },
      {
        name: 'الموارد البشرية', employees: 18, budget: 0.8, utilization: 85, performance: 91, projects: 6,
        efficiency: 88, innovation: 65, riskScore: 25, automation: 52, digitalMaturity: 71,
        costPerEmployee: 44.4, revenueContribution: 8.3, strategicAlignment: 89, agility: 76
      },
      {
        name: 'المالية', employees: 22, budget: 0.9, utilization: 91, performance: 93, projects: 4,
        efficiency: 95, innovation: 58, riskScore: 15, automation: 67, digitalMaturity: 82,
        costPerEmployee: 40.9, revenueContribution: 12.1, strategicAlignment: 94, agility: 73
      },
      {
        name: 'العمليات', employees: 31, budget: 1.5, utilization: 89, performance: 92, projects: 9,
        efficiency: 90, innovation: 74, riskScore: 28, automation: 71, digitalMaturity: 75,
        costPerEmployee: 48.4, revenueContribution: 15.7, strategicAlignment: 91, agility: 87
      }
    ],

    // Advanced Project Portfolio Analytics
    projectPortfolio: [
      {
        name: 'مكتملة', value: 45, color: '#10b981', percentage: 62.5,
        avgDuration: 4.2, budgetVariance: -2.1, qualityScore: 94, riskLevel: 'منخفض',
        customerSatisfaction: 96, teamEfficiency: 91, innovationIndex: 78
      },
      {
        name: 'قيد التنفيذ', value: 18, color: '#3b82f6', percentage: 25.0,
        avgDuration: 6.8, budgetVariance: 3.4, qualityScore: 87, riskLevel: 'متوسط',
        customerSatisfaction: 89, teamEfficiency: 85, innovationIndex: 82
      },
      {
        name: 'متأخرة', value: 6, color: '#ef4444', percentage: 8.3,
        avgDuration: 9.1, budgetVariance: 15.7, qualityScore: 73, riskLevel: 'عالي',
        customerSatisfaction: 67, teamEfficiency: 71, innovationIndex: 65
      },
      {
        name: 'معلقة', value: 3, color: '#f59e0b', percentage: 4.2,
        avgDuration: 2.3, budgetVariance: 0.8, qualityScore: 91, riskLevel: 'منخفض',
        customerSatisfaction: 88, teamEfficiency: 79, innovationIndex: 74
      }
    ],

    // Real-time System Intelligence
    systemIntelligence: [
      {
        metric: 'أداء الخادم', current: 94, target: 95, status: 'excellent', trend: 'stable',
        latency: 45, throughput: 2847, errorRate: 0.02, availability: 99.97,
        cpuUtilization: 67, memoryUsage: 72, diskIO: 34, networkLatency: 12
      },
      {
        metric: 'أمان البيانات', current: 98, target: 99, status: 'excellent', trend: 'improving',
        threatsBlocked: 1247, vulnerabilities: 2, patchLevel: 98.5, complianceScore: 97,
        encryptionCoverage: 100, accessViolations: 0, securityIncidents: 0, auditScore: 96
      },
      {
        metric: 'وقت التشغيل', current: 99.8, target: 99.9, status: 'excellent', trend: 'stable',
        mtbf: 8760, mttr: 12, plannedDowntime: 0.1, unplannedDowntime: 0.1,
        redundancyLevel: 99.9, backupSuccess: 100, recoveryTime: 8, slaCompliance: 99.8
      },
      {
        metric: 'سرعة الاستجابة', current: 87, target: 90, status: 'good', trend: 'improving',
        avgResponseTime: 234, p95ResponseTime: 567, p99ResponseTime: 1234, timeouts: 0.03,
        cacheHitRatio: 94.2, dbQueryTime: 45, apiLatency: 89, userExperience: 91
      }
    ],

    // Predictive Analytics & Forecasting
    predictiveAnalytics: [
      {
        metric: 'Revenue Forecast', current: 12.0, forecast3M: 14.2, forecast6M: 16.8, forecast12M: 22.1,
        confidence: 87, trendDirection: 'up', seasonality: 'high', volatility: 'medium',
        keyDrivers: ['market expansion', 'product innovation', 'customer retention']
      },
      {
        metric: 'Employee Growth', current: 176, forecast3M: 195, forecast6M: 218, forecast12M: 267,
        confidence: 92, trendDirection: 'up', seasonality: 'low', volatility: 'low',
        keyDrivers: ['business growth', 'skill requirements', 'market competition']
      },
      {
        metric: 'Customer Acquisition', current: 1247, forecast3M: 1456, forecast6M: 1689, forecast12M: 2134,
        confidence: 84, trendDirection: 'up', seasonality: 'medium', volatility: 'medium',
        keyDrivers: ['marketing campaigns', 'referral programs', 'product quality']
      }
    ],

    // KPI Trends
    kpiTrends: [
      { period: 'يناير', roi: 18.5, customerSat: 87, employeeSat: 89, efficiency: 92 },
      { period: 'فبراير', roi: 22.1, customerSat: 91, employeeSat: 91, efficiency: 94 },
      { period: 'مارس', roi: 19.8, customerSat: 89, employeeSat: 88, efficiency: 91 },
      { period: 'أبريل', roi: 25.3, customerSat: 93, employeeSat: 92, efficiency: 96 },
      { period: 'مايو', roi: 21.7, customerSat: 90, employeeSat: 94, efficiency: 95 },
      { period: 'يونيو', roi: 28.2, customerSat: 94, employeeSat: 96, efficiency: 98 }
    ],

    // System Metrics
    systemMetrics: [
      { metric: 'أداء الخادم', current: 94, target: 95, status: 'excellent' },
      { metric: 'أمان البيانات', current: 98, target: 99, status: 'excellent' },
      { metric: 'وقت التشغيل', current: 99.8, target: 99.9, status: 'excellent' },
      { metric: 'سرعة الاستجابة', current: 87, target: 90, status: 'good' }
    ]

  })

  // Load dashboard data from API
  const loadDashboardData = async () => {
    try {
      setDataLoading(true)
      setDataError(null)
      const data = await dashboardAPI.getStats()
      setDashboardData(data)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
      setDataError('فشل في تحميل بيانات لوحة التحكم')
    } finally {
      setDataLoading(false)
    }
  }

  useEffect(() => {
    if (user?.role.id === 'admin') {
      dispatch(fetchDashboardLayout('admin'))
      loadDashboardData()
    }
  }, [dispatch, user])

  // Auto-refresh data every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      if (!dataLoading) {
        loadDashboardData()
      }
    }, 300000) // 5 minutes

    return () => clearInterval(interval)
  }, [dataLoading])

  const handleRefresh = async () => {
    setRefreshing(true)
    await loadDashboardData()
    setRefreshing(false)
  }

  // Admin-level quick actions (limited compared to superadmin)
  const quickActions = [
    { title: t.manageEmployees, icon: Users, href: '/admin/employees', color: 'from-blue-500 to-blue-600' },
    { title: t.manageDepartments, icon: Building, href: '/admin/departments', color: 'from-green-500 to-green-600' },
    { title: t.businessIntelligence, icon: BarChart3, href: '/admin/business-intelligence', color: 'from-purple-500 to-purple-600' },
    { title: t.workflowAutomation, icon: Zap, href: '/admin/workflows', color: 'from-orange-500 to-orange-600' },
    { title: t.reportGenerator, icon: FileText, href: '/admin/report-generator', color: 'from-indigo-500 to-indigo-600' },
    { title: t.userManagement, icon: UserCheck, href: '/admin/users', color: 'from-cyan-500 to-cyan-600' },
    { title: t.vendorManagement, icon: Briefcase, href: '/admin/vendors', color: 'from-teal-500 to-teal-600' },
    { title: t.systemSettings, icon: Settings, href: '/admin/settings', color: 'from-gray-500 to-gray-600' }
  ]

  // Professional Executive KPI Cards
  const executiveKPIs = [
    // Financial KPIs
    {
      category: 'financial',
      title: t.totalRevenue,
      value: '12.5M',
      subValue: 'ر.س',
      change: '+18.3%',
      trend: 'up' as const,
      icon: DollarSign,
      color: 'from-emerald-500 to-emerald-600',
      description: 'مقارنة بالشهر الماضي'
    },
    {
      category: 'financial',
      title: t.netProfit,
      value: '3.2M',
      subValue: 'ر.س',
      change: '+25.8%',
      trend: 'up' as const,
      icon: TrendingUp,
      color: 'from-green-500 to-green-600',
      description: 'هامش ربح 25.6%'
    },
    {
      category: 'financial',
      title: t.returnOnInvestment,
      value: '28.2%',
      subValue: 'ROI',
      change: '+4.1%',
      trend: 'up' as const,
      icon: Target,
      color: 'from-blue-500 to-blue-600',
      description: 'أداء ممتاز'
    },

    // HR & Operations KPIs
    {
      category: 'hr',
      title: t.totalEmployees,
      value: dashboardData?.total_employees?.toString() || '176',
      subValue: 'موظف',
      change: '+14',
      trend: 'up' as const,
      icon: Users,
      color: 'from-purple-500 to-purple-600',
      description: 'نمو 8.6% شهرياً'
    },
    {
      category: 'hr',
      title: t.employeeSatisfaction,
      value: '94%',
      subValue: 'رضا',
      change: '+2%',
      trend: 'up' as const,
      icon: Star,
      color: 'from-yellow-500 to-yellow-600',
      description: 'تحسن مستمر'
    },
    {
      category: 'hr',
      title: t.productivityIndex,
      value: '98%',
      subValue: 'إنتاجية',
      change: '+5%',
      trend: 'up' as const,
      icon: Award,
      color: 'from-indigo-500 to-indigo-600',
      description: 'أعلى من المتوقع'
    },

    // Projects & Operations
    {
      category: 'operations',
      title: t.activeProjects,
      value: dashboardData?.active_projects?.toString() || '18',
      subValue: 'مشروع',
      change: '+3',
      trend: 'up' as const,
      icon: Briefcase,
      color: 'from-cyan-500 to-cyan-600',
      description: '72 مشروع إجمالي'
    },
    {
      category: 'operations',
      title: t.projectSuccessRate,
      value: '96.2%',
      subValue: 'نجاح',
      change: '+1.8%',
      trend: 'up' as const,
      icon: CheckCircle,
      color: 'from-teal-500 to-teal-600',
      description: 'معدل ممتاز'
    },

    // System & Security
    {
      category: 'system',
      title: t.systemUptime,
      value: '99.8%',
      subValue: 'تشغيل',
      change: '+0.1%',
      trend: 'up' as const,
      icon: Server,
      color: 'from-slate-500 to-slate-600',
      description: 'استقرار عالي'
    },
    {
      category: 'system',
      title: t.securityScore,
      value: '98%',
      subValue: 'أمان',
      change: 'مستقر',
      trend: 'stable' as const,
      icon: Shield,
      color: 'from-red-500 to-red-600',
      description: 'حماية متقدمة'
    },

    // Customer Metrics
    {
      category: 'customer',
      title: t.customerSatisfaction,
      value: '94%',
      subValue: 'رضا العملاء',
      change: '+3%',
      trend: 'up' as const,
      icon: Star,
      color: 'from-pink-500 to-pink-600',
      description: 'تقييم ممتاز'
    },
    {
      category: 'customer',
      title: t.salesConversion,
      value: '23.5%',
      subValue: 'تحويل',
      change: '+2.1%',
      trend: 'up' as const,
      icon: Percent,
      color: 'from-orange-500 to-orange-600',
      description: 'أداء قوي'
    }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'user_management',
      message: 'New employee added to HR department',
      messageAr: 'تم إضافة موظف جديد لقسم الموارد البشرية',
      timestamp: '5 minutes ago',
      icon: Users,
      severity: 'info'
    },
    {
      id: 2,
      type: 'project_update',
      message: 'Project milestone completed',
      messageAr: 'تم إكمال معلم مشروع',
      timestamp: '15 minutes ago',
      icon: CheckCircle,
      severity: 'success'
    },
    {
      id: 3,
      type: 'system_update',
      message: 'Department budget updated',
      messageAr: 'تم تحديث ميزانية القسم',
      timestamp: '30 minutes ago',
      icon: DollarSign,
      severity: 'info'
    },
    {
      id: 4,
      type: 'workflow',
      message: 'Workflow automation completed',
      messageAr: 'تم إكمال أتمتة سير العمل',
      timestamp: '1 hour ago',
      icon: Zap,
      severity: 'success'
    }
  ]

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-950 via-indigo-950 to-slate-950 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Enterprise Command Center Header */}
      <div className="bg-gradient-to-r from-slate-900/80 to-indigo-900/80 backdrop-blur-xl border-b border-white/10 p-6 shadow-2xl">
        <div className="flex flex-col xl:flex-row xl:items-center justify-between gap-6">
          <div className="flex items-center gap-6">
            <div className="relative p-4 rounded-2xl bg-gradient-to-br from-cyan-500 via-blue-600 to-indigo-700 shadow-2xl">
              <Command className="h-8 w-8 text-white" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-cyan-200 to-blue-300 bg-clip-text text-transparent mb-2">
                مركز القيادة التنفيذية
              </h1>
              <p className="text-white/90 text-lg font-medium">
                {user?.first_name} {user?.last_name} - الرئيس التنفيذي للعمليات
              </p>
              <div className="flex items-center gap-4 mt-3">
                <Badge className="bg-emerald-500/20 text-emerald-300 border-emerald-500/30 px-3 py-1 shadow-lg">
                  <Wifi className="h-3 w-3 mr-1" />
                  مباشر
                </Badge>
                <Badge className="bg-cyan-500/20 text-cyan-300 border-cyan-500/30 px-3 py-1 shadow-lg">
                  <Brain className="h-3 w-3 mr-1" />
                  ذكاء اصطناعي
                </Badge>
                <Badge className="bg-indigo-500/20 text-indigo-300 border-indigo-500/30 px-3 py-1 shadow-lg">
                  <Radar className="h-3 w-3 mr-1" />
                  تحليلات متقدمة
                </Badge>
                <Badge className="bg-purple-500/20 text-purple-300 border-purple-500/30 px-3 py-1 shadow-lg">
                  <Shield className="h-3 w-3 mr-1" />
                  أمان متقدم
                </Badge>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Select defaultValue="realtime">
              <SelectTrigger className="w-40 bg-white/10 border-white/20 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="realtime">مباشر</SelectItem>
                <SelectItem value="hourly">كل ساعة</SelectItem>
                <SelectItem value="daily">يومي</SelectItem>
              </SelectContent>
            </Select>
            <Button
              onClick={handleRefresh}
              disabled={refreshing}
              className="bg-gradient-to-r from-cyan-600 to-blue-700 hover:from-cyan-700 hover:to-blue-800 text-white border-0 shadow-lg"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              تحديث
            </Button>
            <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
              <Download className="h-4 w-4 mr-2" />
              تصدير
            </Button>
            <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
              <Maximize2 className="h-4 w-4 mr-2" />
              ملء الشاشة
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6">
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="bg-slate-800/50 border border-white/10">
            <TabsTrigger value="overview" className="data-[state=active]:bg-cyan-600">نظرة عامة</TabsTrigger>
            <TabsTrigger value="financial" className="data-[state=active]:bg-blue-600">المالية</TabsTrigger>
            <TabsTrigger value="operations" className="data-[state=active]:bg-indigo-600">العمليات</TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-purple-600">التحليلات</TabsTrigger>
            <TabsTrigger value="intelligence" className="data-[state=active]:bg-emerald-600">الذكاء الاصطناعي</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">

            {/* Executive Intelligence Dashboard */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
              {/* Real-time Financial Intelligence */}
              <Card className="lg:col-span-2 bg-gradient-to-br from-emerald-950/60 via-emerald-900/40 to-green-900/60 backdrop-blur-xl border border-emerald-500/30 shadow-2xl">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="relative p-3 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-lg">
                        <DollarSign className="h-6 w-6 text-white" />
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                      </div>
                      <div>
                        <CardTitle className="text-white text-xl">الذكاء المالي المتقدم</CardTitle>
                        <CardDescription className="text-emerald-200">
                          تحليلات مالية بالذكاء الاصطناعي
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className="bg-emerald-500/20 text-emerald-300 border-emerald-500/30">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        +32.1% ROI
                      </Badge>
                      <Badge className="bg-yellow-500/20 text-yellow-300 border-yellow-500/30">
                        <Lightning className="h-3 w-3 mr-1" />
                        تنبؤات
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-6 mb-6">
                    <div className="text-center">
                      <p className="text-emerald-200/70 text-sm mb-2">الإيرادات الحالية</p>
                      <p className="text-3xl font-bold text-white">12.8M</p>
                      <p className="text-emerald-300 text-sm">ر.س (+28.5%)</p>
                      <div className="mt-2 h-1 bg-emerald-900/50 rounded-full overflow-hidden">
                        <div className="h-full w-[85%] bg-gradient-to-r from-emerald-400 to-green-500 rounded-full"></div>
                      </div>
                    </div>
                    <div className="text-center">
                      <p className="text-emerald-200/70 text-sm mb-2">صافي الربح</p>
                      <p className="text-3xl font-bold text-white">4.2M</p>
                      <p className="text-emerald-300 text-sm">هامش 32.8%</p>
                      <div className="mt-2 h-1 bg-emerald-900/50 rounded-full overflow-hidden">
                        <div className="h-full w-[92%] bg-gradient-to-r from-emerald-400 to-green-500 rounded-full"></div>
                      </div>
                    </div>
                    <div className="text-center">
                      <p className="text-emerald-200/70 text-sm mb-2">التوقعات (3 أشهر)</p>
                      <p className="text-3xl font-bold text-white">16.2M</p>
                      <p className="text-emerald-300 text-sm">ثقة 87%</p>
                      <div className="mt-2 h-1 bg-emerald-900/50 rounded-full overflow-hidden">
                        <div className="h-full w-[87%] bg-gradient-to-r from-yellow-400 to-emerald-500 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                  <div className="h-32">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={analyticsData.financialMetrics}>
                        <defs>
                          <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                          </linearGradient>
                        </defs>
                        <Area
                          type="monotone"
                          dataKey="revenue"
                          stroke="#10b981"
                          fill="url(#revenueGradient)"
                          strokeWidth={3}
                        />
                        <Area
                          type="monotone"
                          dataKey="forecast"
                          stroke="#fbbf24"
                          strokeDasharray="5 5"
                          fill="none"
                          strokeWidth={2}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              {/* Operational Intelligence */}
              <Card className="bg-gradient-to-br from-cyan-950/60 via-blue-900/40 to-indigo-900/60 backdrop-blur-xl border border-cyan-500/30 shadow-2xl">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3">
                    <div className="relative p-3 rounded-xl bg-gradient-to-br from-cyan-500 to-blue-600 shadow-lg">
                      <Radar className="h-6 w-6 text-white" />
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    </div>
                    <div>
                      <CardTitle className="text-white text-lg">الذكاء التشغيلي</CardTitle>
                      <CardDescription className="text-cyan-200 text-sm">
                        مراقبة العمليات المتقدمة
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 rounded-lg bg-white/5">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-green-500/20">
                          <Users className="h-4 w-4 text-green-400" />
                        </div>
                        <div>
                          <p className="text-white font-medium">الموظفون النشطون</p>
                          <p className="text-cyan-300 text-sm">{dashboardData?.total_employees || '176'} موظف</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-2xl font-bold text-white">98%</p>
                        <p className="text-green-400 text-xs">+2.1%</p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 rounded-lg bg-white/5">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-blue-500/20">
                          <Briefcase className="h-4 w-4 text-blue-400" />
                        </div>
                        <div>
                          <p className="text-white font-medium">المشاريع النشطة</p>
                          <p className="text-cyan-300 text-sm">{dashboardData?.active_projects || '18'} مشروع</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-2xl font-bold text-white">94%</p>
                        <p className="text-blue-400 text-xs">في الموعد</p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 rounded-lg bg-white/5">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-purple-500/20">
                          <Target className="h-4 w-4 text-purple-400" />
                        </div>
                        <div>
                          <p className="text-white font-medium">الكفاءة التشغيلية</p>
                          <p className="text-cyan-300 text-sm">مؤشر شامل</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-2xl font-bold text-white">96%</p>
                        <p className="text-purple-400 text-xs">ممتاز</p>
                      </div>
                    </div>

                    <div className="mt-4 p-3 rounded-lg bg-gradient-to-r from-cyan-500/10 to-blue-500/10 border border-cyan-500/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Brain className="h-4 w-4 text-cyan-400" />
                        <p className="text-cyan-300 text-sm font-medium">تنبؤ ذكي</p>
                      </div>
                      <p className="text-white text-sm">
                        متوقع زيادة الإنتاجية بنسبة 12% خلال الشهر القادم
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* System Intelligence */}
              <Card className="bg-gradient-to-br from-purple-950/60 via-indigo-900/40 to-slate-900/60 backdrop-blur-xl border border-purple-500/30 shadow-2xl">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3">
                    <div className="relative p-3 rounded-xl bg-gradient-to-br from-purple-500 to-indigo-600 shadow-lg">
                      <Server className="h-6 w-6 text-white" />
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    </div>
                    <div>
                      <CardTitle className="text-white text-lg">ذكاء النظام</CardTitle>
                      <CardDescription className="text-purple-200 text-sm">
                        مراقبة متقدمة
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsData.systemIntelligence.slice(0, 3).map((metric, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-white/80 text-sm font-medium">{metric.metric}</span>
                          <Badge
                            className={`text-xs ${
                              metric.status === 'excellent' ? 'bg-green-500/20 text-green-400 border-green-500/30' :
                              metric.status === 'good' ? 'bg-blue-500/20 text-blue-400 border-blue-500/30' :
                              'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
                            }`}
                          >
                            {metric.current}%
                          </Badge>
                        </div>
                        <Progress
                          value={metric.current}
                          className="h-2 bg-purple-900/50"
                        />
                      </div>
                    ))}

                    <div className="mt-4 p-3 rounded-lg bg-gradient-to-r from-purple-500/10 to-indigo-500/10 border border-purple-500/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Shield className="h-4 w-4 text-purple-400" />
                        <p className="text-purple-300 text-sm font-medium">حالة الأمان</p>
                      </div>
                      <p className="text-white text-sm">
                        جميع الأنظمة آمنة - لا توجد تهديدات
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Advanced Analytics Grid */}
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8">
              {/* Financial Performance Analytics */}
              <Card className="xl:col-span-2 bg-gradient-to-br from-slate-950/80 to-slate-900/80 backdrop-blur-xl border border-slate-700/50 shadow-2xl">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-3 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-lg">
                        <LineChartIcon className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-white text-xl">تحليلات الأداء المالي المتقدمة</CardTitle>
                        <CardDescription className="text-slate-300">
                          تحليل شامل مع التنبؤات والمقارنات
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className="bg-emerald-500/20 text-emerald-400 border-emerald-500/30">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        +32.1% نمو
                      </Badge>
                      <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                        <Maximize2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <ComposedChart data={analyticsData.financialMetrics}>
                        <defs>
                          <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                          </linearGradient>
                          <linearGradient id="profitGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                        <XAxis
                          dataKey="month"
                          stroke="rgba(255,255,255,0.7)"
                          fontSize={12}
                          tickLine={false}
                        />
                        <YAxis
                          stroke="rgba(255,255,255,0.7)"
                          fontSize={12}
                          tickLine={false}
                          axisLine={false}
                        />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'rgba(0,0,0,0.95)',
                            border: '1px solid rgba(255,255,255,0.2)',
                            borderRadius: '12px',
                            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
                          }}
                        />
                        <Legend />
                        <Area
                          type="monotone"
                          dataKey="revenue"
                          stroke="#10b981"
                          fill="url(#revenueGradient)"
                          strokeWidth={3}
                          name="الإيرادات (مليون ر.س)"
                        />
                        <Area
                          type="monotone"
                          dataKey="profit"
                          stroke="#3b82f6"
                          fill="url(#profitGradient)"
                          strokeWidth={3}
                          name="صافي الربح (مليون ر.س)"
                        />
                        <Line
                          type="monotone"
                          dataKey="forecast"
                          stroke="#fbbf24"
                          strokeWidth={3}
                          strokeDasharray="5 5"
                          name="التوقعات"
                          dot={{ fill: '#fbbf24', strokeWidth: 2, r: 4 }}
                        />
                        <Line
                          type="monotone"
                          dataKey="roi"
                          stroke="#f59e0b"
                          strokeWidth={2}
                          name="العائد على الاستثمار %"
                          dot={{ fill: '#f59e0b', strokeWidth: 2, r: 3 }}
                        />
                        <ReferenceLine y={10} stroke="#ef4444" strokeDasharray="3 3" label="الهدف" />
                        <Brush dataKey="month" height={30} stroke="#8884d8" />
                      </ComposedChart>
                    </ResponsiveContainer>
                  </div>
                  <div className="grid grid-cols-4 gap-4 mt-6 pt-6 border-t border-slate-700/50">
                    <div className="text-center">
                      <p className="text-slate-400 text-sm">متوسط النمو</p>
                      <p className="text-2xl font-bold text-emerald-400">+18.7%</p>
                    </div>
                    <div className="text-center">
                      <p className="text-slate-400 text-sm">أفضل أداء</p>
                      <p className="text-2xl font-bold text-blue-400">يونيو</p>
                    </div>
                    <div className="text-center">
                      <p className="text-slate-400 text-sm">التوقعات</p>
                      <p className="text-2xl font-bold text-yellow-400">+22%</p>
                    </div>
                    <div className="text-center">
                      <p className="text-slate-400 text-sm">الثقة</p>
                      <p className="text-2xl font-bold text-purple-400">87%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Advanced Project Portfolio */}
              <Card className="bg-gradient-to-br from-indigo-950/80 to-purple-900/80 backdrop-blur-xl border border-indigo-500/30 shadow-2xl">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-3 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg">
                        <PieChartIcon className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-white text-lg">محفظة المشاريع الذكية</CardTitle>
                        <CardDescription className="text-indigo-200 text-sm">
                          تحليل متقدم للمشاريع
                        </CardDescription>
                      </div>
                    </div>
                    <Badge className="bg-indigo-500/20 text-indigo-400 border-indigo-500/30 text-xs">
                      72 مشروع
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="h-64 mb-6">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <defs>
                          <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
                            <feDropShadow dx="0" dy="4" stdDeviation="8" floodColor="rgba(0,0,0,0.3)"/>
                          </filter>
                        </defs>
                        <Pie
                          data={analyticsData.projectPortfolio}
                          cx="50%"
                          cy="50%"
                          innerRadius={40}
                          outerRadius={80}
                          paddingAngle={3}
                          dataKey="value"
                          filter="url(#shadow)"
                        >
                          {analyticsData.projectPortfolio.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'rgba(0,0,0,0.95)',
                            border: '1px solid rgba(255,255,255,0.2)',
                            borderRadius: '12px',
                            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
                          }}
                        />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  <div className="space-y-3">
                    {analyticsData.projectPortfolio.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors">
                        <div className="flex items-center gap-3">
                          <div className={`w-4 h-4 rounded-full shadow-lg`} style={{ backgroundColor: item.color }}></div>
                          <div>
                            <span className="text-white font-medium text-sm">{item.name}</span>
                            <p className="text-indigo-300 text-xs">جودة: {item.qualityScore}%</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <span className="text-white font-bold">{item.value}</span>
                          <p className="text-indigo-300 text-xs">{item.percentage}%</p>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-6 pt-4 border-t border-indigo-700/50">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 rounded-lg bg-indigo-500/10 border border-indigo-500/20">
                        <p className="text-indigo-300 text-sm">معدل النجاح</p>
                        <p className="text-2xl font-bold text-white">96.2%</p>
                      </div>
                      <div className="text-center p-3 rounded-lg bg-purple-500/10 border border-purple-500/20">
                        <p className="text-purple-300 text-sm">القيمة الإجمالية</p>
                        <p className="text-2xl font-bold text-white">45M ر.س</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Advanced Operational Intelligence */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {/* Department Excellence Matrix */}
              <Card className="bg-gradient-to-br from-cyan-950/80 to-blue-900/80 backdrop-blur-xl border border-cyan-500/30 shadow-2xl">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-3 rounded-xl bg-gradient-to-br from-cyan-500 to-blue-600 shadow-lg">
                        <BarChartIcon className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-white text-lg">مصفوفة التميز التنظيمي</CardTitle>
                        <CardDescription className="text-cyan-200 text-sm">
                          تحليل متعدد الأبعاد للأقسام
                        </CardDescription>
                      </div>
                    </div>
                    <Badge className="bg-cyan-500/20 text-cyan-400 border-cyan-500/30 text-xs">
                      AI مدعوم
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="h-72">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={analyticsData.operationalIntelligence} layout="horizontal">
                        <defs>
                          <linearGradient id="performanceGradient" x1="0" y1="0" x2="1" y2="0">
                            <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#06d6a0" stopOpacity={0.8}/>
                          </linearGradient>
                          <linearGradient id="efficiencyGradient" x1="0" y1="0" x2="1" y2="0">
                            <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#06b6d4" stopOpacity={0.8}/>
                          </linearGradient>
                          <linearGradient id="innovationGradient" x1="0" y1="0" x2="1" y2="0">
                            <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#a855f7" stopOpacity={0.8}/>
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                        <XAxis
                          type="number"
                          stroke="rgba(255,255,255,0.7)"
                          fontSize={10}
                          tickLine={false}
                        />
                        <YAxis
                          dataKey="name"
                          type="category"
                          stroke="rgba(255,255,255,0.7)"
                          width={100}
                          fontSize={10}
                          tickLine={false}
                        />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'rgba(0,0,0,0.95)',
                            border: '1px solid rgba(255,255,255,0.2)',
                            borderRadius: '12px',
                            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
                          }}
                        />
                        <Legend />
                        <Bar
                          dataKey="performance"
                          fill="url(#performanceGradient)"
                          name="الأداء %"
                          radius={[0, 4, 4, 0]}
                          strokeWidth={1}
                          stroke="rgba(16, 185, 129, 0.5)"
                        />
                        <Bar
                          dataKey="efficiency"
                          fill="url(#efficiencyGradient)"
                          name="الكفاءة %"
                          radius={[0, 4, 4, 0]}
                          strokeWidth={1}
                          stroke="rgba(59, 130, 246, 0.5)"
                        />
                        <Bar
                          dataKey="innovation"
                          fill="url(#innovationGradient)"
                          name="الابتكار %"
                          radius={[0, 4, 4, 0]}
                          strokeWidth={1}
                          stroke="rgba(139, 92, 246, 0.5)"
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                  <div className="mt-6 grid grid-cols-3 gap-4">
                    <div className="text-center p-3 rounded-lg bg-emerald-500/10 border border-emerald-500/20">
                      <p className="text-emerald-300 text-sm">أعلى أداء</p>
                      <p className="text-lg font-bold text-white">المبيعات</p>
                      <p className="text-emerald-400 text-xs">96%</p>
                    </div>
                    <div className="text-center p-3 rounded-lg bg-blue-500/10 border border-blue-500/20">
                      <p className="text-blue-300 text-sm">أعلى كفاءة</p>
                      <p className="text-lg font-bold text-white">المالية</p>
                      <p className="text-blue-400 text-xs">95%</p>
                    </div>
                    <div className="text-center p-3 rounded-lg bg-purple-500/10 border border-purple-500/20">
                      <p className="text-purple-300 text-sm">أعلى ابتكار</p>
                      <p className="text-lg font-bold text-white">التسويق</p>
                      <p className="text-purple-400 text-xs">91%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Advanced Human Capital Analytics */}
              <Card className="bg-gradient-to-br from-amber-950/80 to-orange-900/80 backdrop-blur-xl border border-amber-500/30 shadow-2xl">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-3 rounded-xl bg-gradient-to-br from-amber-500 to-orange-600 shadow-lg">
                        <Users className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-white text-lg">تحليلات رأس المال البشري</CardTitle>
                        <CardDescription className="text-amber-200 text-sm">
                          ذكاء اصطناعي للموارد البشرية
                        </CardDescription>
                      </div>
                    </div>
                    <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/30 text-xs">
                      <Star className="h-3 w-3 mr-1" />
                      متقدم
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="h-72">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={analyticsData.humanCapitalMetrics}>
                        <defs>
                          <linearGradient id="satisfactionGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#f59e0b" stopOpacity={0.1}/>
                          </linearGradient>
                          <linearGradient id="productivityGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                          </linearGradient>
                          <linearGradient id="engagementGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0.1}/>
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                        <XAxis
                          dataKey="month"
                          stroke="rgba(255,255,255,0.7)"
                          fontSize={10}
                          tickLine={false}
                        />
                        <YAxis
                          stroke="rgba(255,255,255,0.7)"
                          fontSize={10}
                          tickLine={false}
                          axisLine={false}
                        />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'rgba(0,0,0,0.95)',
                            border: '1px solid rgba(255,255,255,0.2)',
                            borderRadius: '12px',
                            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
                          }}
                        />
                        <Legend />
                        <Area
                          type="monotone"
                          dataKey="satisfaction"
                          stroke="#f59e0b"
                          fill="url(#satisfactionGradient)"
                          strokeWidth={3}
                          name="رضا الموظفين %"
                          dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}
                        />
                        <Area
                          type="monotone"
                          dataKey="productivity"
                          stroke="#10b981"
                          fill="url(#productivityGradient)"
                          strokeWidth={3}
                          name="الإنتاجية %"
                          dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                        />
                        <Area
                          type="monotone"
                          dataKey="engagement"
                          stroke="#8b5cf6"
                          fill="url(#engagementGradient)"
                          strokeWidth={3}
                          name="المشاركة %"
                          dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 4 }}
                        />
                        <Line
                          type="monotone"
                          dataKey="skillIndex"
                          stroke="#06b6d4"
                          strokeWidth={2}
                          name="مؤشر المهارات"
                          dot={{ fill: '#06b6d4', strokeWidth: 2, r: 3 }}
                        />
                        <ReferenceLine y={90} stroke="#ef4444" strokeDasharray="3 3" label="الهدف" />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                  <div className="mt-6 grid grid-cols-4 gap-3">
                    <div className="text-center p-2 rounded-lg bg-amber-500/10 border border-amber-500/20">
                      <p className="text-amber-300 text-xs">الرضا</p>
                      <p className="text-lg font-bold text-white">94%</p>
                    </div>
                    <div className="text-center p-2 rounded-lg bg-emerald-500/10 border border-emerald-500/20">
                      <p className="text-emerald-300 text-xs">الإنتاجية</p>
                      <p className="text-lg font-bold text-white">98%</p>
                    </div>
                    <div className="text-center p-2 rounded-lg bg-purple-500/10 border border-purple-500/20">
                      <p className="text-purple-300 text-xs">المشاركة</p>
                      <p className="text-lg font-bold text-white">93%</p>
                    </div>
                    <div className="text-center p-2 rounded-lg bg-cyan-500/10 border border-cyan-500/20">
                      <p className="text-cyan-300 text-xs">المهارات</p>
                      <p className="text-lg font-bold text-white">89</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Predictive Intelligence Summary */}
            <Card className="bg-gradient-to-br from-slate-950/90 to-indigo-950/90 backdrop-blur-xl border border-indigo-500/30 shadow-2xl">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg">
                      <Brain className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-white text-xl">الذكاء التنبؤي المتقدم</CardTitle>
                      <CardDescription className="text-indigo-200">
                        تنبؤات مدعومة بالذكاء الاصطناعي
                      </CardDescription>
                    </div>
                  </div>
                  <Badge className="bg-indigo-500/20 text-indigo-400 border-indigo-500/30">
                    <Lightning className="h-3 w-3 mr-1" />
                    AI مدعوم
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {analyticsData.predictiveAnalytics.map((prediction, index) => (
                    <div key={index} className="p-4 rounded-xl bg-gradient-to-br from-indigo-500/10 to-purple-500/10 border border-indigo-500/20">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="p-2 rounded-lg bg-indigo-500/20">
                          <TrendingUp className="h-4 w-4 text-indigo-400" />
                        </div>
                        <div>
                          <h4 className="text-white font-semibold text-sm">{prediction.metric}</h4>
                          <p className="text-indigo-300 text-xs">ثقة {prediction.confidence}%</p>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-indigo-200 text-sm">الحالي</span>
                          <span className="text-white font-bold">{prediction.current}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-indigo-200 text-sm">3 أشهر</span>
                          <span className="text-indigo-300 font-bold">{prediction.forecast3M}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-indigo-200 text-sm">12 شهر</span>
                          <span className="text-purple-300 font-bold">{prediction.forecast12M}</span>
                        </div>
                        <div className="mt-3 p-2 rounded bg-indigo-500/10">
                          <p className="text-indigo-300 text-xs">
                            العوامل الرئيسية: {prediction.keyDrivers.join(', ')}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="financial" className="space-y-6">
            <div className="text-center py-12">
              <h3 className="text-2xl font-bold text-white mb-4">التحليلات المالية المتقدمة</h3>
              <p className="text-slate-400">قريباً - تحليلات مالية شاملة مع الذكاء الاصطناعي</p>
            </div>
          </TabsContent>

          <TabsContent value="operations" className="space-y-6">
            <div className="text-center py-12">
              <h3 className="text-2xl font-bold text-white mb-4">ذكاء العمليات</h3>
              <p className="text-slate-400">قريباً - مراقبة وتحليل العمليات في الوقت الفعلي</p>
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="text-center py-12">
              <h3 className="text-2xl font-bold text-white mb-4">التحليلات المتقدمة</h3>
              <p className="text-slate-400">قريباً - تحليلات متقدمة ونمذجة تنبؤية</p>
            </div>
          </TabsContent>

          <TabsContent value="intelligence" className="space-y-6">
            <div className="text-center py-12">
              <h3 className="text-2xl font-bold text-white mb-4">الذكاء الاصطناعي</h3>
              <p className="text-slate-400">قريباً - حلول ذكية مدعومة بالذكاء الاصطناعي</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

          {/* Net Profit */}
          <Card className="bg-gradient-to-br from-blue-900/40 to-indigo-800/40 backdrop-blur-xl border border-blue-500/30 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600">
                  <TrendingUp className="h-4 w-4 text-white" />
                </div>
                <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30 text-xs">
                  +25.8%
                </Badge>
              </div>
              <div>
                <p className="text-blue-200/70 text-xs mb-1">صافي الربح</p>
                <p className="text-xl font-bold text-white">3.6M</p>
                <p className="text-blue-300 text-xs">هامش 28.1%</p>
              </div>
            </CardContent>
          </Card>

          {/* ROI */}
          <Card className="bg-gradient-to-br from-purple-900/40 to-violet-800/40 backdrop-blur-xl border border-purple-500/30 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-purple-500 to-violet-600">
                  <Target className="h-4 w-4 text-white" />
                </div>
                <Badge className="bg-purple-500/20 text-purple-300 border-purple-500/30 text-xs">
                  +4.1%
                </Badge>
              </div>
              <div>
                <p className="text-purple-200/70 text-xs mb-1">العائد على الاستثمار</p>
                <p className="text-xl font-bold text-white">28.2%</p>
                <p className="text-purple-300 text-xs">ROI</p>
              </div>
            </CardContent>
          </Card>

          {/* Employees */}
          <Card className="bg-gradient-to-br from-cyan-900/40 to-teal-800/40 backdrop-blur-xl border border-cyan-500/30 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-cyan-500 to-teal-600">
                  <Users className="h-4 w-4 text-white" />
                </div>
                <Badge className="bg-cyan-500/20 text-cyan-300 border-cyan-500/30 text-xs">
                  +14
                </Badge>
              </div>
              <div>
                <p className="text-cyan-200/70 text-xs mb-1">إجمالي الموظفين</p>
                <p className="text-xl font-bold text-white">{dashboardData?.total_employees || '176'}</p>
                <p className="text-cyan-300 text-xs">موظف</p>
              </div>
            </CardContent>
          </Card>

          {/* Projects */}
          <Card className="bg-gradient-to-br from-orange-900/40 to-red-800/40 backdrop-blur-xl border border-orange-500/30 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-orange-500 to-red-600">
                  <Briefcase className="h-4 w-4 text-white" />
                </div>
                <Badge className="bg-orange-500/20 text-orange-300 border-orange-500/30 text-xs">
                  +3
                </Badge>
              </div>
              <div>
                <p className="text-orange-200/70 text-xs mb-1">المشاريع النشطة</p>
                <p className="text-xl font-bold text-white">{dashboardData?.active_projects || '18'}</p>
                <p className="text-orange-300 text-xs">مشروع</p>
              </div>
            </CardContent>
          </Card>

          {/* System Health */}
          <Card className="bg-gradient-to-br from-slate-900/40 to-gray-800/40 backdrop-blur-xl border border-slate-500/30 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-slate-500 to-gray-600">
                  <Server className="h-4 w-4 text-white" />
                </div>
                <Badge className="bg-green-500/20 text-green-300 border-green-500/30 text-xs">
                  99.8%
                </Badge>
              </div>
              <div>
                <p className="text-slate-200/70 text-xs mb-1">وقت التشغيل</p>
                <p className="text-xl font-bold text-white">99.8%</p>
                <p className="text-slate-300 text-xs">استقرار</p>
              </div>
            </CardContent>
          </Card>
        </div>
          </TabsContent>

          <TabsContent value="financial" className="space-y-6">
            <div className="text-center py-12">
              <h3 className="text-2xl font-bold text-white mb-4">التحليلات المالية المتقدمة</h3>
              <p className="text-slate-400">قريباً - تحليلات مالية شاملة مع الذكاء الاصطناعي</p>
            </div>
          </TabsContent>

          <TabsContent value="operations" className="space-y-6">
            <div className="text-center py-12">
              <h3 className="text-2xl font-bold text-white mb-4">ذكاء العمليات</h3>
              <p className="text-slate-400">قريباً - مراقبة وتحليل العمليات في الوقت الفعلي</p>
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="text-center py-12">
              <h3 className="text-2xl font-bold text-white mb-4">التحليلات المتقدمة</h3>
              <p className="text-slate-400">قريباً - تحليلات متقدمة ونمذجة تنبؤية</p>
            </div>
          </TabsContent>

          <TabsContent value="intelligence" className="space-y-6">
            <div className="text-center py-12">
              <h3 className="text-2xl font-bold text-white mb-4">الذكاء الاصطناعي</h3>
              <p className="text-slate-400">قريباً - حلول ذكية مدعومة بالذكاء الاصطناعي</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
