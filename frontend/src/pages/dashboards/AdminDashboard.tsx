import React, { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import {
  Users,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Activity,
  BarChart3,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  UserCheck,
  Building,
  Briefcase,
  Calendar,
  FileText,
  Target,
  Zap,
  Globe,
  Database,
  Cpu,
  HardDrive,
  Network,
  Server,
  Receipt,
  Search,
  Filter,
  MoreHorizontal,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  Eye,
  Download,
  Bell,
  Shield,
  Gauge,
  PieChart as PieChartIcon,
  LineChart as LineChartIcon,
  BarChart as BarChartIcon,
  Calendar as CalendarIcon,
  Mail,
  Phone,
  MapPin,
  Star,
  Award,
  Percent
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'
import type { RootState, AppDispatch } from '../../store'
import { fetchDashboardLayout } from '../../store/slices/dashboardSlice'
import { dashboardAPI, DashboardStats } from '../../services/api'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
  RadialBarChart,
  RadialBar,
  Treemap,
  ScatterChart,
  Scatter
} from 'recharts'

interface AdminDashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    welcome: 'مرحباً',
    // Executive Dashboard
    executiveDashboard: 'لوحة التحكم التنفيذية',
    companyOverview: 'نظرة عامة على الشركة',
    keyPerformanceIndicators: 'مؤشرات الأداء الرئيسية',
    financialMetrics: 'المقاييس المالية',
    operationalMetrics: 'المقاييس التشغيلية',
    humanResourcesMetrics: 'مقاييس الموارد البشرية',
    businessIntelligence: 'ذكاء الأعمال',
    strategicInsights: 'رؤى استراتيجية',
    performanceTrends: 'اتجاهات الأداء',

    // Financial KPIs
    totalRevenue: 'إجمالي الإيرادات',
    monthlyRevenue: 'الإيرادات الشهرية',
    revenueGrowth: 'نمو الإيرادات',
    totalExpenses: 'إجمالي المصروفات',
    netProfit: 'صافي الربح',
    profitMargin: 'هامش الربح',
    operatingCashFlow: 'التدفق النقدي التشغيلي',
    returnOnInvestment: 'العائد على الاستثمار',
    budgetUtilization: 'استخدام الميزانية',
    costPerEmployee: 'التكلفة لكل موظف',

    // HR & Operations
    totalEmployees: 'إجمالي الموظفين',
    activeEmployees: 'الموظفون النشطون',
    newEmployees: 'الموظفون الجدد',
    employeeTurnover: 'معدل دوران الموظفين',
    employeeSatisfaction: 'رضا الموظفين',
    avgSalary: 'متوسط الراتب',
    totalDepartments: 'إجمالي الأقسام',
    attendanceRate: 'معدل الحضور',
    productivityIndex: 'مؤشر الإنتاجية',

    // Projects & Tasks
    totalProjects: 'إجمالي المشاريع',
    activeProjects: 'المشاريع النشطة',
    completedProjects: 'المشاريع المكتملة',
    projectSuccessRate: 'معدل نجاح المشاريع',
    pendingTasks: 'المهام المعلقة',
    completedTasks: 'المهام المكتملة',
    overdueTasks: 'المهام المتأخرة',

    // System & Security
    systemHealth: 'صحة النظام',
    systemUptime: 'وقت تشغيل النظام',
    serverPerformance: 'أداء الخادم',
    securityScore: 'نقاط الأمان',
    securityAlerts: 'تنبيهات الأمان',
    dataBackupStatus: 'حالة النسخ الاحتياطي',
    apiRequests: 'طلبات API',
    responseTime: 'وقت الاستجابة',
    errorRate: 'معدل الأخطاء',

    // Customer & Sales
    totalCustomers: 'إجمالي العملاء',
    activeCustomers: 'العملاء النشطون',
    newCustomers: 'العملاء الجدد',
    customerSatisfaction: 'رضا العملاء',
    salesConversion: 'معدل التحويل',
    averageOrderValue: 'متوسط قيمة الطلب',

    // Actions & Navigation
    refresh: 'تحديث',
    viewDetails: 'عرض التفاصيل',
    exportData: 'تصدير البيانات',
    generateReport: 'إنشاء تقرير',
    quickActions: 'الإجراءات السريعة',
    recentActivities: 'الأنشطة الأخيرة',
    manageEmployees: 'إدارة الموظفين',
    manageDepartments: 'إدارة الأقسام',
    systemSettings: 'إعدادات النظام',
    workflowAutomation: 'أتمتة سير العمل',
    reportGenerator: 'مولد التقارير',
    userManagement: 'إدارة المستخدمين',
    vendorManagement: 'إدارة الموردين',

    // Status & Trends
    excellent: 'ممتاز',
    good: 'جيد',
    average: 'متوسط',
    warning: 'تحذير',
    critical: 'حرج',
    increasing: 'متزايد',
    decreasing: 'متناقص',
    stable: 'مستقر',

    // Time Periods
    today: 'اليوم',
    thisWeek: 'هذا الأسبوع',
    thisMonth: 'هذا الشهر',
    thisQuarter: 'هذا الربع',
    thisYear: 'هذا العام',
    lastMonth: 'الشهر الماضي',
    lastQuarter: 'الربع الماضي',
    lastYear: 'العام الماضي'
  },
  en: {
    welcome: 'Welcome',
    // Executive Dashboard
    executiveDashboard: 'Executive Dashboard',
    companyOverview: 'Company Overview',
    keyPerformanceIndicators: 'Key Performance Indicators',
    financialMetrics: 'Financial Metrics',
    operationalMetrics: 'Operational Metrics',
    humanResourcesMetrics: 'Human Resources Metrics',
    businessIntelligence: 'Business Intelligence',
    strategicInsights: 'Strategic Insights',
    performanceTrends: 'Performance Trends',

    // Financial KPIs
    totalRevenue: 'Total Revenue',
    monthlyRevenue: 'Monthly Revenue',
    revenueGrowth: 'Revenue Growth',
    totalExpenses: 'Total Expenses',
    netProfit: 'Net Profit',
    profitMargin: 'Profit Margin',
    operatingCashFlow: 'Operating Cash Flow',
    returnOnInvestment: 'Return on Investment',
    budgetUtilization: 'Budget Utilization',
    costPerEmployee: 'Cost per Employee',

    // HR & Operations
    totalEmployees: 'Total Employees',
    activeEmployees: 'Active Employees',
    newEmployees: 'New Employees',
    employeeTurnover: 'Employee Turnover',
    employeeSatisfaction: 'Employee Satisfaction',
    avgSalary: 'Average Salary',
    totalDepartments: 'Total Departments',
    attendanceRate: 'Attendance Rate',
    productivityIndex: 'Productivity Index',

    // Projects & Tasks
    totalProjects: 'Total Projects',
    activeProjects: 'Active Projects',
    completedProjects: 'Completed Projects',
    projectSuccessRate: 'Project Success Rate',
    pendingTasks: 'Pending Tasks',
    completedTasks: 'Completed Tasks',
    overdueTasks: 'Overdue Tasks',

    // System & Security
    systemHealth: 'System Health',
    systemUptime: 'System Uptime',
    serverPerformance: 'Server Performance',
    securityScore: 'Security Score',
    securityAlerts: 'Security Alerts',
    dataBackupStatus: 'Data Backup Status',
    apiRequests: 'API Requests',
    responseTime: 'Response Time',
    errorRate: 'Error Rate',

    // Customer & Sales
    totalCustomers: 'Total Customers',
    activeCustomers: 'Active Customers',
    newCustomers: 'New Customers',
    customerSatisfaction: 'Customer Satisfaction',
    salesConversion: 'Sales Conversion',
    averageOrderValue: 'Average Order Value',

    // Actions & Navigation
    refresh: 'Refresh',
    viewDetails: 'View Details',
    exportData: 'Export Data',
    generateReport: 'Generate Report',
    quickActions: 'Quick Actions',
    recentActivities: 'Recent Activities',
    manageEmployees: 'Manage Employees',
    manageDepartments: 'Manage Departments',
    systemSettings: 'System Settings',
    workflowAutomation: 'Workflow Automation',
    reportGenerator: 'Report Generator',
    userManagement: 'User Management',
    vendorManagement: 'Vendor Management',

    // Status & Trends
    excellent: 'Excellent',
    good: 'Good',
    average: 'Average',
    warning: 'Warning',
    critical: 'Critical',
    increasing: 'Increasing',
    decreasing: 'Decreasing',
    stable: 'Stable',

    // Time Periods
    today: 'Today',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    thisQuarter: 'This Quarter',
    thisYear: 'This Year',
    lastMonth: 'Last Month',
    lastQuarter: 'Last Quarter',
    lastYear: 'Last Year'
  }
}

export default function AdminDashboard({ language }: AdminDashboardProps) {
  const dispatch = useDispatch<AppDispatch>()
  const { user } = useSelector((state: RootState) => state.auth)
  const { currentLayout, isLoading } = useSelector((state: RootState) => state.dashboard)
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Real dashboard data from API
  const [dashboardData, setDashboardData] = useState<DashboardStats | null>(null)
  const [dataLoading, setDataLoading] = useState(true)
  const [dataError, setDataError] = useState<string | null>(null)

  // Professional enterprise-level chart data
  const [chartData, setChartData] = useState({
    // Financial Performance - 12 months (in millions for better chart display)
    financialTrends: [
      { month: 'يناير', revenue: 8.5, expenses: 6.2, profit: 2.3, growth: 12.5 },
      { month: 'فبراير', revenue: 9.2, expenses: 6.5, profit: 2.7, growth: 15.2 },
      { month: 'مارس', revenue: 7.8, expenses: 5.8, profit: 2.0, growth: 8.7 },
      { month: 'أبريل', revenue: 11.0, expenses: 7.2, profit: 3.8, growth: 22.1 },
      { month: 'مايو', revenue: 9.5, expenses: 6.8, profit: 2.7, growth: 18.3 },
      { month: 'يونيو', revenue: 12.0, expenses: 7.5, profit: 4.5, growth: 25.8 }
    ],

    // Employee Analytics
    employeeMetrics: [
      { month: 'يناير', total: 145, newHires: 8, departures: 3, satisfaction: 87, productivity: 92 },
      { month: 'فبراير', total: 150, newHires: 12, departures: 7, satisfaction: 89, productivity: 94 },
      { month: 'مارس', total: 155, newHires: 10, departures: 5, satisfaction: 91, productivity: 96 },
      { month: 'أبريل', total: 162, newHires: 15, departures: 8, satisfaction: 88, productivity: 93 },
      { month: 'مايو', total: 169, newHires: 18, departures: 11, satisfaction: 92, productivity: 97 },
      { month: 'يونيو', total: 176, newHires: 14, departures: 7, satisfaction: 94, productivity: 98 }
    ],

    // Department Performance
    departmentPerformance: [
      { name: 'تقنية المعلومات', employees: 45, budget: 2500000, utilization: 87, performance: 94, projects: 12 },
      { name: 'المبيعات', employees: 32, budget: 1800000, utilization: 92, performance: 96, projects: 8 },
      { name: 'التسويق', employees: 28, budget: 1200000, utilization: 78, performance: 89, projects: 15 },
      { name: 'الموارد البشرية', employees: 18, budget: 800000, utilization: 85, performance: 91, projects: 6 },
      { name: 'المالية', employees: 22, budget: 900000, utilization: 91, performance: 93, projects: 4 },
      { name: 'العمليات', employees: 31, budget: 1500000, utilization: 89, performance: 92, projects: 9 }
    ],

    // Project Status Distribution
    projectStatus: [
      { name: 'مكتملة', value: 45, color: '#10b981', percentage: 62.5 },
      { name: 'قيد التنفيذ', value: 18, color: '#3b82f6', percentage: 25.0 },
      { name: 'متأخرة', value: 6, color: '#ef4444', percentage: 8.3 },
      { name: 'معلقة', value: 3, color: '#f59e0b', percentage: 4.2 }
    ],

    // Customer Satisfaction & Sales
    customerMetrics: [
      { quarter: 'Q1', satisfaction: 87, retention: 94, newCustomers: 156, revenue: 2850000 },
      { quarter: 'Q2', satisfaction: 91, retention: 96, newCustomers: 189, revenue: 3200000 },
      { quarter: 'Q3', satisfaction: 89, retention: 95, newCustomers: 167, revenue: 2950000 },
      { quarter: 'Q4', satisfaction: 93, retention: 97, newCustomers: 203, revenue: 3650000 }
    ],

    // System Performance & Security
    systemMetrics: [
      { metric: 'أداء الخادم', current: 94, target: 95, status: 'excellent' },
      { metric: 'أمان البيانات', current: 98, target: 99, status: 'excellent' },
      { metric: 'وقت التشغيل', current: 99.8, target: 99.9, status: 'excellent' },
      { metric: 'سرعة الاستجابة', current: 87, target: 90, status: 'good' },
      { metric: 'النسخ الاحتياطي', current: 100, target: 100, status: 'excellent' },
      { metric: 'مراقبة الأمان', current: 96, target: 98, status: 'good' }
    ],

    // KPI Trends
    kpiTrends: [
      { period: 'يناير', roi: 18.5, customerSat: 87, employeeSat: 89, efficiency: 92 },
      { period: 'فبراير', roi: 22.1, customerSat: 91, employeeSat: 91, efficiency: 94 },
      { period: 'مارس', roi: 19.8, customerSat: 89, employeeSat: 88, efficiency: 91 },
      { period: 'أبريل', roi: 25.3, customerSat: 93, employeeSat: 92, efficiency: 96 },
      { period: 'مايو', roi: 21.7, customerSat: 90, employeeSat: 94, efficiency: 95 },
      { period: 'يونيو', roi: 28.2, customerSat: 94, employeeSat: 96, efficiency: 98 }
    ]
  })

  // Load dashboard data from API
  const loadDashboardData = async () => {
    try {
      setDataLoading(true)
      setDataError(null)
      const data = await dashboardAPI.getStats()
      setDashboardData(data)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
      setDataError('فشل في تحميل بيانات لوحة التحكم')
    } finally {
      setDataLoading(false)
    }
  }

  useEffect(() => {
    if (user?.role.id === 'admin') {
      dispatch(fetchDashboardLayout('admin'))
      loadDashboardData()
    }
  }, [dispatch, user])

  // Auto-refresh data every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      if (!dataLoading) {
        loadDashboardData()
      }
    }, 300000) // 5 minutes

    return () => clearInterval(interval)
  }, [dataLoading])

  const handleRefresh = async () => {
    setRefreshing(true)
    await loadDashboardData()
    setRefreshing(false)
  }

  // Admin-level quick actions (limited compared to superadmin)
  const quickActions = [
    { title: t.manageEmployees, icon: Users, href: '/admin/employees', color: 'from-blue-500 to-blue-600' },
    { title: t.manageDepartments, icon: Building, href: '/admin/departments', color: 'from-green-500 to-green-600' },
    { title: t.businessIntelligence, icon: BarChart3, href: '/admin/business-intelligence', color: 'from-purple-500 to-purple-600' },
    { title: t.workflowAutomation, icon: Zap, href: '/admin/workflows', color: 'from-orange-500 to-orange-600' },
    { title: t.reportGenerator, icon: FileText, href: '/admin/report-generator', color: 'from-indigo-500 to-indigo-600' },
    { title: t.userManagement, icon: UserCheck, href: '/admin/users', color: 'from-cyan-500 to-cyan-600' },
    { title: t.vendorManagement, icon: Briefcase, href: '/admin/vendors', color: 'from-teal-500 to-teal-600' },
    { title: t.systemSettings, icon: Settings, href: '/admin/settings', color: 'from-gray-500 to-gray-600' }
  ]

  // Professional Executive KPI Cards
  const executiveKPIs = [
    // Financial KPIs
    {
      category: 'financial',
      title: t.totalRevenue,
      value: '12.5M',
      subValue: 'ر.س',
      change: '+18.3%',
      trend: 'up' as const,
      icon: DollarSign,
      color: 'from-emerald-500 to-emerald-600',
      description: 'مقارنة بالشهر الماضي'
    },
    {
      category: 'financial',
      title: t.netProfit,
      value: '3.2M',
      subValue: 'ر.س',
      change: '+25.8%',
      trend: 'up' as const,
      icon: TrendingUp,
      color: 'from-green-500 to-green-600',
      description: 'هامش ربح 25.6%'
    },
    {
      category: 'financial',
      title: t.returnOnInvestment,
      value: '28.2%',
      subValue: 'ROI',
      change: '+4.1%',
      trend: 'up' as const,
      icon: Target,
      color: 'from-blue-500 to-blue-600',
      description: 'أداء ممتاز'
    },

    // HR & Operations KPIs
    {
      category: 'hr',
      title: t.totalEmployees,
      value: dashboardData?.total_employees?.toString() || '176',
      subValue: 'موظف',
      change: '+14',
      trend: 'up' as const,
      icon: Users,
      color: 'from-purple-500 to-purple-600',
      description: 'نمو 8.6% شهرياً'
    },
    {
      category: 'hr',
      title: t.employeeSatisfaction,
      value: '94%',
      subValue: 'رضا',
      change: '+2%',
      trend: 'up' as const,
      icon: Star,
      color: 'from-yellow-500 to-yellow-600',
      description: 'تحسن مستمر'
    },
    {
      category: 'hr',
      title: t.productivityIndex,
      value: '98%',
      subValue: 'إنتاجية',
      change: '+5%',
      trend: 'up' as const,
      icon: Award,
      color: 'from-indigo-500 to-indigo-600',
      description: 'أعلى من المتوقع'
    },

    // Projects & Operations
    {
      category: 'operations',
      title: t.activeProjects,
      value: dashboardData?.active_projects?.toString() || '18',
      subValue: 'مشروع',
      change: '+3',
      trend: 'up' as const,
      icon: Briefcase,
      color: 'from-cyan-500 to-cyan-600',
      description: '72 مشروع إجمالي'
    },
    {
      category: 'operations',
      title: t.projectSuccessRate,
      value: '96.2%',
      subValue: 'نجاح',
      change: '+1.8%',
      trend: 'up' as const,
      icon: CheckCircle,
      color: 'from-teal-500 to-teal-600',
      description: 'معدل ممتاز'
    },

    // System & Security
    {
      category: 'system',
      title: t.systemUptime,
      value: '99.8%',
      subValue: 'تشغيل',
      change: '+0.1%',
      trend: 'up' as const,
      icon: Server,
      color: 'from-slate-500 to-slate-600',
      description: 'استقرار عالي'
    },
    {
      category: 'system',
      title: t.securityScore,
      value: '98%',
      subValue: 'أمان',
      change: 'مستقر',
      trend: 'stable' as const,
      icon: Shield,
      color: 'from-red-500 to-red-600',
      description: 'حماية متقدمة'
    },

    // Customer Metrics
    {
      category: 'customer',
      title: t.customerSatisfaction,
      value: '94%',
      subValue: 'رضا العملاء',
      change: '+3%',
      trend: 'up' as const,
      icon: Star,
      color: 'from-pink-500 to-pink-600',
      description: 'تقييم ممتاز'
    },
    {
      category: 'customer',
      title: t.salesConversion,
      value: '23.5%',
      subValue: 'تحويل',
      change: '+2.1%',
      trend: 'up' as const,
      icon: Percent,
      color: 'from-orange-500 to-orange-600',
      description: 'أداء قوي'
    }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'user_management',
      message: 'New employee added to HR department',
      messageAr: 'تم إضافة موظف جديد لقسم الموارد البشرية',
      timestamp: '5 minutes ago',
      icon: Users,
      severity: 'info'
    },
    {
      id: 2,
      type: 'project_update',
      message: 'Project milestone completed',
      messageAr: 'تم إكمال معلم مشروع',
      timestamp: '15 minutes ago',
      icon: CheckCircle,
      severity: 'success'
    },
    {
      id: 3,
      type: 'system_update',
      message: 'Department budget updated',
      messageAr: 'تم تحديث ميزانية القسم',
      timestamp: '30 minutes ago',
      icon: DollarSign,
      severity: 'info'
    },
    {
      id: 4,
      type: 'workflow',
      message: 'Workflow automation completed',
      messageAr: 'تم إكمال أتمتة سير العمل',
      timestamp: '1 hour ago',
      icon: Zap,
      severity: 'success'
    }
  ]

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Professional Executive Header */}
      <div className="bg-gradient-to-r from-slate-800/50 to-purple-800/50 backdrop-blur-xl border-b border-white/10 p-6">
        <div className="flex flex-col xl:flex-row xl:items-center justify-between gap-6">
          <div className="flex items-center gap-6">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-2xl">
              <BarChart3 className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent mb-2">
                لوحة التحكم التنفيذية
              </h1>
              <p className="text-white/80 text-lg font-medium">
                مرحباً {user?.first_name} {user?.last_name} - المدير التنفيذي
              </p>
              <div className="flex items-center gap-4 mt-3">
                <Badge className="bg-green-500/20 text-green-400 border-green-500/30 px-3 py-1">
                  <Activity className="h-3 w-3 mr-1" />
                  النظام متصل
                </Badge>
                <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30 px-3 py-1">
                  <Clock className="h-3 w-3 mr-1" />
                  {new Date().toLocaleDateString('ar-SA')}
                </Badge>
                <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30 px-3 py-1">
                  <Shield className="h-3 w-3 mr-1" />
                  أمان عالي
                </Badge>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Button
              onClick={handleRefresh}
              disabled={refreshing}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              تحديث البيانات
            </Button>
            <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
              <Download className="h-4 w-4 mr-2" />
              تصدير التقرير
            </Button>
            <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
              <Settings className="h-4 w-4 mr-2" />
              الإعدادات
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-8">

        {/* Compact Executive KPI Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {/* Financial Performance */}
          <Card className="bg-gradient-to-br from-emerald-900/40 to-green-800/40 backdrop-blur-xl border border-emerald-500/30 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-emerald-500 to-green-600">
                  <DollarSign className="h-4 w-4 text-white" />
                </div>
                <Badge className="bg-emerald-500/20 text-emerald-300 border-emerald-500/30 text-xs">
                  +28.5%
                </Badge>
              </div>
              <div>
                <p className="text-emerald-200/70 text-xs mb-1">إجمالي الإيرادات</p>
                <p className="text-xl font-bold text-white">12.8M</p>
                <p className="text-emerald-300 text-xs">ر.س</p>
              </div>
            </CardContent>
          </Card>

          {/* Net Profit */}
          <Card className="bg-gradient-to-br from-blue-900/40 to-indigo-800/40 backdrop-blur-xl border border-blue-500/30 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600">
                  <TrendingUp className="h-4 w-4 text-white" />
                </div>
                <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30 text-xs">
                  +25.8%
                </Badge>
              </div>
              <div>
                <p className="text-blue-200/70 text-xs mb-1">صافي الربح</p>
                <p className="text-xl font-bold text-white">3.6M</p>
                <p className="text-blue-300 text-xs">هامش 28.1%</p>
              </div>
            </CardContent>
          </Card>

          {/* ROI */}
          <Card className="bg-gradient-to-br from-purple-900/40 to-violet-800/40 backdrop-blur-xl border border-purple-500/30 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-purple-500 to-violet-600">
                  <Target className="h-4 w-4 text-white" />
                </div>
                <Badge className="bg-purple-500/20 text-purple-300 border-purple-500/30 text-xs">
                  +4.1%
                </Badge>
              </div>
              <div>
                <p className="text-purple-200/70 text-xs mb-1">العائد على الاستثمار</p>
                <p className="text-xl font-bold text-white">28.2%</p>
                <p className="text-purple-300 text-xs">ROI</p>
              </div>
            </CardContent>
          </Card>

          {/* Employees */}
          <Card className="bg-gradient-to-br from-cyan-900/40 to-teal-800/40 backdrop-blur-xl border border-cyan-500/30 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-cyan-500 to-teal-600">
                  <Users className="h-4 w-4 text-white" />
                </div>
                <Badge className="bg-cyan-500/20 text-cyan-300 border-cyan-500/30 text-xs">
                  +14
                </Badge>
              </div>
              <div>
                <p className="text-cyan-200/70 text-xs mb-1">إجمالي الموظفين</p>
                <p className="text-xl font-bold text-white">{dashboardData?.total_employees || '176'}</p>
                <p className="text-cyan-300 text-xs">موظف</p>
              </div>
            </CardContent>
          </Card>

          {/* Projects */}
          <Card className="bg-gradient-to-br from-orange-900/40 to-red-800/40 backdrop-blur-xl border border-orange-500/30 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-orange-500 to-red-600">
                  <Briefcase className="h-4 w-4 text-white" />
                </div>
                <Badge className="bg-orange-500/20 text-orange-300 border-orange-500/30 text-xs">
                  +3
                </Badge>
              </div>
              <div>
                <p className="text-orange-200/70 text-xs mb-1">المشاريع النشطة</p>
                <p className="text-xl font-bold text-white">{dashboardData?.active_projects || '18'}</p>
                <p className="text-orange-300 text-xs">مشروع</p>
              </div>
            </CardContent>
          </Card>

          {/* System Health */}
          <Card className="bg-gradient-to-br from-slate-900/40 to-gray-800/40 backdrop-blur-xl border border-slate-500/30 shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-slate-500 to-gray-600">
                  <Server className="h-4 w-4 text-white" />
                </div>
                <Badge className="bg-green-500/20 text-green-300 border-green-500/30 text-xs">
                  99.8%
                </Badge>
              </div>
              <div>
                <p className="text-slate-200/70 text-xs mb-1">وقت التشغيل</p>
                <p className="text-xl font-bold text-white">99.8%</p>
                <p className="text-slate-300 text-xs">استقرار</p>
              </div>
            </CardContent>
          </Card>
        </div>



        {/* Compact Analytics Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Financial Performance Chart */}
          <Card className="bg-gradient-to-br from-slate-900/60 to-slate-800/60 backdrop-blur-xl border border-slate-700/50 shadow-lg">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-emerald-500 to-green-600">
                    <LineChartIcon className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-white text-lg">الأداء المالي</CardTitle>
                    <CardDescription className="text-slate-300 text-sm">
                      آخر 6 أشهر (مليون ر.س)
                    </CardDescription>
                  </div>
                </div>
                <Badge className="bg-emerald-500/20 text-emerald-400 border-emerald-500/30 text-xs">
                  +28.5%
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <ComposedChart data={chartData.financialTrends}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    <XAxis
                      dataKey="month"
                      stroke="rgba(255,255,255,0.7)"
                      fontSize={10}
                      tickLine={false}
                    />
                    <YAxis
                      stroke="rgba(255,255,255,0.7)"
                      fontSize={10}
                      tickLine={false}
                      axisLine={false}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0,0,0,0.95)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px'
                      }}
                    />
                    <Legend />
                    <Bar dataKey="revenue" fill="#10b981" name="الإيرادات" radius={[2, 2, 0, 0]} />
                    <Bar dataKey="expenses" fill="#ef4444" name="المصروفات" radius={[2, 2, 0, 0]} />
                    <Line type="monotone" dataKey="profit" stroke="#3b82f6" strokeWidth={2} name="صافي الربح" />
                  </ComposedChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Project Status Chart */}
          <Card className="bg-gradient-to-br from-indigo-900/60 to-purple-800/60 backdrop-blur-xl border border-indigo-500/30 shadow-lg">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-600">
                    <PieChartIcon className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-white text-lg">حالة المشاريع</CardTitle>
                    <CardDescription className="text-indigo-200 text-sm">
                      توزيع 72 مشروع
                    </CardDescription>
                  </div>
                </div>
                <Badge className="bg-indigo-500/20 text-indigo-400 border-indigo-500/30 text-xs">
                  96.2%
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-48 mb-4">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={chartData.projectStatus}
                      cx="50%"
                      cy="50%"
                      innerRadius={30}
                      outerRadius={70}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {chartData.projectStatus.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0,0,0,0.95)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px'
                      }}
                    />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="grid grid-cols-2 gap-2 text-xs">
                {chartData.projectStatus.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-2 rounded bg-white/5">
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full`} style={{ backgroundColor: item.color }}></div>
                      <span className="text-white">{item.name}</span>
                    </div>
                    <span className="text-white font-bold">{item.value}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Compact Performance Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Department Performance */}
          <Card className="bg-gradient-to-br from-cyan-900/60 to-blue-800/60 backdrop-blur-xl border border-cyan-500/30 shadow-lg">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-cyan-500 to-blue-600">
                    <BarChartIcon className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-white text-lg">أداء الأقسام</CardTitle>
                    <CardDescription className="text-cyan-200 text-sm">
                      الكفاءة والإنتاجية
                    </CardDescription>
                  </div>
                </div>
                <Badge className="bg-cyan-500/20 text-cyan-400 border-cyan-500/30 text-xs">
                  94%
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-56">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={chartData.departmentPerformance} layout="horizontal">
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    <XAxis
                      type="number"
                      stroke="rgba(255,255,255,0.7)"
                      fontSize={10}
                      tickLine={false}
                    />
                    <YAxis
                      dataKey="name"
                      type="category"
                      stroke="rgba(255,255,255,0.7)"
                      width={80}
                      fontSize={10}
                      tickLine={false}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0,0,0,0.95)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px'
                      }}
                    />
                    <Legend />
                    <Bar
                      dataKey="performance"
                      fill="#10b981"
                      name="الأداء %"
                      radius={[0, 4, 4, 0]}
                    />
                    <Bar
                      dataKey="utilization"
                      fill="#3b82f6"
                      name="الكفاءة %"
                      radius={[0, 4, 4, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Employee Analytics */}
          <Card className="bg-gradient-to-br from-amber-900/60 to-orange-800/60 backdrop-blur-xl border border-amber-500/30 shadow-lg">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-amber-500 to-orange-600">
                    <Users className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-white text-lg">مقاييس الموظفين</CardTitle>
                    <CardDescription className="text-amber-200 text-sm">
                      الرضا والإنتاجية
                    </CardDescription>
                  </div>
                </div>
                <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/30 text-xs">
                  96%
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-56">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData.employeeMetrics}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    <XAxis
                      dataKey="month"
                      stroke="rgba(255,255,255,0.7)"
                      fontSize={10}
                      tickLine={false}
                    />
                    <YAxis
                      stroke="rgba(255,255,255,0.7)"
                      fontSize={10}
                      tickLine={false}
                      axisLine={false}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0,0,0,0.95)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px'
                      }}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="satisfaction"
                      stroke="#f59e0b"
                      strokeWidth={2}
                      name="رضا الموظفين %"
                      dot={{ fill: '#f59e0b', strokeWidth: 2, r: 3 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="productivity"
                      stroke="#10b981"
                      strokeWidth={2}
                      name="الإنتاجية %"
                      dot={{ fill: '#10b981', strokeWidth: 2, r: 3 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="total"
                      stroke="#3b82f6"
                      strokeWidth={2}
                      name="إجمالي الموظفين"
                      dot={{ fill: '#3b82f6', strokeWidth: 2, r: 3 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* KPI Trends */}
        <Card className="bg-gradient-to-br from-slate-900/80 to-purple-900/80 backdrop-blur-xl border border-purple-500/30 shadow-lg">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="p-2 rounded-lg bg-gradient-to-br from-purple-500 to-indigo-600">
                  <Gauge className="h-4 w-4 text-white" />
                </div>
                <div>
                  <CardTitle className="text-white text-lg">مؤشرات الأداء الرئيسية</CardTitle>
                  <CardDescription className="text-purple-200 text-sm">
                    اتجاهات الأداء الاستراتيجي
                  </CardDescription>
                </div>
              </div>
              <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30 text-xs">
                مباشر
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData.kpiTrends}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis
                    dataKey="period"
                    stroke="rgba(255,255,255,0.7)"
                    fontSize={10}
                    tickLine={false}
                  />
                  <YAxis
                    stroke="rgba(255,255,255,0.7)"
                    fontSize={10}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.95)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px'
                    }}
                  />
                  <Legend />
                  <Line type="monotone" dataKey="roi" stroke="#10b981" strokeWidth={2} name="ROI %" />
                  <Line type="monotone" dataKey="customerSat" stroke="#3b82f6" strokeWidth={2} name="رضا العملاء %" />
                  <Line type="monotone" dataKey="employeeSat" stroke="#f59e0b" strokeWidth={2} name="رضا الموظفين %" />
                  <Line type="monotone" dataKey="efficiency" stroke="#8b5cf6" strokeWidth={2} name="الكفاءة %" />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div className="grid grid-cols-4 gap-2 mt-4 pt-4 border-t border-purple-700/50">
              <div className="text-center p-2 rounded bg-emerald-500/10">
                <p className="text-emerald-300 text-xs">ROI</p>
                <p className="text-lg font-bold text-white">28.2%</p>
              </div>
              <div className="text-center p-2 rounded bg-blue-500/10">
                <p className="text-blue-300 text-xs">العملاء</p>
                <p className="text-lg font-bold text-white">94%</p>
              </div>
              <div className="text-center p-2 rounded bg-amber-500/10">
                <p className="text-amber-300 text-xs">الموظفين</p>
                <p className="text-lg font-bold text-white">96%</p>
              </div>
              <div className="text-center p-2 rounded bg-purple-500/10">
                <p className="text-purple-300 text-xs">الكفاءة</p>
                <p className="text-lg font-bold text-white">98%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Compact System Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {chartData.systemMetrics.slice(0, 4).map((metric, index) => (
            <Card key={index} className="bg-gradient-to-br from-slate-900/60 to-gray-800/60 backdrop-blur-xl border border-slate-500/30 shadow-lg">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-slate-500 to-gray-600">
                    <Server className="h-4 w-4 text-white" />
                  </div>
                  <Badge
                    className={`text-xs ${
                      metric.status === 'excellent' ? 'bg-green-500/20 text-green-400 border-green-500/30' :
                      metric.status === 'good' ? 'bg-blue-500/20 text-blue-400 border-blue-500/30' :
                      'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
                    }`}
                  >
                    {metric.status === 'excellent' ? 'ممتاز' : metric.status === 'good' ? 'جيد' : 'متوسط'}
                  </Badge>
                </div>
                <div>
                  <p className="text-slate-200/70 text-xs mb-1">{metric.metric}</p>
                  <p className="text-xl font-bold text-white">{metric.current}%</p>
                  <Progress value={metric.current} className="h-1 bg-slate-700/50 mt-2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
