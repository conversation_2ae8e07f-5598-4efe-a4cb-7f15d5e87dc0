import React, { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import {
  Users,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Activity,
  BarChart3,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  UserCheck,
  Building,
  Briefcase,
  Calendar,
  FileText,
  Target,
  Zap,
  Globe,
  Database,
  Cpu,
  HardDrive,
  Network,
  Server,
  Receipt,
  Search,
  Filter,
  MoreHorizontal,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  Eye,
  Download,
  Bell,
  Shield,
  Gauge,
  PieChart as PieChartIcon,
  LineChart as LineChartIcon,
  BarChart as BarChartIcon,
  Calendar as CalendarIcon,
  Mail,
  Phone,
  MapPin,
  Star,
  Award,
  Percent
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'
import type { RootState, AppDispatch } from '../../store'
import { fetchDashboardLayout } from '../../store/slices/dashboardSlice'
import { dashboardAPI, DashboardStats } from '../../services/api'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
  RadialBarChart,
  RadialBar,
  Treemap,
  ScatterChart,
  Scatter
} from 'recharts'

interface AdminDashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    welcome: 'مرحباً',
    // Executive Dashboard
    executiveDashboard: 'لوحة التحكم التنفيذية',
    companyOverview: 'نظرة عامة على الشركة',
    keyPerformanceIndicators: 'مؤشرات الأداء الرئيسية',
    financialMetrics: 'المقاييس المالية',
    operationalMetrics: 'المقاييس التشغيلية',
    humanResourcesMetrics: 'مقاييس الموارد البشرية',
    businessIntelligence: 'ذكاء الأعمال',
    strategicInsights: 'رؤى استراتيجية',
    performanceTrends: 'اتجاهات الأداء',

    // Financial KPIs
    totalRevenue: 'إجمالي الإيرادات',
    monthlyRevenue: 'الإيرادات الشهرية',
    revenueGrowth: 'نمو الإيرادات',
    totalExpenses: 'إجمالي المصروفات',
    netProfit: 'صافي الربح',
    profitMargin: 'هامش الربح',
    operatingCashFlow: 'التدفق النقدي التشغيلي',
    returnOnInvestment: 'العائد على الاستثمار',
    budgetUtilization: 'استخدام الميزانية',
    costPerEmployee: 'التكلفة لكل موظف',

    // HR & Operations
    totalEmployees: 'إجمالي الموظفين',
    activeEmployees: 'الموظفون النشطون',
    newEmployees: 'الموظفون الجدد',
    employeeTurnover: 'معدل دوران الموظفين',
    employeeSatisfaction: 'رضا الموظفين',
    avgSalary: 'متوسط الراتب',
    totalDepartments: 'إجمالي الأقسام',
    attendanceRate: 'معدل الحضور',
    productivityIndex: 'مؤشر الإنتاجية',

    // Projects & Tasks
    totalProjects: 'إجمالي المشاريع',
    activeProjects: 'المشاريع النشطة',
    completedProjects: 'المشاريع المكتملة',
    projectSuccessRate: 'معدل نجاح المشاريع',
    pendingTasks: 'المهام المعلقة',
    completedTasks: 'المهام المكتملة',
    overdueTasks: 'المهام المتأخرة',

    // System & Security
    systemHealth: 'صحة النظام',
    systemUptime: 'وقت تشغيل النظام',
    serverPerformance: 'أداء الخادم',
    securityScore: 'نقاط الأمان',
    securityAlerts: 'تنبيهات الأمان',
    dataBackupStatus: 'حالة النسخ الاحتياطي',
    apiRequests: 'طلبات API',
    responseTime: 'وقت الاستجابة',
    errorRate: 'معدل الأخطاء',

    // Customer & Sales
    totalCustomers: 'إجمالي العملاء',
    activeCustomers: 'العملاء النشطون',
    newCustomers: 'العملاء الجدد',
    customerSatisfaction: 'رضا العملاء',
    salesConversion: 'معدل التحويل',
    averageOrderValue: 'متوسط قيمة الطلب',

    // Actions & Navigation
    refresh: 'تحديث',
    viewDetails: 'عرض التفاصيل',
    exportData: 'تصدير البيانات',
    generateReport: 'إنشاء تقرير',
    quickActions: 'الإجراءات السريعة',
    recentActivities: 'الأنشطة الأخيرة',
    manageEmployees: 'إدارة الموظفين',
    manageDepartments: 'إدارة الأقسام',
    systemSettings: 'إعدادات النظام',
    workflowAutomation: 'أتمتة سير العمل',
    reportGenerator: 'مولد التقارير',
    userManagement: 'إدارة المستخدمين',
    vendorManagement: 'إدارة الموردين',

    // Status & Trends
    excellent: 'ممتاز',
    good: 'جيد',
    average: 'متوسط',
    warning: 'تحذير',
    critical: 'حرج',
    increasing: 'متزايد',
    decreasing: 'متناقص',
    stable: 'مستقر',

    // Time Periods
    today: 'اليوم',
    thisWeek: 'هذا الأسبوع',
    thisMonth: 'هذا الشهر',
    thisQuarter: 'هذا الربع',
    thisYear: 'هذا العام',
    lastMonth: 'الشهر الماضي',
    lastQuarter: 'الربع الماضي',
    lastYear: 'العام الماضي'
  },
  en: {
    welcome: 'Welcome',
    // Executive Dashboard
    executiveDashboard: 'Executive Dashboard',
    companyOverview: 'Company Overview',
    keyPerformanceIndicators: 'Key Performance Indicators',
    financialMetrics: 'Financial Metrics',
    operationalMetrics: 'Operational Metrics',
    humanResourcesMetrics: 'Human Resources Metrics',
    businessIntelligence: 'Business Intelligence',
    strategicInsights: 'Strategic Insights',
    performanceTrends: 'Performance Trends',

    // Financial KPIs
    totalRevenue: 'Total Revenue',
    monthlyRevenue: 'Monthly Revenue',
    revenueGrowth: 'Revenue Growth',
    totalExpenses: 'Total Expenses',
    netProfit: 'Net Profit',
    profitMargin: 'Profit Margin',
    operatingCashFlow: 'Operating Cash Flow',
    returnOnInvestment: 'Return on Investment',
    budgetUtilization: 'Budget Utilization',
    costPerEmployee: 'Cost per Employee',

    // HR & Operations
    totalEmployees: 'Total Employees',
    activeEmployees: 'Active Employees',
    newEmployees: 'New Employees',
    employeeTurnover: 'Employee Turnover',
    employeeSatisfaction: 'Employee Satisfaction',
    avgSalary: 'Average Salary',
    totalDepartments: 'Total Departments',
    attendanceRate: 'Attendance Rate',
    productivityIndex: 'Productivity Index',

    // Projects & Tasks
    totalProjects: 'Total Projects',
    activeProjects: 'Active Projects',
    completedProjects: 'Completed Projects',
    projectSuccessRate: 'Project Success Rate',
    pendingTasks: 'Pending Tasks',
    completedTasks: 'Completed Tasks',
    overdueTasks: 'Overdue Tasks',

    // System & Security
    systemHealth: 'System Health',
    systemUptime: 'System Uptime',
    serverPerformance: 'Server Performance',
    securityScore: 'Security Score',
    securityAlerts: 'Security Alerts',
    dataBackupStatus: 'Data Backup Status',
    apiRequests: 'API Requests',
    responseTime: 'Response Time',
    errorRate: 'Error Rate',

    // Customer & Sales
    totalCustomers: 'Total Customers',
    activeCustomers: 'Active Customers',
    newCustomers: 'New Customers',
    customerSatisfaction: 'Customer Satisfaction',
    salesConversion: 'Sales Conversion',
    averageOrderValue: 'Average Order Value',

    // Actions & Navigation
    refresh: 'Refresh',
    viewDetails: 'View Details',
    exportData: 'Export Data',
    generateReport: 'Generate Report',
    quickActions: 'Quick Actions',
    recentActivities: 'Recent Activities',
    manageEmployees: 'Manage Employees',
    manageDepartments: 'Manage Departments',
    systemSettings: 'System Settings',
    workflowAutomation: 'Workflow Automation',
    reportGenerator: 'Report Generator',
    userManagement: 'User Management',
    vendorManagement: 'Vendor Management',

    // Status & Trends
    excellent: 'Excellent',
    good: 'Good',
    average: 'Average',
    warning: 'Warning',
    critical: 'Critical',
    increasing: 'Increasing',
    decreasing: 'Decreasing',
    stable: 'Stable',

    // Time Periods
    today: 'Today',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    thisQuarter: 'This Quarter',
    thisYear: 'This Year',
    lastMonth: 'Last Month',
    lastQuarter: 'Last Quarter',
    lastYear: 'Last Year'
  }
}

export default function AdminDashboard({ language }: AdminDashboardProps) {
  const dispatch = useDispatch<AppDispatch>()
  const { user } = useSelector((state: RootState) => state.auth)
  const { currentLayout, isLoading } = useSelector((state: RootState) => state.dashboard)
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Real dashboard data from API
  const [dashboardData, setDashboardData] = useState<DashboardStats | null>(null)
  const [dataLoading, setDataLoading] = useState(true)
  const [dataError, setDataError] = useState<string | null>(null)

  // Professional enterprise-level chart data
  const [chartData, setChartData] = useState({
    // Financial Performance - 12 months
    financialTrends: [
      { month: 'يناير', revenue: 850000, expenses: 620000, profit: 230000, growth: 12.5 },
      { month: 'فبراير', revenue: 920000, expenses: 650000, profit: 270000, growth: 15.2 },
      { month: 'مارس', revenue: 780000, expenses: 580000, profit: 200000, growth: 8.7 },
      { month: 'أبريل', revenue: 1100000, expenses: 720000, profit: 380000, growth: 22.1 },
      { month: 'مايو', revenue: 950000, expenses: 680000, profit: 270000, growth: 18.3 },
      { month: 'يونيو', revenue: 1200000, expenses: 750000, profit: 450000, growth: 25.8 }
    ],

    // Employee Analytics
    employeeMetrics: [
      { month: 'يناير', total: 145, newHires: 8, departures: 3, satisfaction: 87, productivity: 92 },
      { month: 'فبراير', total: 150, newHires: 12, departures: 7, satisfaction: 89, productivity: 94 },
      { month: 'مارس', total: 155, newHires: 10, departures: 5, satisfaction: 91, productivity: 96 },
      { month: 'أبريل', total: 162, newHires: 15, departures: 8, satisfaction: 88, productivity: 93 },
      { month: 'مايو', total: 169, newHires: 18, departures: 11, satisfaction: 92, productivity: 97 },
      { month: 'يونيو', total: 176, newHires: 14, departures: 7, satisfaction: 94, productivity: 98 }
    ],

    // Department Performance
    departmentPerformance: [
      { name: 'تقنية المعلومات', employees: 45, budget: 2500000, utilization: 87, performance: 94, projects: 12 },
      { name: 'المبيعات', employees: 32, budget: 1800000, utilization: 92, performance: 96, projects: 8 },
      { name: 'التسويق', employees: 28, budget: 1200000, utilization: 78, performance: 89, projects: 15 },
      { name: 'الموارد البشرية', employees: 18, budget: 800000, utilization: 85, performance: 91, projects: 6 },
      { name: 'المالية', employees: 22, budget: 900000, utilization: 91, performance: 93, projects: 4 },
      { name: 'العمليات', employees: 31, budget: 1500000, utilization: 89, performance: 92, projects: 9 }
    ],

    // Project Status Distribution
    projectStatus: [
      { name: 'مكتملة', value: 45, color: '#10b981', percentage: 62.5 },
      { name: 'قيد التنفيذ', value: 18, color: '#3b82f6', percentage: 25.0 },
      { name: 'متأخرة', value: 6, color: '#ef4444', percentage: 8.3 },
      { name: 'معلقة', value: 3, color: '#f59e0b', percentage: 4.2 }
    ],

    // Customer Satisfaction & Sales
    customerMetrics: [
      { quarter: 'Q1', satisfaction: 87, retention: 94, newCustomers: 156, revenue: 2850000 },
      { quarter: 'Q2', satisfaction: 91, retention: 96, newCustomers: 189, revenue: 3200000 },
      { quarter: 'Q3', satisfaction: 89, retention: 95, newCustomers: 167, revenue: 2950000 },
      { quarter: 'Q4', satisfaction: 93, retention: 97, newCustomers: 203, revenue: 3650000 }
    ],

    // System Performance & Security
    systemMetrics: [
      { metric: 'أداء الخادم', current: 94, target: 95, status: 'excellent' },
      { metric: 'أمان البيانات', current: 98, target: 99, status: 'excellent' },
      { metric: 'وقت التشغيل', current: 99.8, target: 99.9, status: 'excellent' },
      { metric: 'سرعة الاستجابة', current: 87, target: 90, status: 'good' },
      { metric: 'النسخ الاحتياطي', current: 100, target: 100, status: 'excellent' },
      { metric: 'مراقبة الأمان', current: 96, target: 98, status: 'good' }
    ],

    // KPI Trends
    kpiTrends: [
      { period: 'يناير', roi: 18.5, customerSat: 87, employeeSat: 89, efficiency: 92 },
      { period: 'فبراير', roi: 22.1, customerSat: 91, employeeSat: 91, efficiency: 94 },
      { period: 'مارس', roi: 19.8, customerSat: 89, employeeSat: 88, efficiency: 91 },
      { period: 'أبريل', roi: 25.3, customerSat: 93, employeeSat: 92, efficiency: 96 },
      { period: 'مايو', roi: 21.7, customerSat: 90, employeeSat: 94, efficiency: 95 },
      { period: 'يونيو', roi: 28.2, customerSat: 94, employeeSat: 96, efficiency: 98 }
    ]
  })

  // Load dashboard data from API
  const loadDashboardData = async () => {
    try {
      setDataLoading(true)
      setDataError(null)
      const data = await dashboardAPI.getStats()
      setDashboardData(data)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
      setDataError('فشل في تحميل بيانات لوحة التحكم')
    } finally {
      setDataLoading(false)
    }
  }

  useEffect(() => {
    if (user?.role.id === 'admin') {
      dispatch(fetchDashboardLayout('admin'))
      loadDashboardData()
    }
  }, [dispatch, user])

  // Auto-refresh data every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      if (!dataLoading) {
        loadDashboardData()
      }
    }, 300000) // 5 minutes

    return () => clearInterval(interval)
  }, [dataLoading])

  const handleRefresh = async () => {
    setRefreshing(true)
    await loadDashboardData()
    setRefreshing(false)
  }

  // Admin-level quick actions (limited compared to superadmin)
  const quickActions = [
    { title: t.manageEmployees, icon: Users, href: '/admin/employees', color: 'from-blue-500 to-blue-600' },
    { title: t.manageDepartments, icon: Building, href: '/admin/departments', color: 'from-green-500 to-green-600' },
    { title: t.businessIntelligence, icon: BarChart3, href: '/admin/business-intelligence', color: 'from-purple-500 to-purple-600' },
    { title: t.workflowAutomation, icon: Zap, href: '/admin/workflows', color: 'from-orange-500 to-orange-600' },
    { title: t.reportGenerator, icon: FileText, href: '/admin/report-generator', color: 'from-indigo-500 to-indigo-600' },
    { title: t.userManagement, icon: UserCheck, href: '/admin/users', color: 'from-cyan-500 to-cyan-600' },
    { title: t.vendorManagement, icon: Briefcase, href: '/admin/vendors', color: 'from-teal-500 to-teal-600' },
    { title: t.systemSettings, icon: Settings, href: '/admin/settings', color: 'from-gray-500 to-gray-600' }
  ]

  // Professional Executive KPI Cards
  const executiveKPIs = [
    // Financial KPIs
    {
      category: 'financial',
      title: t.totalRevenue,
      value: '12.5M',
      subValue: 'ر.س',
      change: '+18.3%',
      trend: 'up' as const,
      icon: DollarSign,
      color: 'from-emerald-500 to-emerald-600',
      description: 'مقارنة بالشهر الماضي'
    },
    {
      category: 'financial',
      title: t.netProfit,
      value: '3.2M',
      subValue: 'ر.س',
      change: '+25.8%',
      trend: 'up' as const,
      icon: TrendingUp,
      color: 'from-green-500 to-green-600',
      description: 'هامش ربح 25.6%'
    },
    {
      category: 'financial',
      title: t.returnOnInvestment,
      value: '28.2%',
      subValue: 'ROI',
      change: '+4.1%',
      trend: 'up' as const,
      icon: Target,
      color: 'from-blue-500 to-blue-600',
      description: 'أداء ممتاز'
    },

    // HR & Operations KPIs
    {
      category: 'hr',
      title: t.totalEmployees,
      value: dashboardData?.total_employees?.toString() || '176',
      subValue: 'موظف',
      change: '+14',
      trend: 'up' as const,
      icon: Users,
      color: 'from-purple-500 to-purple-600',
      description: 'نمو 8.6% شهرياً'
    },
    {
      category: 'hr',
      title: t.employeeSatisfaction,
      value: '94%',
      subValue: 'رضا',
      change: '+2%',
      trend: 'up' as const,
      icon: Star,
      color: 'from-yellow-500 to-yellow-600',
      description: 'تحسن مستمر'
    },
    {
      category: 'hr',
      title: t.productivityIndex,
      value: '98%',
      subValue: 'إنتاجية',
      change: '+5%',
      trend: 'up' as const,
      icon: Award,
      color: 'from-indigo-500 to-indigo-600',
      description: 'أعلى من المتوقع'
    },

    // Projects & Operations
    {
      category: 'operations',
      title: t.activeProjects,
      value: dashboardData?.active_projects?.toString() || '18',
      subValue: 'مشروع',
      change: '+3',
      trend: 'up' as const,
      icon: Briefcase,
      color: 'from-cyan-500 to-cyan-600',
      description: '72 مشروع إجمالي'
    },
    {
      category: 'operations',
      title: t.projectSuccessRate,
      value: '96.2%',
      subValue: 'نجاح',
      change: '+1.8%',
      trend: 'up' as const,
      icon: CheckCircle,
      color: 'from-teal-500 to-teal-600',
      description: 'معدل ممتاز'
    },

    // System & Security
    {
      category: 'system',
      title: t.systemUptime,
      value: '99.8%',
      subValue: 'تشغيل',
      change: '+0.1%',
      trend: 'up' as const,
      icon: Server,
      color: 'from-slate-500 to-slate-600',
      description: 'استقرار عالي'
    },
    {
      category: 'system',
      title: t.securityScore,
      value: '98%',
      subValue: 'أمان',
      change: 'مستقر',
      trend: 'stable' as const,
      icon: Shield,
      color: 'from-red-500 to-red-600',
      description: 'حماية متقدمة'
    },

    // Customer Metrics
    {
      category: 'customer',
      title: t.customerSatisfaction,
      value: '94%',
      subValue: 'رضا العملاء',
      change: '+3%',
      trend: 'up' as const,
      icon: Star,
      color: 'from-pink-500 to-pink-600',
      description: 'تقييم ممتاز'
    },
    {
      category: 'customer',
      title: t.salesConversion,
      value: '23.5%',
      subValue: 'تحويل',
      change: '+2.1%',
      trend: 'up' as const,
      icon: Percent,
      color: 'from-orange-500 to-orange-600',
      description: 'أداء قوي'
    }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'user_management',
      message: 'New employee added to HR department',
      messageAr: 'تم إضافة موظف جديد لقسم الموارد البشرية',
      timestamp: '5 minutes ago',
      icon: Users,
      severity: 'info'
    },
    {
      id: 2,
      type: 'project_update',
      message: 'Project milestone completed',
      messageAr: 'تم إكمال معلم مشروع',
      timestamp: '15 minutes ago',
      icon: CheckCircle,
      severity: 'success'
    },
    {
      id: 3,
      type: 'system_update',
      message: 'Department budget updated',
      messageAr: 'تم تحديث ميزانية القسم',
      timestamp: '30 minutes ago',
      icon: DollarSign,
      severity: 'info'
    },
    {
      id: 4,
      type: 'workflow',
      message: 'Workflow automation completed',
      messageAr: 'تم إكمال أتمتة سير العمل',
      timestamp: '1 hour ago',
      icon: Zap,
      severity: 'success'
    }
  ]

  return (
    <div className={`space-y-8 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Executive Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
        <div>
          <h1 className="text-4xl font-bold text-white mb-3">
            {t.executiveDashboard}
          </h1>
          <p className="text-white/80 text-lg">
            {t.welcome}, {user?.first_name} {user?.last_name} - {t.companyOverview}
          </p>
          <div className="flex items-center gap-4 mt-2">
            <Badge variant="outline" className="text-white border-white/20 bg-white/10">
              <Clock className="h-3 w-3 mr-1" />
              آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}
            </Badge>
            <Badge variant="outline" className="text-green-400 border-green-400/20 bg-green-400/10">
              <CheckCircle className="h-3 w-3 mr-1" />
              النظام يعمل بكفاءة
            </Badge>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button bg-white/10 hover:bg-white/20"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button variant="outline" className="glass-button border-white/20">
            <Download className="h-4 w-4 mr-2" />
            {t.exportData}
          </Button>
          <Button variant="outline" className="glass-button border-white/20">
            <FileText className="h-4 w-4 mr-2" />
            {t.generateReport}
          </Button>
        </div>
      </div>

      {/* Error State */}
      {dataError && (
        <Card className="glass-card border-red-500/20 bg-red-500/10">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <p className="text-red-400">{dataError}</p>
              <Button
                onClick={loadDashboardData}
                variant="outline"
                size="sm"
                className="mr-auto text-red-400 border-red-400/20"
              >
                إعادة المحاولة
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Executive KPI Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {executiveKPIs.map((kpi, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/30 transition-all duration-300 group">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className={`p-3 rounded-xl bg-gradient-to-r ${kpi.color} group-hover:scale-110 transition-transform duration-300`}>
                  <kpi.icon className="h-6 w-6 text-white" />
                </div>
                <div className="flex items-center gap-1">
                  {kpi.trend === 'up' && <ArrowUpRight className="h-4 w-4 text-green-400" />}
                  {kpi.trend === 'down' && <ArrowDownRight className="h-4 w-4 text-red-400" />}
                  {kpi.trend === 'stable' && <Minus className="h-4 w-4 text-yellow-400" />}
                  <span className={`text-sm font-medium ${
                    kpi.trend === 'up' ? 'text-green-400' :
                    kpi.trend === 'down' ? 'text-red-400' : 'text-yellow-400'
                  }`}>
                    {kpi.change}
                  </span>
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-white/70 text-sm font-medium">{kpi.title}</p>
                <div className="flex items-baseline gap-2">
                  <p className="text-3xl font-bold text-white">{kpi.value}</p>
                  <p className="text-white/50 text-sm">{kpi.subValue}</p>
                </div>
                <p className="text-white/60 text-xs">{kpi.description}</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Professional Analytics Section */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* Financial Performance - Large Chart */}
        <Card className="xl:col-span-2 glass-card border-white/20">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-white text-xl">{t.financialMetrics}</CardTitle>
                <CardDescription className="text-white/70">
                  الأداء المالي والنمو على مدار 6 أشهر
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-green-400 border-green-400/20">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +18.3% نمو
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <ComposedChart data={chartData.financialTrends}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="month" stroke="rgba(255,255,255,0.7)" />
                  <YAxis stroke="rgba(255,255,255,0.7)" />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.9)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '12px'
                    }}
                  />
                  <Legend />
                  <Bar dataKey="revenue" fill="#10b981" name="الإيرادات" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="expenses" fill="#ef4444" name="المصروفات" radius={[4, 4, 0, 0]} />
                  <Line type="monotone" dataKey="profit" stroke="#3b82f6" strokeWidth={3} name="صافي الربح" />
                  <Line type="monotone" dataKey="growth" stroke="#f59e0b" strokeWidth={2} name="معدل النمو %" />
                </ComposedChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Project Status Distribution */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white">حالة المشاريع</CardTitle>
            <CardDescription className="text-white/70">
              توزيع المشاريع حسب الحالة
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={chartData.projectStatus}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={120}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {chartData.projectStatus.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.9)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px'
                    }}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 space-y-2">
              {chartData.projectStatus.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full`} style={{ backgroundColor: item.color }}></div>
                    <span className="text-white/80 text-sm">{item.name}</span>
                  </div>
                  <span className="text-white font-medium">{item.value}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Department Performance & HR Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Department Performance */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white">أداء الأقسام</CardTitle>
            <CardDescription className="text-white/70">
              مقاييس الأداء والميزانية لكل قسم
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData.departmentPerformance} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis type="number" stroke="rgba(255,255,255,0.7)" />
                  <YAxis dataKey="name" type="category" stroke="rgba(255,255,255,0.7)" width={100} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.9)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px'
                    }}
                  />
                  <Legend />
                  <Bar dataKey="performance" fill="#10b981" name="الأداء %" radius={[0, 4, 4, 0]} />
                  <Bar dataKey="utilization" fill="#3b82f6" name="استخدام الميزانية %" radius={[0, 4, 4, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Employee Metrics Trends */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white">مقاييس الموظفين</CardTitle>
            <CardDescription className="text-white/70">
              الرضا والإنتاجية على مدار 6 أشهر
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData.employeeMetrics}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="month" stroke="rgba(255,255,255,0.7)" />
                  <YAxis stroke="rgba(255,255,255,0.7)" />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.9)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px'
                    }}
                  />
                  <Legend />
                  <Line type="monotone" dataKey="satisfaction" stroke="#f59e0b" strokeWidth={3} name="رضا الموظفين %" />
                  <Line type="monotone" dataKey="productivity" stroke="#10b981" strokeWidth={3} name="الإنتاجية %" />
                  <Line type="monotone" dataKey="total" stroke="#3b82f6" strokeWidth={2} name="إجمالي الموظفين" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* KPI Performance Trends */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-white text-xl">{t.keyPerformanceIndicators}</CardTitle>
              <CardDescription className="text-white/70">
                اتجاهات مؤشرات الأداء الرئيسية
              </CardDescription>
            </div>
            <Button variant="outline" className="glass-button border-white/20">
              <Eye className="h-4 w-4 mr-2" />
              {t.viewDetails}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData.kpiTrends}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                <XAxis dataKey="period" stroke="rgba(255,255,255,0.7)" />
                <YAxis stroke="rgba(255,255,255,0.7)" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(0,0,0,0.9)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '12px'
                  }}
                />
                <Legend />
                <Line type="monotone" dataKey="roi" stroke="#10b981" strokeWidth={3} name="العائد على الاستثمار %" />
                <Line type="monotone" dataKey="customerSat" stroke="#3b82f6" strokeWidth={3} name="رضا العملاء %" />
                <Line type="monotone" dataKey="employeeSat" stroke="#f59e0b" strokeWidth={3} name="رضا الموظفين %" />
                <Line type="monotone" dataKey="efficiency" stroke="#8b5cf6" strokeWidth={3} name="الكفاءة التشغيلية %" />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Executive Actions & Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Strategic Quick Actions */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white">{t.quickActions}</CardTitle>
            <CardDescription className="text-white/70">
              إجراءات استراتيجية للإدارة التنفيذية
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {quickActions.map((action, index) => (
                <Link key={index} to={action.href}>
                  <Button
                    variant="outline"
                    className={`w-full h-24 flex flex-col items-center justify-center gap-3 glass-button bg-gradient-to-r ${action.color} hover:scale-105 transition-all duration-300 border-white/20`}
                  >
                    <action.icon className="h-7 w-7" />
                    <span className="text-sm text-center font-medium">{action.title}</span>
                  </Button>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Executive Activities Feed */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white">{t.recentActivities}</CardTitle>
              <Button variant="ghost" size="sm" className="text-white/70 hover:text-white">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start gap-4 p-4 rounded-xl bg-white/5 hover:bg-white/10 transition-colors">
                  <div className={`p-2 rounded-lg ${
                    activity.severity === 'success' ? 'bg-green-500/20 text-green-400' :
                    activity.severity === 'warning' ? 'bg-yellow-500/20 text-yellow-400' :
                    'bg-blue-500/20 text-blue-400'
                  }`}>
                    <activity.icon className="h-5 w-5" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-white text-sm font-medium">
                      {language === 'ar' ? activity.messageAr : activity.message}
                    </p>
                    <p className="text-white/50 text-xs mt-1">{activity.timestamp}</p>
                  </div>
                  <Badge
                    variant="outline"
                    className={`${
                      activity.severity === 'success' ? 'text-green-400 border-green-400/20' :
                      activity.severity === 'warning' ? 'text-yellow-400 border-yellow-400/20' :
                      'text-blue-400 border-blue-400/20'
                    }`}
                  >
                    {activity.severity}
                  </Badge>
                </div>
              ))}
            </div>
            <div className="mt-6 pt-4 border-t border-white/10">
              <Button variant="outline" className="w-full glass-button border-white/20">
                <Activity className="h-4 w-4 mr-2" />
                عرض جميع الأنشطة
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Health Summary */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white">{t.systemHealth}</CardTitle>
          <CardDescription className="text-white/70">
            مراقبة شاملة لصحة النظام والأمان
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {chartData.systemMetrics.map((metric, index) => (
              <div key={index} className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-white/80 text-sm font-medium">{metric.metric}</span>
                  <Badge
                    variant="outline"
                    className={`${
                      metric.status === 'excellent' ? 'text-green-400 border-green-400/20' :
                      metric.status === 'good' ? 'text-blue-400 border-blue-400/20' :
                      'text-yellow-400 border-yellow-400/20'
                    }`}
                  >
                    {metric.status === 'excellent' ? 'ممتاز' : metric.status === 'good' ? 'جيد' : 'متوسط'}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-white/60">الحالي</span>
                    <span className="text-white font-medium">{metric.current}%</span>
                  </div>
                  <Progress
                    value={metric.current}
                    className="h-2 bg-white/10"
                  />
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-white/50">الهدف: {metric.target}%</span>
                    <span className={`${
                      metric.current >= metric.target ? 'text-green-400' : 'text-yellow-400'
                    }`}>
                      {metric.current >= metric.target ? '✓ تم تحقيقه' : '⚠ تحت الهدف'}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
