import React, { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import {
  Users,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Activity,
  BarChart3,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  UserCheck,
  Building,
  Briefcase,
  Calendar,
  FileText,
  Target,
  Zap,
  Globe,
  Database,
  Cpu,
  HardDrive,
  Network,
  Server,
  Receipt,
  Search,
  Filter,
  MoreHorizontal,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  Eye,
  Download,
  Bell,
  Shield,
  Gauge,
  PieChart as PieChartIcon,
  LineChart as LineChartIcon,
  BarChart as BarChartIcon,
  Calendar as CalendarIcon,
  Mail,
  Phone,
  MapPin,
  Star,
  Award,
  Percent
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'
import type { RootState, AppDispatch } from '../../store'
import { fetchDashboardLayout } from '../../store/slices/dashboardSlice'
import { dashboardAPI, DashboardStats } from '../../services/api'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
  RadialBarChart,
  RadialBar,
  Treemap,
  ScatterChart,
  Scatter
} from 'recharts'

interface AdminDashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    welcome: 'مرحباً',
    // Executive Dashboard
    executiveDashboard: 'لوحة التحكم التنفيذية',
    companyOverview: 'نظرة عامة على الشركة',
    keyPerformanceIndicators: 'مؤشرات الأداء الرئيسية',
    financialMetrics: 'المقاييس المالية',
    operationalMetrics: 'المقاييس التشغيلية',
    humanResourcesMetrics: 'مقاييس الموارد البشرية',
    businessIntelligence: 'ذكاء الأعمال',
    strategicInsights: 'رؤى استراتيجية',
    performanceTrends: 'اتجاهات الأداء',

    // Financial KPIs
    totalRevenue: 'إجمالي الإيرادات',
    monthlyRevenue: 'الإيرادات الشهرية',
    revenueGrowth: 'نمو الإيرادات',
    totalExpenses: 'إجمالي المصروفات',
    netProfit: 'صافي الربح',
    profitMargin: 'هامش الربح',
    operatingCashFlow: 'التدفق النقدي التشغيلي',
    returnOnInvestment: 'العائد على الاستثمار',
    budgetUtilization: 'استخدام الميزانية',
    costPerEmployee: 'التكلفة لكل موظف',

    // HR & Operations
    totalEmployees: 'إجمالي الموظفين',
    activeEmployees: 'الموظفون النشطون',
    newEmployees: 'الموظفون الجدد',
    employeeTurnover: 'معدل دوران الموظفين',
    employeeSatisfaction: 'رضا الموظفين',
    avgSalary: 'متوسط الراتب',
    totalDepartments: 'إجمالي الأقسام',
    attendanceRate: 'معدل الحضور',
    productivityIndex: 'مؤشر الإنتاجية',

    // Projects & Tasks
    totalProjects: 'إجمالي المشاريع',
    activeProjects: 'المشاريع النشطة',
    completedProjects: 'المشاريع المكتملة',
    projectSuccessRate: 'معدل نجاح المشاريع',
    pendingTasks: 'المهام المعلقة',
    completedTasks: 'المهام المكتملة',
    overdueTasks: 'المهام المتأخرة',

    // System & Security
    systemHealth: 'صحة النظام',
    systemUptime: 'وقت تشغيل النظام',
    serverPerformance: 'أداء الخادم',
    securityScore: 'نقاط الأمان',
    securityAlerts: 'تنبيهات الأمان',
    dataBackupStatus: 'حالة النسخ الاحتياطي',
    apiRequests: 'طلبات API',
    responseTime: 'وقت الاستجابة',
    errorRate: 'معدل الأخطاء',

    // Customer & Sales
    totalCustomers: 'إجمالي العملاء',
    activeCustomers: 'العملاء النشطون',
    newCustomers: 'العملاء الجدد',
    customerSatisfaction: 'رضا العملاء',
    salesConversion: 'معدل التحويل',
    averageOrderValue: 'متوسط قيمة الطلب',

    // Actions & Navigation
    refresh: 'تحديث',
    viewDetails: 'عرض التفاصيل',
    exportData: 'تصدير البيانات',
    generateReport: 'إنشاء تقرير',
    quickActions: 'الإجراءات السريعة',
    recentActivities: 'الأنشطة الأخيرة',
    manageEmployees: 'إدارة الموظفين',
    manageDepartments: 'إدارة الأقسام',
    systemSettings: 'إعدادات النظام',
    workflowAutomation: 'أتمتة سير العمل',
    reportGenerator: 'مولد التقارير',
    userManagement: 'إدارة المستخدمين',
    vendorManagement: 'إدارة الموردين',

    // Status & Trends
    excellent: 'ممتاز',
    good: 'جيد',
    average: 'متوسط',
    warning: 'تحذير',
    critical: 'حرج',
    increasing: 'متزايد',
    decreasing: 'متناقص',
    stable: 'مستقر',

    // Time Periods
    today: 'اليوم',
    thisWeek: 'هذا الأسبوع',
    thisMonth: 'هذا الشهر',
    thisQuarter: 'هذا الربع',
    thisYear: 'هذا العام',
    lastMonth: 'الشهر الماضي',
    lastQuarter: 'الربع الماضي',
    lastYear: 'العام الماضي'
  },
  en: {
    welcome: 'Welcome',
    // Executive Dashboard
    executiveDashboard: 'Executive Dashboard',
    companyOverview: 'Company Overview',
    keyPerformanceIndicators: 'Key Performance Indicators',
    financialMetrics: 'Financial Metrics',
    operationalMetrics: 'Operational Metrics',
    humanResourcesMetrics: 'Human Resources Metrics',
    businessIntelligence: 'Business Intelligence',
    strategicInsights: 'Strategic Insights',
    performanceTrends: 'Performance Trends',

    // Financial KPIs
    totalRevenue: 'Total Revenue',
    monthlyRevenue: 'Monthly Revenue',
    revenueGrowth: 'Revenue Growth',
    totalExpenses: 'Total Expenses',
    netProfit: 'Net Profit',
    profitMargin: 'Profit Margin',
    operatingCashFlow: 'Operating Cash Flow',
    returnOnInvestment: 'Return on Investment',
    budgetUtilization: 'Budget Utilization',
    costPerEmployee: 'Cost per Employee',

    // HR & Operations
    totalEmployees: 'Total Employees',
    activeEmployees: 'Active Employees',
    newEmployees: 'New Employees',
    employeeTurnover: 'Employee Turnover',
    employeeSatisfaction: 'Employee Satisfaction',
    avgSalary: 'Average Salary',
    totalDepartments: 'Total Departments',
    attendanceRate: 'Attendance Rate',
    productivityIndex: 'Productivity Index',

    // Projects & Tasks
    totalProjects: 'Total Projects',
    activeProjects: 'Active Projects',
    completedProjects: 'Completed Projects',
    projectSuccessRate: 'Project Success Rate',
    pendingTasks: 'Pending Tasks',
    completedTasks: 'Completed Tasks',
    overdueTasks: 'Overdue Tasks',

    // System & Security
    systemHealth: 'System Health',
    systemUptime: 'System Uptime',
    serverPerformance: 'Server Performance',
    securityScore: 'Security Score',
    securityAlerts: 'Security Alerts',
    dataBackupStatus: 'Data Backup Status',
    apiRequests: 'API Requests',
    responseTime: 'Response Time',
    errorRate: 'Error Rate',

    // Customer & Sales
    totalCustomers: 'Total Customers',
    activeCustomers: 'Active Customers',
    newCustomers: 'New Customers',
    customerSatisfaction: 'Customer Satisfaction',
    salesConversion: 'Sales Conversion',
    averageOrderValue: 'Average Order Value',

    // Actions & Navigation
    refresh: 'Refresh',
    viewDetails: 'View Details',
    exportData: 'Export Data',
    generateReport: 'Generate Report',
    quickActions: 'Quick Actions',
    recentActivities: 'Recent Activities',
    manageEmployees: 'Manage Employees',
    manageDepartments: 'Manage Departments',
    systemSettings: 'System Settings',
    workflowAutomation: 'Workflow Automation',
    reportGenerator: 'Report Generator',
    userManagement: 'User Management',
    vendorManagement: 'Vendor Management',

    // Status & Trends
    excellent: 'Excellent',
    good: 'Good',
    average: 'Average',
    warning: 'Warning',
    critical: 'Critical',
    increasing: 'Increasing',
    decreasing: 'Decreasing',
    stable: 'Stable',

    // Time Periods
    today: 'Today',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    thisQuarter: 'This Quarter',
    thisYear: 'This Year',
    lastMonth: 'Last Month',
    lastQuarter: 'Last Quarter',
    lastYear: 'Last Year'
  }
}

export default function AdminDashboard({ language }: AdminDashboardProps) {
  const dispatch = useDispatch<AppDispatch>()
  const { user } = useSelector((state: RootState) => state.auth)
  const { currentLayout, isLoading } = useSelector((state: RootState) => state.dashboard)
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Real dashboard data from API
  const [dashboardData, setDashboardData] = useState<DashboardStats | null>(null)
  const [dataLoading, setDataLoading] = useState(true)
  const [dataError, setDataError] = useState<string | null>(null)

  // Professional enterprise-level chart data
  const [chartData, setChartData] = useState({
    // Financial Performance - 12 months
    financialTrends: [
      { month: 'يناير', revenue: 850000, expenses: 620000, profit: 230000, growth: 12.5 },
      { month: 'فبراير', revenue: 920000, expenses: 650000, profit: 270000, growth: 15.2 },
      { month: 'مارس', revenue: 780000, expenses: 580000, profit: 200000, growth: 8.7 },
      { month: 'أبريل', revenue: 1100000, expenses: 720000, profit: 380000, growth: 22.1 },
      { month: 'مايو', revenue: 950000, expenses: 680000, profit: 270000, growth: 18.3 },
      { month: 'يونيو', revenue: 1200000, expenses: 750000, profit: 450000, growth: 25.8 }
    ],

    // Employee Analytics
    employeeMetrics: [
      { month: 'يناير', total: 145, newHires: 8, departures: 3, satisfaction: 87, productivity: 92 },
      { month: 'فبراير', total: 150, newHires: 12, departures: 7, satisfaction: 89, productivity: 94 },
      { month: 'مارس', total: 155, newHires: 10, departures: 5, satisfaction: 91, productivity: 96 },
      { month: 'أبريل', total: 162, newHires: 15, departures: 8, satisfaction: 88, productivity: 93 },
      { month: 'مايو', total: 169, newHires: 18, departures: 11, satisfaction: 92, productivity: 97 },
      { month: 'يونيو', total: 176, newHires: 14, departures: 7, satisfaction: 94, productivity: 98 }
    ],

    // Department Performance
    departmentPerformance: [
      { name: 'تقنية المعلومات', employees: 45, budget: 2500000, utilization: 87, performance: 94, projects: 12 },
      { name: 'المبيعات', employees: 32, budget: 1800000, utilization: 92, performance: 96, projects: 8 },
      { name: 'التسويق', employees: 28, budget: 1200000, utilization: 78, performance: 89, projects: 15 },
      { name: 'الموارد البشرية', employees: 18, budget: 800000, utilization: 85, performance: 91, projects: 6 },
      { name: 'المالية', employees: 22, budget: 900000, utilization: 91, performance: 93, projects: 4 },
      { name: 'العمليات', employees: 31, budget: 1500000, utilization: 89, performance: 92, projects: 9 }
    ],

    // Project Status Distribution
    projectStatus: [
      { name: 'مكتملة', value: 45, color: '#10b981', percentage: 62.5 },
      { name: 'قيد التنفيذ', value: 18, color: '#3b82f6', percentage: 25.0 },
      { name: 'متأخرة', value: 6, color: '#ef4444', percentage: 8.3 },
      { name: 'معلقة', value: 3, color: '#f59e0b', percentage: 4.2 }
    ],

    // Customer Satisfaction & Sales
    customerMetrics: [
      { quarter: 'Q1', satisfaction: 87, retention: 94, newCustomers: 156, revenue: 2850000 },
      { quarter: 'Q2', satisfaction: 91, retention: 96, newCustomers: 189, revenue: 3200000 },
      { quarter: 'Q3', satisfaction: 89, retention: 95, newCustomers: 167, revenue: 2950000 },
      { quarter: 'Q4', satisfaction: 93, retention: 97, newCustomers: 203, revenue: 3650000 }
    ],

    // System Performance & Security
    systemMetrics: [
      { metric: 'أداء الخادم', current: 94, target: 95, status: 'excellent' },
      { metric: 'أمان البيانات', current: 98, target: 99, status: 'excellent' },
      { metric: 'وقت التشغيل', current: 99.8, target: 99.9, status: 'excellent' },
      { metric: 'سرعة الاستجابة', current: 87, target: 90, status: 'good' },
      { metric: 'النسخ الاحتياطي', current: 100, target: 100, status: 'excellent' },
      { metric: 'مراقبة الأمان', current: 96, target: 98, status: 'good' }
    ],

    // KPI Trends
    kpiTrends: [
      { period: 'يناير', roi: 18.5, customerSat: 87, employeeSat: 89, efficiency: 92 },
      { period: 'فبراير', roi: 22.1, customerSat: 91, employeeSat: 91, efficiency: 94 },
      { period: 'مارس', roi: 19.8, customerSat: 89, employeeSat: 88, efficiency: 91 },
      { period: 'أبريل', roi: 25.3, customerSat: 93, employeeSat: 92, efficiency: 96 },
      { period: 'مايو', roi: 21.7, customerSat: 90, employeeSat: 94, efficiency: 95 },
      { period: 'يونيو', roi: 28.2, customerSat: 94, employeeSat: 96, efficiency: 98 }
    ]
  })

  // Load dashboard data from API
  const loadDashboardData = async () => {
    try {
      setDataLoading(true)
      setDataError(null)
      const data = await dashboardAPI.getStats()
      setDashboardData(data)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
      setDataError('فشل في تحميل بيانات لوحة التحكم')
    } finally {
      setDataLoading(false)
    }
  }

  useEffect(() => {
    if (user?.role.id === 'admin') {
      dispatch(fetchDashboardLayout('admin'))
      loadDashboardData()
    }
  }, [dispatch, user])

  // Auto-refresh data every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      if (!dataLoading) {
        loadDashboardData()
      }
    }, 300000) // 5 minutes

    return () => clearInterval(interval)
  }, [dataLoading])

  const handleRefresh = async () => {
    setRefreshing(true)
    await loadDashboardData()
    setRefreshing(false)
  }

  // Admin-level quick actions (limited compared to superadmin)
  const quickActions = [
    { title: t.manageEmployees, icon: Users, href: '/admin/employees', color: 'from-blue-500 to-blue-600' },
    { title: t.manageDepartments, icon: Building, href: '/admin/departments', color: 'from-green-500 to-green-600' },
    { title: t.businessIntelligence, icon: BarChart3, href: '/admin/business-intelligence', color: 'from-purple-500 to-purple-600' },
    { title: t.workflowAutomation, icon: Zap, href: '/admin/workflows', color: 'from-orange-500 to-orange-600' },
    { title: t.reportGenerator, icon: FileText, href: '/admin/report-generator', color: 'from-indigo-500 to-indigo-600' },
    { title: t.userManagement, icon: UserCheck, href: '/admin/users', color: 'from-cyan-500 to-cyan-600' },
    { title: t.vendorManagement, icon: Briefcase, href: '/admin/vendors', color: 'from-teal-500 to-teal-600' },
    { title: t.systemSettings, icon: Settings, href: '/admin/settings', color: 'from-gray-500 to-gray-600' }
  ]

  // Professional Executive KPI Cards
  const executiveKPIs = [
    // Financial KPIs
    {
      category: 'financial',
      title: t.totalRevenue,
      value: '12.5M',
      subValue: 'ر.س',
      change: '+18.3%',
      trend: 'up' as const,
      icon: DollarSign,
      color: 'from-emerald-500 to-emerald-600',
      description: 'مقارنة بالشهر الماضي'
    },
    {
      category: 'financial',
      title: t.netProfit,
      value: '3.2M',
      subValue: 'ر.س',
      change: '+25.8%',
      trend: 'up' as const,
      icon: TrendingUp,
      color: 'from-green-500 to-green-600',
      description: 'هامش ربح 25.6%'
    },
    {
      category: 'financial',
      title: t.returnOnInvestment,
      value: '28.2%',
      subValue: 'ROI',
      change: '+4.1%',
      trend: 'up' as const,
      icon: Target,
      color: 'from-blue-500 to-blue-600',
      description: 'أداء ممتاز'
    },

    // HR & Operations KPIs
    {
      category: 'hr',
      title: t.totalEmployees,
      value: dashboardData?.total_employees?.toString() || '176',
      subValue: 'موظف',
      change: '+14',
      trend: 'up' as const,
      icon: Users,
      color: 'from-purple-500 to-purple-600',
      description: 'نمو 8.6% شهرياً'
    },
    {
      category: 'hr',
      title: t.employeeSatisfaction,
      value: '94%',
      subValue: 'رضا',
      change: '+2%',
      trend: 'up' as const,
      icon: Star,
      color: 'from-yellow-500 to-yellow-600',
      description: 'تحسن مستمر'
    },
    {
      category: 'hr',
      title: t.productivityIndex,
      value: '98%',
      subValue: 'إنتاجية',
      change: '+5%',
      trend: 'up' as const,
      icon: Award,
      color: 'from-indigo-500 to-indigo-600',
      description: 'أعلى من المتوقع'
    },

    // Projects & Operations
    {
      category: 'operations',
      title: t.activeProjects,
      value: dashboardData?.active_projects?.toString() || '18',
      subValue: 'مشروع',
      change: '+3',
      trend: 'up' as const,
      icon: Briefcase,
      color: 'from-cyan-500 to-cyan-600',
      description: '72 مشروع إجمالي'
    },
    {
      category: 'operations',
      title: t.projectSuccessRate,
      value: '96.2%',
      subValue: 'نجاح',
      change: '+1.8%',
      trend: 'up' as const,
      icon: CheckCircle,
      color: 'from-teal-500 to-teal-600',
      description: 'معدل ممتاز'
    },

    // System & Security
    {
      category: 'system',
      title: t.systemUptime,
      value: '99.8%',
      subValue: 'تشغيل',
      change: '+0.1%',
      trend: 'up' as const,
      icon: Server,
      color: 'from-slate-500 to-slate-600',
      description: 'استقرار عالي'
    },
    {
      category: 'system',
      title: t.securityScore,
      value: '98%',
      subValue: 'أمان',
      change: 'مستقر',
      trend: 'stable' as const,
      icon: Shield,
      color: 'from-red-500 to-red-600',
      description: 'حماية متقدمة'
    },

    // Customer Metrics
    {
      category: 'customer',
      title: t.customerSatisfaction,
      value: '94%',
      subValue: 'رضا العملاء',
      change: '+3%',
      trend: 'up' as const,
      icon: Star,
      color: 'from-pink-500 to-pink-600',
      description: 'تقييم ممتاز'
    },
    {
      category: 'customer',
      title: t.salesConversion,
      value: '23.5%',
      subValue: 'تحويل',
      change: '+2.1%',
      trend: 'up' as const,
      icon: Percent,
      color: 'from-orange-500 to-orange-600',
      description: 'أداء قوي'
    }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'user_management',
      message: 'New employee added to HR department',
      messageAr: 'تم إضافة موظف جديد لقسم الموارد البشرية',
      timestamp: '5 minutes ago',
      icon: Users,
      severity: 'info'
    },
    {
      id: 2,
      type: 'project_update',
      message: 'Project milestone completed',
      messageAr: 'تم إكمال معلم مشروع',
      timestamp: '15 minutes ago',
      icon: CheckCircle,
      severity: 'success'
    },
    {
      id: 3,
      type: 'system_update',
      message: 'Department budget updated',
      messageAr: 'تم تحديث ميزانية القسم',
      timestamp: '30 minutes ago',
      icon: DollarSign,
      severity: 'info'
    },
    {
      id: 4,
      type: 'workflow',
      message: 'Workflow automation completed',
      messageAr: 'تم إكمال أتمتة سير العمل',
      timestamp: '1 hour ago',
      icon: Zap,
      severity: 'success'
    }
  ]

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Professional Executive Header */}
      <div className="bg-gradient-to-r from-slate-800/50 to-purple-800/50 backdrop-blur-xl border-b border-white/10 p-6">
        <div className="flex flex-col xl:flex-row xl:items-center justify-between gap-6">
          <div className="flex items-center gap-6">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-2xl">
              <BarChart3 className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent mb-2">
                لوحة التحكم التنفيذية
              </h1>
              <p className="text-white/80 text-lg font-medium">
                مرحباً {user?.first_name} {user?.last_name} - المدير التنفيذي
              </p>
              <div className="flex items-center gap-4 mt-3">
                <Badge className="bg-green-500/20 text-green-400 border-green-500/30 px-3 py-1">
                  <Activity className="h-3 w-3 mr-1" />
                  النظام متصل
                </Badge>
                <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30 px-3 py-1">
                  <Clock className="h-3 w-3 mr-1" />
                  {new Date().toLocaleDateString('ar-SA')}
                </Badge>
                <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30 px-3 py-1">
                  <Shield className="h-3 w-3 mr-1" />
                  أمان عالي
                </Badge>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Button
              onClick={handleRefresh}
              disabled={refreshing}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              تحديث البيانات
            </Button>
            <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
              <Download className="h-4 w-4 mr-2" />
              تصدير التقرير
            </Button>
            <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
              <Settings className="h-4 w-4 mr-2" />
              الإعدادات
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-8">

        {/* Real-Time Business Intelligence Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Primary Financial KPI */}
          <Card className="lg:col-span-2 bg-gradient-to-br from-emerald-900/40 to-green-800/40 backdrop-blur-xl border border-emerald-500/30 shadow-2xl">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-lg">
                    <DollarSign className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-white font-semibold text-lg">الأداء المالي الإجمالي</h3>
                    <p className="text-emerald-200/70 text-sm">العائد على الاستثمار</p>
                  </div>
                </div>
                <Badge className="bg-emerald-500/20 text-emerald-300 border-emerald-500/30">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +28.5%
                </Badge>
              </div>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <p className="text-emerald-200/70 text-sm mb-1">إجمالي الإيرادات</p>
                  <p className="text-3xl font-bold text-white">12.8M</p>
                  <p className="text-emerald-300 text-sm">ر.س</p>
                </div>
                <div>
                  <p className="text-emerald-200/70 text-sm mb-1">صافي الربح</p>
                  <p className="text-3xl font-bold text-white">3.6M</p>
                  <p className="text-emerald-300 text-sm">هامش 28.1%</p>
                </div>
              </div>
              <div className="mt-4 h-20">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={chartData.financialTrends.slice(-6)}>
                    <Area type="monotone" dataKey="profit" stroke="#10b981" fill="rgba(16, 185, 129, 0.2)" strokeWidth={2} />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Operational Excellence */}
          <Card className="bg-gradient-to-br from-blue-900/40 to-indigo-800/40 backdrop-blur-xl border border-blue-500/30 shadow-2xl">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg">
                  <Target className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-white font-semibold">التميز التشغيلي</h3>
                  <p className="text-blue-200/70 text-sm">مؤشر الكفاءة</p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-blue-200/80 text-sm">الإنتاجية</span>
                    <span className="text-white font-bold">96%</span>
                  </div>
                  <Progress value={96} className="h-2 bg-blue-900/50" />
                </div>
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-blue-200/80 text-sm">الجودة</span>
                    <span className="text-white font-bold">94%</span>
                  </div>
                  <Progress value={94} className="h-2 bg-blue-900/50" />
                </div>
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-blue-200/80 text-sm">رضا العملاء</span>
                    <span className="text-white font-bold">98%</span>
                  </div>
                  <Progress value={98} className="h-2 bg-blue-900/50" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Human Capital */}
          <Card className="bg-gradient-to-br from-purple-900/40 to-violet-800/40 backdrop-blur-xl border border-purple-500/30 shadow-2xl">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-violet-600 shadow-lg">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-white font-semibold">رأس المال البشري</h3>
                  <p className="text-purple-200/70 text-sm">إدارة المواهب</p>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-purple-200/80 text-sm">إجمالي الموظفين</span>
                  <span className="text-2xl font-bold text-white">{dashboardData?.total_employees || '176'}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-purple-200/80 text-sm">معدل الاحتفاظ</span>
                  <span className="text-lg font-bold text-purple-300">94.2%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-purple-200/80 text-sm">رضا الموظفين</span>
                  <span className="text-lg font-bold text-purple-300">96%</span>
                </div>
                <Badge className="w-full justify-center bg-purple-500/20 text-purple-300 border-purple-500/30">
                  <Award className="h-3 w-3 mr-1" />
                  أفضل بيئة عمل 2024
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Advanced Business Intelligence Analytics */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Executive Financial Dashboard */}
          <Card className="xl:col-span-2 bg-gradient-to-br from-slate-900/60 to-slate-800/60 backdrop-blur-xl border border-slate-700/50 shadow-2xl">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-emerald-500 to-green-600">
                    <LineChartIcon className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-white text-xl">تحليل الأداء المالي المتقدم</CardTitle>
                    <CardDescription className="text-slate-300">
                      اتجاهات الإيرادات والربحية - آخر 6 أشهر
                    </CardDescription>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className="bg-emerald-500/20 text-emerald-400 border-emerald-500/30">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +28.5% نمو
                  </Badge>
                  <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-96">
                <ResponsiveContainer width="100%" height="100%">
                  <ComposedChart data={chartData.financialTrends}>
                    <defs>
                      <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                      </linearGradient>
                      <linearGradient id="profitGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    <XAxis
                      dataKey="month"
                      stroke="rgba(255,255,255,0.7)"
                      fontSize={12}
                      tickLine={false}
                    />
                    <YAxis
                      stroke="rgba(255,255,255,0.7)"
                      fontSize={12}
                      tickLine={false}
                      axisLine={false}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0,0,0,0.95)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '12px',
                        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
                      }}
                    />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stroke="#10b981"
                      fill="url(#revenueGradient)"
                      strokeWidth={3}
                      name="الإيرادات (مليون ر.س)"
                    />
                    <Area
                      type="monotone"
                      dataKey="profit"
                      stroke="#3b82f6"
                      fill="url(#profitGradient)"
                      strokeWidth={3}
                      name="صافي الربح (مليون ر.س)"
                    />
                    <Line
                      type="monotone"
                      dataKey="growth"
                      stroke="#f59e0b"
                      strokeWidth={3}
                      name="معدل النمو %"
                      dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}
                    />
                  </ComposedChart>
                </ResponsiveContainer>
              </div>
              <div className="grid grid-cols-3 gap-4 mt-6 pt-6 border-t border-slate-700/50">
                <div className="text-center">
                  <p className="text-slate-400 text-sm">متوسط النمو الشهري</p>
                  <p className="text-2xl font-bold text-emerald-400">+18.7%</p>
                </div>
                <div className="text-center">
                  <p className="text-slate-400 text-sm">أفضل شهر</p>
                  <p className="text-2xl font-bold text-blue-400">يونيو</p>
                </div>
                <div className="text-center">
                  <p className="text-slate-400 text-sm">التوقعات القادمة</p>
                  <p className="text-2xl font-bold text-purple-400">+22%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Strategic Project Portfolio */}
          <Card className="bg-gradient-to-br from-indigo-900/60 to-purple-800/60 backdrop-blur-xl border border-indigo-500/30 shadow-2xl">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-600">
                    <PieChartIcon className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-white">محفظة المشاريع الاستراتيجية</CardTitle>
                    <CardDescription className="text-indigo-200">
                      إدارة المشاريع والمبادرات
                    </CardDescription>
                  </div>
                </div>
                <Badge className="bg-indigo-500/20 text-indigo-400 border-indigo-500/30">
                  72 مشروع
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-64 mb-6">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <defs>
                      <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
                        <feDropShadow dx="0" dy="4" stdDeviation="8" floodColor="rgba(0,0,0,0.3)"/>
                      </filter>
                    </defs>
                    <Pie
                      data={chartData.projectStatus}
                      cx="50%"
                      cy="50%"
                      innerRadius={50}
                      outerRadius={90}
                      paddingAngle={3}
                      dataKey="value"
                      filter="url(#shadow)"
                    >
                      {chartData.projectStatus.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0,0,0,0.95)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '12px',
                        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
                      }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="space-y-3">
                {chartData.projectStatus.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors">
                    <div className="flex items-center gap-3">
                      <div className={`w-4 h-4 rounded-full shadow-lg`} style={{ backgroundColor: item.color }}></div>
                      <span className="text-white font-medium">{item.name}</span>
                    </div>
                    <div className="text-right">
                      <span className="text-white font-bold text-lg">{item.value}</span>
                      <p className="text-indigo-300 text-sm">{item.percentage}%</p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6 pt-4 border-t border-indigo-700/50">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-indigo-300 text-sm">معدل النجاح</p>
                    <p className="text-2xl font-bold text-white">96.2%</p>
                  </div>
                  <div>
                    <p className="text-indigo-300 text-sm">القيمة الإجمالية</p>
                    <p className="text-2xl font-bold text-white">45M ر.س</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Organizational Performance Intelligence */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Department Excellence Matrix */}
          <Card className="bg-gradient-to-br from-cyan-900/60 to-blue-800/60 backdrop-blur-xl border border-cyan-500/30 shadow-2xl">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-cyan-500 to-blue-600">
                    <BarChartIcon className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-white">مصفوفة التميز التنظيمي</CardTitle>
                    <CardDescription className="text-cyan-200">
                      أداء الأقسام والكفاءة التشغيلية
                    </CardDescription>
                  </div>
                </div>
                <Button variant="ghost" size="sm" className="text-cyan-400 hover:text-white">
                  <Eye className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={chartData.departmentPerformance} layout="horizontal">
                    <defs>
                      <linearGradient id="performanceGradient" x1="0" y1="0" x2="1" y2="0">
                        <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#06d6a0" stopOpacity={0.8}/>
                      </linearGradient>
                      <linearGradient id="utilizationGradient" x1="0" y1="0" x2="1" y2="0">
                        <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#06b6d4" stopOpacity={0.8}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    <XAxis
                      type="number"
                      stroke="rgba(255,255,255,0.7)"
                      fontSize={12}
                      tickLine={false}
                    />
                    <YAxis
                      dataKey="name"
                      type="category"
                      stroke="rgba(255,255,255,0.7)"
                      width={120}
                      fontSize={12}
                      tickLine={false}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0,0,0,0.95)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '12px',
                        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
                      }}
                    />
                    <Legend />
                    <Bar
                      dataKey="performance"
                      fill="url(#performanceGradient)"
                      name="مؤشر الأداء %"
                      radius={[0, 6, 6, 0]}
                      strokeWidth={1}
                      stroke="rgba(16, 185, 129, 0.5)"
                    />
                    <Bar
                      dataKey="utilization"
                      fill="url(#utilizationGradient)"
                      name="كفاءة الموارد %"
                      radius={[0, 6, 6, 0]}
                      strokeWidth={1}
                      stroke="rgba(59, 130, 246, 0.5)"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              <div className="mt-6 grid grid-cols-2 gap-4">
                <div className="text-center p-3 rounded-lg bg-emerald-500/10 border border-emerald-500/20">
                  <p className="text-emerald-300 text-sm">أعلى أداء</p>
                  <p className="text-xl font-bold text-white">تقنية المعلومات</p>
                  <p className="text-emerald-400 text-sm">94%</p>
                </div>
                <div className="text-center p-3 rounded-lg bg-blue-500/10 border border-blue-500/20">
                  <p className="text-blue-300 text-sm">أفضل كفاءة</p>
                  <p className="text-xl font-bold text-white">المبيعات</p>
                  <p className="text-blue-400 text-sm">92%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Advanced Talent Analytics */}
          <Card className="bg-gradient-to-br from-amber-900/60 to-orange-800/60 backdrop-blur-xl border border-amber-500/30 shadow-2xl">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-amber-500 to-orange-600">
                    <Users className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-white">تحليلات المواهب المتقدمة</CardTitle>
                    <CardDescription className="text-amber-200">
                      رضا الموظفين والإنتاجية التنظيمية
                    </CardDescription>
                  </div>
                </div>
                <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/30">
                  <Star className="h-3 w-3 mr-1" />
                  تميز
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData.employeeMetrics}>
                    <defs>
                      <linearGradient id="satisfactionGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#f59e0b" stopOpacity={0.1}/>
                      </linearGradient>
                      <linearGradient id="productivityGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    <XAxis
                      dataKey="month"
                      stroke="rgba(255,255,255,0.7)"
                      fontSize={12}
                      tickLine={false}
                    />
                    <YAxis
                      stroke="rgba(255,255,255,0.7)"
                      fontSize={12}
                      tickLine={false}
                      axisLine={false}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0,0,0,0.95)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '12px',
                        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
                      }}
                    />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="satisfaction"
                      stroke="#f59e0b"
                      fill="url(#satisfactionGradient)"
                      strokeWidth={3}
                      name="رضا الموظفين %"
                      dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}
                    />
                    <Area
                      type="monotone"
                      dataKey="productivity"
                      stroke="#10b981"
                      fill="url(#productivityGradient)"
                      strokeWidth={3}
                      name="مؤشر الإنتاجية %"
                      dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="total"
                      stroke="#3b82f6"
                      strokeWidth={2}
                      name="إجمالي الموظفين"
                      dot={{ fill: '#3b82f6', strokeWidth: 2, r: 3 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
              <div className="mt-6 grid grid-cols-3 gap-4">
                <div className="text-center p-3 rounded-lg bg-amber-500/10 border border-amber-500/20">
                  <p className="text-amber-300 text-sm">متوسط الرضا</p>
                  <p className="text-2xl font-bold text-white">92%</p>
                </div>
                <div className="text-center p-3 rounded-lg bg-emerald-500/10 border border-emerald-500/20">
                  <p className="text-emerald-300 text-sm">الإنتاجية</p>
                  <p className="text-2xl font-bold text-white">96%</p>
                </div>
                <div className="text-center p-3 rounded-lg bg-blue-500/10 border border-blue-500/20">
                  <p className="text-blue-300 text-sm">معدل النمو</p>
                  <p className="text-2xl font-bold text-white">+8.6%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Executive Strategic KPI Command Center */}
        <Card className="bg-gradient-to-br from-slate-900/80 to-purple-900/80 backdrop-blur-xl border border-purple-500/30 shadow-2xl">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-indigo-600 shadow-lg">
                  <Gauge className="h-6 w-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-white text-2xl">مركز القيادة الاستراتيجية</CardTitle>
                  <CardDescription className="text-purple-200">
                    مؤشرات الأداء الرئيسية للإدارة التنفيذية
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                  <Activity className="h-3 w-3 mr-1" />
                  مباشر
                </Badge>
                <Button variant="outline" className="border-purple-500/30 text-purple-300 hover:bg-purple-500/10">
                  <Eye className="h-4 w-4 mr-2" />
                  تفاصيل متقدمة
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData.kpiTrends}>
                  <defs>
                    <linearGradient id="roiGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                    </linearGradient>
                    <linearGradient id="customerGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                    </linearGradient>
                    <linearGradient id="employeeGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#f59e0b" stopOpacity={0.1}/>
                    </linearGradient>
                    <linearGradient id="efficiencyGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis
                    dataKey="period"
                    stroke="rgba(255,255,255,0.7)"
                    fontSize={12}
                    tickLine={false}
                  />
                  <YAxis
                    stroke="rgba(255,255,255,0.7)"
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.95)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '16px',
                      boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
                      padding: '16px'
                    }}
                  />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="roi"
                    stroke="#10b981"
                    fill="url(#roiGradient)"
                    strokeWidth={4}
                    name="العائد على الاستثمار %"
                    dot={{ fill: '#10b981', strokeWidth: 3, r: 5 }}
                  />
                  <Area
                    type="monotone"
                    dataKey="customerSat"
                    stroke="#3b82f6"
                    fill="url(#customerGradient)"
                    strokeWidth={4}
                    name="رضا العملاء %"
                    dot={{ fill: '#3b82f6', strokeWidth: 3, r: 5 }}
                  />
                  <Area
                    type="monotone"
                    dataKey="employeeSat"
                    stroke="#f59e0b"
                    fill="url(#employeeGradient)"
                    strokeWidth={4}
                    name="رضا الموظفين %"
                    dot={{ fill: '#f59e0b', strokeWidth: 3, r: 5 }}
                  />
                  <Area
                    type="monotone"
                    dataKey="efficiency"
                    stroke="#8b5cf6"
                    fill="url(#efficiencyGradient)"
                    strokeWidth={4}
                    name="الكفاءة التشغيلية %"
                    dot={{ fill: '#8b5cf6', strokeWidth: 3, r: 5 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div className="grid grid-cols-4 gap-4 mt-8 pt-6 border-t border-purple-700/50">
              <div className="text-center p-4 rounded-xl bg-emerald-500/10 border border-emerald-500/20">
                <p className="text-emerald-300 text-sm font-medium">ROI الحالي</p>
                <p className="text-3xl font-bold text-white">28.2%</p>
                <p className="text-emerald-400 text-xs">+4.1% من الهدف</p>
              </div>
              <div className="text-center p-4 rounded-xl bg-blue-500/10 border border-blue-500/20">
                <p className="text-blue-300 text-sm font-medium">رضا العملاء</p>
                <p className="text-3xl font-bold text-white">94%</p>
                <p className="text-blue-400 text-xs">+3% هذا الشهر</p>
              </div>
              <div className="text-center p-4 rounded-xl bg-amber-500/10 border border-amber-500/20">
                <p className="text-amber-300 text-sm font-medium">رضا الموظفين</p>
                <p className="text-3xl font-bold text-white">96%</p>
                <p className="text-amber-400 text-xs">أعلى مستوى</p>
              </div>
              <div className="text-center p-4 rounded-xl bg-purple-500/10 border border-purple-500/20">
                <p className="text-purple-300 text-sm font-medium">الكفاءة</p>
                <p className="text-3xl font-bold text-white">98%</p>
                <p className="text-purple-400 text-xs">متفوق</p>
              </div>
            </div>
          </CardContent>
        </Card>

      {/* Executive Actions & Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Strategic Quick Actions */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white">{t.quickActions}</CardTitle>
            <CardDescription className="text-white/70">
              إجراءات استراتيجية للإدارة التنفيذية
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {quickActions.map((action, index) => (
                <Link key={index} to={action.href}>
                  <Button
                    variant="outline"
                    className={`w-full h-24 flex flex-col items-center justify-center gap-3 glass-button bg-gradient-to-r ${action.color} hover:scale-105 transition-all duration-300 border-white/20`}
                  >
                    <action.icon className="h-7 w-7" />
                    <span className="text-sm text-center font-medium">{action.title}</span>
                  </Button>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Executive Activities Feed */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white">{t.recentActivities}</CardTitle>
              <Button variant="ghost" size="sm" className="text-white/70 hover:text-white">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start gap-4 p-4 rounded-xl bg-white/5 hover:bg-white/10 transition-colors">
                  <div className={`p-2 rounded-lg ${
                    activity.severity === 'success' ? 'bg-green-500/20 text-green-400' :
                    activity.severity === 'warning' ? 'bg-yellow-500/20 text-yellow-400' :
                    'bg-blue-500/20 text-blue-400'
                  }`}>
                    <activity.icon className="h-5 w-5" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-white text-sm font-medium">
                      {language === 'ar' ? activity.messageAr : activity.message}
                    </p>
                    <p className="text-white/50 text-xs mt-1">{activity.timestamp}</p>
                  </div>
                  <Badge
                    variant="outline"
                    className={`${
                      activity.severity === 'success' ? 'text-green-400 border-green-400/20' :
                      activity.severity === 'warning' ? 'text-yellow-400 border-yellow-400/20' :
                      'text-blue-400 border-blue-400/20'
                    }`}
                  >
                    {activity.severity}
                  </Badge>
                </div>
              ))}
            </div>
            <div className="mt-6 pt-4 border-t border-white/10">
              <Button variant="outline" className="w-full glass-button border-white/20">
                <Activity className="h-4 w-4 mr-2" />
                عرض جميع الأنشطة
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Health Summary */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white">{t.systemHealth}</CardTitle>
          <CardDescription className="text-white/70">
            مراقبة شاملة لصحة النظام والأمان
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {chartData.systemMetrics.map((metric, index) => (
              <div key={index} className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-white/80 text-sm font-medium">{metric.metric}</span>
                  <Badge
                    variant="outline"
                    className={`${
                      metric.status === 'excellent' ? 'text-green-400 border-green-400/20' :
                      metric.status === 'good' ? 'text-blue-400 border-blue-400/20' :
                      'text-yellow-400 border-yellow-400/20'
                    }`}
                  >
                    {metric.status === 'excellent' ? 'ممتاز' : metric.status === 'good' ? 'جيد' : 'متوسط'}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-white/60">الحالي</span>
                    <span className="text-white font-medium">{metric.current}%</span>
                  </div>
                  <Progress
                    value={metric.current}
                    className="h-2 bg-white/10"
                  />
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-white/50">الهدف: {metric.target}%</span>
                    <span className={`${
                      metric.current >= metric.target ? 'text-green-400' : 'text-yellow-400'
                    }`}>
                      {metric.current >= metric.target ? '✓ تم تحقيقه' : '⚠ تحت الهدف'}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  )
}
