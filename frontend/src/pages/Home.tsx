import { useState } from 'react'
import { Link } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Users,
  Building,
  BarChart3,
  Shield,
  Globe,
  Zap,
  CheckCircle,
  ArrowRight,
  Star,
  Play,
  Menu,
  X,
  Phone,
  Mail,
  MapPin,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
  DollarSign,
  TrendingUp
} from 'lucide-react'

interface HomeProps {
  language: 'ar' | 'en'
  setLanguage: (lang: 'ar' | 'en') => void
}

const translations = {
  ar: {
    // Header
    appName: 'نمو',
    tagline: 'منصة إدارة المؤسسات الشاملة المدعومة بالذكاء الاصطناعي',
    login: 'تسجيل الدخول',
    getStarted: 'ابدأ الآن',

    // Hero Section
    heroTitle: 'منصة إدارة المؤسسات الشاملة',
    heroSubtitle: 'نظام ERP متكامل • إدارة الموارد البشرية • CRM • إدارة المشاريع • مؤشرات الأداء KPIs • الذكاء الاصطناعي',
    heroDescription: 'نمو هو نظام إدارة مؤسسات شامل يجمع بين ERP وCRM وHR وإدارة المشاريع ومؤشرات الأداء الرئيسية والذكاء الاصطناعي في منصة واحدة. يدعم أكثر من 50 وحدة عمل مع تحليلات متقدمة وأتمتة ذكية.',
    watchDemo: 'شاهد العرض التوضيحي',
    startFree: 'ابدأ مجاناً',

    // Features
    featuresTitle: 'مميزات النظام الشامل',
    featuresSubtitle: '10 أنظمة في منصة واحدة - حلول متكاملة لجميع احتياجات مؤسستك',

    feature1Title: 'نظام ERP متكامل',
    feature1Desc: 'إدارة شاملة للموارد: الموارد البشرية، المالية، المخزون، المشتريات، والأصول',

    feature2Title: 'نظام CRM متقدم',
    feature2Desc: 'إدارة العملاء، المبيعات، العروض، والفرص التجارية مع تحليلات ذكية',

    feature3Title: 'إدارة المشاريع الاحترافية',
    feature3Desc: 'تخطيط وتنفيذ ومتابعة المشاريع مع إدارة المهام والفرق والميزانيات',

    feature4Title: 'إدارة مؤشرات الأداء الرئيسية',
    feature4Desc: 'تتبع وتحليل KPIs مع تنبيهات ذكية، تقارير تفاعلية، وتوقعات مدعومة بالذكاء الاصطناعي',

    feature5Title: 'الذكاء الاصطناعي والأتمتة',
    feature5Desc: 'تحليلات تنبؤية، كشف الشذوذ، توصيات ذكية، وأتمتة سير العمل',

    feature6Title: 'إدارة الامتثال والمخاطر',
    feature6Desc: 'إدارة الامتثال التنظيمي، حماية البيانات، والتدقيق الداخلي مع منصة SaaS متعددة المستأجرين',

    // How it Works
    howItWorksTitle: 'كيف يعمل النظام',
    howItWorksSubtitle: 'خطوات بسيطة للبدء',

    step1Title: 'إنشاء الحساب',
    step1Desc: 'سجل حساباً جديداً وقم بإعداد مؤسستك',

    step2Title: 'إضافة الموظفين',
    step2Desc: 'أضف موظفيك وحدد أدوارهم وصلاحياتهم',

    step3Title: 'بدء الإدارة',
    step3Desc: 'ابدأ في إدارة جميع عمليات مؤسستك بسهولة',

    // Stats
    statsTitle: 'أرقام تتحدث عن نفسها',
    companies: 'شركة',
    users: 'مستخدم',
    projects: 'مشروع',
    satisfaction: 'رضا العملاء',

    // Testimonials
    testimonialsTitle: 'آراء عملائنا',
    testimonialsSubtitle: 'ماذا يقول عملاؤنا عن نمو',

    // Pricing
    pricingTitle: 'خطط الأسعار',
    pricingSubtitle: 'اختر الخطة المناسبة لمؤسستك',
    basicPlan: 'الخطة الأساسية',
    proPlan: 'الخطة المتقدمة',
    enterprisePlan: 'خطة المؤسسات',
    monthlyBilling: 'شهرياً',
    yearlyBilling: 'سنوياً',
    choosePlan: 'اختر هذه الخطة',
    mostPopular: 'الأكثر شعبية',

    // Process
    processTitle: 'كيف نعمل معك',
    processSubtitle: 'عملية بسيطة ومدروسة لضمان نجاحك',

    process1Title: 'استشارة مجانية',
    process1Desc: 'نبدأ بفهم احتياجاتك ومتطلبات مؤسستك',

    process2Title: 'تخصيص النظام',
    process2Desc: 'نقوم بتخصيص النظام ليناسب عملياتك تماماً',

    process3Title: 'التدريب والدعم',
    process3Desc: 'نوفر تدريباً شاملاً ودعماً مستمراً لفريقك',

    process4Title: 'النمو والتطوير',
    process4Desc: 'نساعدك في النمو مع تطوير مستمر للنظام',

    // Integration
    integrationTitle: 'تكامل سلس مع أدواتك',
    integrationSubtitle: 'يتكامل نمو مع جميع الأدوات التي تستخدمها بالفعل',

    // FAQ
    faqTitle: 'الأسئلة الشائعة',
    faqSubtitle: 'إجابات على الأسئلة الأكثر شيوعاً',

    // CTA
    ctaTitle: 'جاهز للبدء؟',
    ctaSubtitle: 'انضم إلى آلاف الشركات التي تثق في نمو',
    ctaButton: 'ابدأ تجربتك المجانية',

    // Footer
    footerTagline: 'منصة إدارة المؤسسات الشاملة المدعومة بالذكاء الاصطناعي',
    footerDescription: 'نمو هو نظام ERP متكامل يجمع بين إدارة الموارد البشرية، CRM، إدارة المشاريع، مؤشرات الأداء الرئيسية، والذكاء الاصطناعي في منصة واحدة. يدعم أكثر من 50 وحدة عمل مع تحليلات متقدمة وأتمتة ذكية.',
    quickLinks: 'روابط سريعة',
    about: 'حول نمو',
    features: 'المميزات',
    pricing: 'الأسعار',
    support: 'الدعم',
    contact: 'اتصل بنا',
    privacy: 'سياسة الخصوصية',
    terms: 'شروط الاستخدام',
    solutions: 'الحلول',
    erpSolution: 'نظام ERP',
    crmSolution: 'نظام CRM',
    hrSolution: 'إدارة الموارد البشرية',
    projectManagement: 'إدارة المشاريع',
    kpiManagement: 'إدارة مؤشرات الأداء',
    aiAnalytics: 'الذكاء الاصطناعي والتحليلات',
    compliance: 'الامتثال والمخاطر',
    industries: 'الصناعات',
    manufacturing: 'التصنيع',
    consulting: 'الاستشارات',
    services: 'الخدمات',
    government: 'الحكومة',
    followUs: 'تابعنا',
    allRightsReserved: 'جميع الحقوق محفوظة'
  },
  en: {
    // Header
    appName: 'Numu',
    tagline: 'AI-Powered Comprehensive Enterprise Management Platform',
    login: 'Login',
    getStarted: 'Get Started',

    // Hero Section
    heroTitle: 'Complete Enterprise Management Platform',
    heroSubtitle: 'Integrated ERP • Human Resources • CRM • Project Management • KPIs Management • Artificial Intelligence',
    heroDescription: 'Numu is a comprehensive enterprise management system that combines ERP, CRM, HR, Project Management, KPI Management, and AI in one platform. Supporting 50+ business modules with advanced analytics and intelligent automation.',
    watchDemo: 'Watch Demo',
    startFree: 'Start Free',

    // Features
    featuresTitle: 'Comprehensive System Features',
    featuresSubtitle: '10 Systems in One Platform - Integrated solutions for all your organization needs',

    feature1Title: 'Integrated ERP System',
    feature1Desc: 'Complete resource management: HR, Finance, Inventory, Procurement, and Assets',

    feature2Title: 'Advanced CRM System',
    feature2Desc: 'Customer management, sales, quotations, and business opportunities with smart analytics',

    feature3Title: 'Professional Project Management',
    feature3Desc: 'Plan, execute, and monitor projects with task, team, and budget management',

    feature4Title: 'KPI Management System',
    feature4Desc: 'Track and analyze KPIs with smart alerts, interactive reports, and AI-powered predictions',

    feature5Title: 'AI & Automation',
    feature5Desc: 'Predictive analytics, anomaly detection, smart recommendations, and workflow automation',

    feature6Title: 'Compliance & Risk Management',
    feature6Desc: 'Regulatory compliance, data protection, internal auditing with multi-tenant SaaS platform',

    // How it Works
    howItWorksTitle: 'How It Works',
    howItWorksSubtitle: 'Simple steps to get started',

    step1Title: 'Create Account',
    step1Desc: 'Register a new account and set up your organization',

    step2Title: 'Add Employees',
    step2Desc: 'Add your employees and define their roles and permissions',

    step3Title: 'Start Managing',
    step3Desc: 'Begin managing all your organization operations easily',

    // Stats
    statsTitle: 'Numbers Speak for Themselves',
    companies: 'Companies',
    users: 'Users',
    projects: 'Projects',
    satisfaction: 'Satisfaction',

    // Testimonials
    testimonialsTitle: 'Customer Reviews',
    testimonialsSubtitle: 'What our customers say about Numu',

    // Pricing
    pricingTitle: 'Pricing Plans',
    pricingSubtitle: 'Choose the right plan for your organization',
    basicPlan: 'Basic Plan',
    proPlan: 'Pro Plan',
    enterprisePlan: 'Enterprise Plan',
    monthlyBilling: 'Monthly',
    yearlyBilling: 'Yearly',
    choosePlan: 'Choose This Plan',
    mostPopular: 'Most Popular',

    // Process
    processTitle: 'How We Work With You',
    processSubtitle: 'A simple and thoughtful process to ensure your success',

    process1Title: 'Free Consultation',
    process1Desc: 'We start by understanding your needs and organization requirements',

    process2Title: 'System Customization',
    process2Desc: 'We customize the system to perfectly fit your operations',

    process3Title: 'Training & Support',
    process3Desc: 'We provide comprehensive training and ongoing support for your team',

    process4Title: 'Growth & Development',
    process4Desc: 'We help you grow with continuous system development',

    // Integration
    integrationTitle: 'Seamless Integration with Your Tools',
    integrationSubtitle: 'Numu integrates with all the tools you already use',

    // FAQ
    faqTitle: 'Frequently Asked Questions',
    faqSubtitle: 'Answers to the most common questions',

    // CTA
    ctaTitle: 'Ready to Start?',
    ctaSubtitle: 'Join thousands of companies that trust Numu',
    ctaButton: 'Start Your Free Trial',

    // Footer
    footerTagline: 'AI-Powered Comprehensive Enterprise Management Platform',
    footerDescription: 'Numu is an integrated ERP system that combines HR, CRM, Project Management, KPI Management, and AI in one platform. Supporting 50+ business modules with advanced analytics and intelligent automation.',
    quickLinks: 'Quick Links',
    about: 'About Numu',
    features: 'Features',
    pricing: 'Pricing',
    support: 'Support',
    contact: 'Contact Us',
    privacy: 'Privacy Policy',
    terms: 'Terms of Service',
    solutions: 'Solutions',
    erpSolution: 'ERP System',
    crmSolution: 'CRM System',
    hrSolution: 'Human Resources',
    projectManagement: 'Project Management',
    kpiManagement: 'KPI Management',
    aiAnalytics: 'AI & Analytics',
    compliance: 'Compliance & Risk',
    industries: 'Industries',
    manufacturing: 'Manufacturing',
    consulting: 'Consulting',
    services: 'Services',
    government: 'Government',
    followUs: 'Follow Us',
    allRightsReserved: 'All Rights Reserved'
  }
}

export default function Home({ language, setLanguage }: HomeProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      {/* Header */}
      <header className="relative z-50 glass-card border-b border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            {/* Logo */}
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center glow floating shadow-lg">
                <span className="text-white text-xl font-bold">ن</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">{t.appName}</h1>
                <p className="text-xs text-white/60">{t.tagline}</p>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center gap-8">
              <a href="#features" className="text-white/80 hover:text-white transition-colors">{t.features}</a>
              <Link to="/how-it-works" className="text-white/80 hover:text-white transition-colors">
                {language === 'ar' ? 'كيف يعمل' : 'How It Works'}
              </Link>
              <a href="#testimonials" className="text-white/80 hover:text-white transition-colors">
                {language === 'ar' ? 'آراء العملاء' : 'Testimonials'}
              </a>
              <a href="#contact" className="text-white/80 hover:text-white transition-colors">{t.contact}</a>
            </nav>

            {/* Actions */}
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLanguage(language === 'ar' ? 'en' : 'ar')}
                className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10"
              >
                <Globe className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'English' : 'العربية'}
              </Button>

              <Link to="/login">
                <Button variant="outline" className="glass-button">
                  {t.login}
                </Button>
              </Link>

              <Link to="/login">
                <Button className="glass-button bg-gradient-to-r from-blue-500 to-purple-500">
                  {t.getStarted}
                </Button>
              </Link>

              {/* Mobile Menu Button */}
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden glass-card border-t border-white/10">
            <div className="px-4 py-6 space-y-4">
              <a href="#features" className="block text-white/80 hover:text-white transition-colors">{t.features}</a>
              <Link to="/how-it-works" className="block text-white/80 hover:text-white transition-colors">
                {language === 'ar' ? 'كيف يعمل' : 'How It Works'}
              </Link>
              <a href="#testimonials" className="block text-white/80 hover:text-white transition-colors">
                {language === 'ar' ? 'آراء العملاء' : 'Testimonials'}
              </a>
              <a href="#contact" className="block text-white/80 hover:text-white transition-colors">{t.contact}</a>
            </div>
          </div>
        )}
      </header>

      {/* Hero Section */}
      <section className="relative z-10 pt-32 pb-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            {/* Badge with Animation */}
            <div className="inline-flex items-center gap-2 glass-card px-6 py-3 rounded-full border border-white/20 mb-8 animate-fade-in-up animation-delay-200">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-white/80 text-sm font-medium">
                {language === 'ar' ? 'متاح الآن - نسخة تجريبية مجانية' : 'Available Now - Free Trial'}
              </span>
            </div>

            <h1 className="text-6xl md:text-8xl font-bold text-white mb-8 leading-tight animate-fade-in-up animation-delay-400">
              <span className="inline-block animate-text-gradient bg-gradient-to-r from-white via-blue-200 to-white bg-clip-text text-transparent bg-300% animate-gradient-x">
                {t.heroTitle}
              </span>
            </h1>
            <p className="text-2xl md:text-3xl text-white/90 mb-10 max-w-5xl mx-auto leading-relaxed font-light animate-fade-in-up animation-delay-600">
              {t.heroSubtitle}
            </p>
            <p className="text-xl text-white/70 mb-16 max-w-4xl mx-auto leading-relaxed animate-fade-in-up animation-delay-800">
              {t.heroDescription}
            </p>

            {/* Key Benefits with Staggered Animation */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16 max-w-4xl mx-auto">
              <div className="flex items-center justify-center gap-3 text-white/80 animate-fade-in-up animation-delay-1000 group">
                <CheckCircle className="h-6 w-6 text-green-400 group-hover:scale-110 transition-transform duration-300" />
                <span className="text-lg">{language === 'ar' ? 'إعداد سريع في 5 دقائق' : 'Quick 5-minute setup'}</span>
              </div>
              <div className="flex items-center justify-center gap-3 text-white/80 animate-fade-in-up animation-delay-1200 group">
                <CheckCircle className="h-6 w-6 text-green-400 group-hover:scale-110 transition-transform duration-300" />
                <span className="text-lg">{language === 'ar' ? 'دعم فني 24/7' : '24/7 Technical Support'}</span>
              </div>
              <div className="flex items-center justify-center gap-3 text-white/80 animate-fade-in-up animation-delay-1400 group">
                <CheckCircle className="h-6 w-6 text-green-400 group-hover:scale-110 transition-transform duration-300" />
                <span className="text-lg">{language === 'ar' ? 'أمان متقدم للبيانات' : 'Advanced Data Security'}</span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12 animate-fade-in-up animation-delay-1600">
              <Link to="/login">
                <Button size="lg" className="glass-button bg-gradient-to-r from-blue-500 to-purple-500 text-xl px-12 py-6 h-auto shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 hover:scale-105 hover:-translate-y-1 group">
                  <Zap className="h-6 w-6 mr-3 group-hover:rotate-12 transition-transform duration-300" />
                  {t.startFree}
                </Button>
              </Link>

              <Button size="lg" variant="outline" className="glass-button text-xl px-12 py-6 h-auto border-2 border-white/30 hover:border-white/50 transition-all duration-300 hover:scale-105 hover:-translate-y-1 group">
                <Play className="h-6 w-6 mr-3 group-hover:scale-110 transition-transform duration-300" />
                {t.watchDemo}
              </Button>
            </div>

            {/* Trust Indicators with Animation */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-white/60 text-sm animate-fade-in-up animation-delay-1800">
              <div className="flex items-center gap-2 group hover:text-white/80 transition-colors duration-300">
                <Shield className="h-5 w-5 group-hover:scale-110 group-hover:text-blue-400 transition-all duration-300" />
                <span>{language === 'ar' ? 'معتمد من هيئة الاتصالات' : 'CITC Certified'}</span>
              </div>
              <div className="flex items-center gap-2 group hover:text-white/80 transition-colors duration-300">
                <Globe className="h-5 w-5 group-hover:scale-110 group-hover:text-green-400 transition-all duration-300" />
                <span>{language === 'ar' ? 'متوافق مع رؤية 2030' : 'Vision 2030 Compliant'}</span>
              </div>
              <div className="flex items-center gap-2 group hover:text-white/80 transition-colors duration-300">
                <Star className="h-5 w-5 text-yellow-400 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300" />
                <span>{language === 'ar' ? 'تقييم 4.9/5 من العملاء' : '4.9/5 Customer Rating'}</span>
              </div>
            </div>
          </div>

          {/* Decorative Lines */}
          <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
          <div className="absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/10 to-transparent"></div>

          {/* Additional Information Section */}
          <div className="mt-20">
            <div className="max-w-6xl mx-auto">
              {/* Info Cards Grid */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                {/* Card 1 - Quick Setup */}
                <div className="glass-card p-8 rounded-2xl border border-white/20 text-center group hover:border-white/40 transition-all duration-300 animate-fade-in-up animation-delay-1800 hover-scale">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 animate-bounce-slow">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">
                    {language === 'ar' ? 'إعداد سريع' : 'Quick Setup'}
                  </h3>
                  <p className="text-white/70 leading-relaxed">
                    {language === 'ar'
                      ? 'ابدأ في استخدام النظام خلال 5 دقائق فقط. لا حاجة لخبرة تقنية معقدة.'
                      : 'Get started in just 5 minutes. No complex technical expertise required.'
                    }
                  </p>
                </div>

                {/* Card 2 - Smart Analytics */}
                <div className="glass-card p-8 rounded-2xl border border-white/20 text-center group hover:border-white/40 transition-all duration-300 animate-fade-in-up animation-delay-2000 hover-scale">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 animate-rotate-slow">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">
                    {language === 'ar' ? 'تحليلات ذكية' : 'Smart Analytics'}
                  </h3>
                  <p className="text-white/70 leading-relaxed">
                    {language === 'ar'
                      ? 'تقارير تفاعلية ورؤى عميقة لاتخاذ قرارات مدروسة وتحسين الأداء.'
                      : 'Interactive reports and deep insights for informed decisions and performance optimization.'
                    }
                  </p>
                </div>

                {/* Card 3 - Cloud Security */}
                <div className="glass-card p-8 rounded-2xl border border-white/20 text-center group hover:border-white/40 transition-all duration-300 animate-fade-in-up animation-delay-2200 hover-scale">
                  <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 floating">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">
                    {language === 'ar' ? 'أمان سحابي' : 'Cloud Security'}
                  </h3>
                  <p className="text-white/70 leading-relaxed">
                    {language === 'ar'
                      ? 'حماية متقدمة للبيانات مع تشفير عالي المستوى ونسخ احتياطية تلقائية.'
                      : 'Advanced data protection with high-level encryption and automatic backups.'
                    }
                  </p>
                </div>
              </div>

              {/* Feature Highlights with SVG */}
              <div className="glass-card p-12 rounded-3xl border border-white/20 relative overflow-hidden animate-fade-in-up animation-delay-2400">
                {/* Background SVG Pattern */}
                <div className="absolute inset-0 opacity-5">
                  <svg className="w-full h-full" viewBox="0 0 400 400" fill="none">
                    <defs>
                      <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                        <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="1"/>
                      </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#grid)" />
                  </svg>
                </div>

                <div className="relative z-10">
                  <div className="text-center mb-12">
                    <h2 className="text-4xl font-bold text-white mb-6 animate-fade-in-up animation-delay-2600">
                      {language === 'ar' ? 'لماذا تختار نمو؟' : 'Why Choose Numu?'}
                    </h2>
                    <p className="text-xl text-white/80 max-w-3xl mx-auto animate-fade-in-up animation-delay-2800">
                      {language === 'ar'
                        ? 'نظام شامل يجمع كل ما تحتاجه لإدارة مؤسستك بكفاءة وفعالية'
                        : 'A comprehensive system that brings together everything you need to manage your organization efficiently'
                      }
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                    {/* Left Side - Features List */}
                    <div className="space-y-6 animate-slide-in-left animation-delay-3000">
                      <div className="flex items-start gap-4 animate-fade-in-up animation-delay-3200">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1 floating">
                          <CheckCircle className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h4 className="text-lg font-semibold text-white mb-2">
                            {language === 'ar' ? 'إدارة شاملة للموارد البشرية' : 'Comprehensive HR Management'}
                          </h4>
                          <p className="text-white/70">
                            {language === 'ar'
                              ? 'من التوظيف إلى التقاعد، إدارة كاملة لدورة حياة الموظف'
                              : 'From hiring to retirement, complete employee lifecycle management'
                            }
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start gap-4 animate-fade-in-up animation-delay-3400">
                        <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1 floating animation-delay-2000">
                          <CheckCircle className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h4 className="text-lg font-semibold text-white mb-2">
                            {language === 'ar' ? 'تتبع مالي دقيق' : 'Precise Financial Tracking'}
                          </h4>
                          <p className="text-white/70">
                            {language === 'ar'
                              ? 'مراقبة الميزانيات والمصروفات مع تقارير مالية تفصيلية'
                              : 'Monitor budgets and expenses with detailed financial reports'
                            }
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start gap-4 animate-fade-in-up animation-delay-3600">
                        <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1 floating animation-delay-4000">
                          <CheckCircle className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h4 className="text-lg font-semibold text-white mb-2">
                            {language === 'ar' ? 'تعاون فعال بين الفرق' : 'Effective Team Collaboration'}
                          </h4>
                          <p className="text-white/70">
                            {language === 'ar'
                              ? 'أدوات تواصل وتعاون متقدمة لتحسين الإنتاجية'
                              : 'Advanced communication and collaboration tools to boost productivity'
                            }
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Right Side - SVG Illustration */}
                    <div className="flex justify-center animate-slide-in-right animation-delay-3000">
                      <div className="relative">
                        <svg className="w-80 h-80 animate-rotate-slow" viewBox="0 0 400 400" fill="none">
                          {/* Background Circle */}
                          <circle cx="200" cy="200" r="180" fill="url(#gradient1)" fillOpacity="0.1" />

                          {/* Central Hub */}
                          <circle cx="200" cy="200" r="40" fill="url(#gradient2)" />
                          <circle cx="200" cy="200" r="30" fill="rgba(255,255,255,0.1)" />

                          {/* Connecting Lines */}
                          <line x1="200" y1="200" x2="200" y2="80" stroke="url(#gradient3)" strokeWidth="3" strokeDasharray="5,5">
                            <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite" />
                          </line>
                          <line x1="200" y1="200" x2="320" y2="200" stroke="url(#gradient3)" strokeWidth="3" strokeDasharray="5,5">
                            <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite" />
                          </line>
                          <line x1="200" y1="200" x2="200" y2="320" stroke="url(#gradient3)" strokeWidth="3" strokeDasharray="5,5">
                            <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite" />
                          </line>
                          <line x1="200" y1="200" x2="80" y2="200" stroke="url(#gradient3)" strokeWidth="3" strokeDasharray="5,5">
                            <animate attributeName="stroke-dashoffset" values="0;10" dur="2s" repeatCount="indefinite" />
                          </line>

                          {/* Feature Nodes */}
                          <circle cx="200" cy="80" r="25" fill="url(#gradient4)" />
                          <circle cx="320" cy="200" r="25" fill="url(#gradient5)" />
                          <circle cx="200" cy="320" r="25" fill="url(#gradient6)" />
                          <circle cx="80" cy="200" r="25" fill="url(#gradient7)" />

                          {/* Icons in nodes */}
                          <g transform="translate(185, 65)">
                            <path d="M15 7a4 4 0 11-8 0 4 4 0 018 0zM1.615 16.428a1.224 1.224 0 01-.569-1.175 6.002 6.002 0 0111.908 0c.058.467-.172.92-.57 1.174A9.953 9.953 0 017 18a9.953 9.953 0 01-5.385-1.572z" fill="white" />
                          </g>

                          <defs>
                            <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stopColor="#3B82F6" />
                              <stop offset="100%" stopColor="#8B5CF6" />
                            </linearGradient>
                            <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stopColor="#3B82F6" />
                              <stop offset="100%" stopColor="#8B5CF6" />
                            </linearGradient>
                            <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.8" />
                              <stop offset="100%" stopColor="#8B5CF6" stopOpacity="0.8" />
                            </linearGradient>
                            <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stopColor="#3B82F6" />
                              <stop offset="100%" stopColor="#06B6D4" />
                            </linearGradient>
                            <linearGradient id="gradient5" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stopColor="#8B5CF6" />
                              <stop offset="100%" stopColor="#EC4899" />
                            </linearGradient>
                            <linearGradient id="gradient6" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stopColor="#10B981" />
                              <stop offset="100%" stopColor="#059669" />
                            </linearGradient>
                            <linearGradient id="gradient7" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stopColor="#F59E0B" />
                              <stop offset="100%" stopColor="#EF4444" />
                            </linearGradient>
                          </defs>
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>


        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="relative z-10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">{t.featuresTitle}</h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">{t.featuresSubtitle}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1 - ERP */}
            <Card className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Building className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-white text-xl">{t.feature1Title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-white/70 text-center">{t.feature1Desc}</p>
              </CardContent>
            </Card>

            {/* Feature 2 - CRM */}
            <Card className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-white text-xl">{t.feature2Title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-white/70 text-center">{t.feature2Desc}</p>
              </CardContent>
            </Card>

            {/* Feature 3 - Project Management */}
            <Card className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Building className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-white text-xl">{t.feature3Title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-white/70 text-center">{t.feature3Desc}</p>
              </CardContent>
            </Card>

            {/* Feature 4 - KPI Management */}
            <Card className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <BarChart3 className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-white text-xl">{t.feature4Title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-white/70 text-center">{t.feature4Desc}</p>
              </CardContent>
            </Card>

            {/* Feature 5 - AI & Automation */}
            <Card className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Zap className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-white text-xl">{t.feature5Title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-white/70 text-center">{t.feature5Desc}</p>
              </CardContent>
            </Card>

            {/* Feature 6 - Multi-Tenant */}
            <Card className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <Globe className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-white text-xl">{t.feature6Title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-white/70 text-center">{t.feature6Desc}</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How it Works Section */}
      <section id="how-it-works" className="relative z-10 py-20 bg-black/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">{t.howItWorksTitle}</h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">{t.howItWorksSubtitle}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Step 1 */}
            <div className="text-center group">
              <div className="relative mb-8">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 glow">
                  <span className="text-white text-2xl font-bold">1</span>
                </div>
                <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-px h-16 bg-gradient-to-b from-blue-500 to-transparent md:hidden"></div>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">{t.step1Title}</h3>
              <p className="text-white/70">{t.step1Desc}</p>
            </div>

            {/* Step 2 */}
            <div className="text-center group">
              <div className="relative mb-8">
                <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 glow">
                  <span className="text-white text-2xl font-bold">2</span>
                </div>
                <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-px h-16 bg-gradient-to-b from-purple-500 to-transparent md:hidden"></div>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">{t.step2Title}</h3>
              <p className="text-white/70">{t.step2Desc}</p>
            </div>

            {/* Step 3 */}
            <div className="text-center group">
              <div className="relative mb-8">
                <div className="w-20 h-20 bg-gradient-to-r from-pink-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 glow">
                  <span className="text-white text-2xl font-bold">3</span>
                </div>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">{t.step3Title}</h3>
              <p className="text-white/70">{t.step3Desc}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="relative z-10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">{t.statsTitle}</h2>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-500 mb-2">500+</div>
              <p className="text-white/70 text-lg">{t.companies}</p>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-pink-500 mb-2">10K+</div>
              <p className="text-white/70 text-lg">{t.users}</p>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-red-500 mb-2">2K+</div>
              <p className="text-white/70 text-lg">{t.projects}</p>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500 mb-2">98%</div>
              <p className="text-white/70 text-lg">{t.satisfaction}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="relative z-10 py-20 bg-black/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">{t.testimonialsTitle}</h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto">{t.testimonialsSubtitle}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <Card className="glass-card border-white/20">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-white/80 mb-6">"نمو غيّر طريقة إدارتنا للشركة بالكامل. النظام سهل الاستخدام ويوفر جميع الأدوات التي نحتاجها."</p>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-bold">أ</span>
                  </div>
                  <div>
                    <p className="text-white font-semibold">أحمد المحمد</p>
                    <p className="text-white/60 text-sm">مدير عام، شركة التقنية المتقدمة</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Testimonial 2 */}
            <Card className="glass-card border-white/20">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-white/80 mb-6">"الدعم الفني ممتاز والنظام يعمل بسلاسة. وفرنا الكثير من الوقت والجهد في إدارة الموظفين."</p>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-bold">س</span>
                  </div>
                  <div>
                    <p className="text-white font-semibold">سارة العلي</p>
                    <p className="text-white/60 text-sm">مديرة الموارد البشرية، مجموعة الخليج</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Testimonial 3 */}
            <Card className="glass-card border-white/20">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-white/80 mb-6">"التقارير المالية دقيقة ومفصلة. النظام ساعدنا في اتخاذ قرارات أفضل لنمو الشركة."</p>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-bold">م</span>
                  </div>
                  <div>
                    <p className="text-white font-semibold">محمد الراشد</p>
                    <p className="text-white/60 text-sm">المدير المالي، شركة الابتكار</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="relative z-10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in-up">{t.pricingTitle}</h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto animate-fade-in-up animation-delay-200">{t.pricingSubtitle}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Basic Plan */}
            <div className="glass-card p-8 rounded-2xl border border-white/20 text-center hover:border-white/40 transition-all duration-300 animate-fade-in-up animation-delay-400">
              <h3 className="text-2xl font-bold text-white mb-4">{t.basicPlan}</h3>
              <div className="mb-6">
                <span className="text-4xl font-bold text-white">299</span>
                <span className="text-white/70 text-lg"> ريال/{t.monthlyBilling}</span>
              </div>
              <ul className="space-y-3 mb-8 text-white/80">
                <li className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>{language === 'ar' ? 'حتى 50 موظف' : 'Up to 50 employees'}</span>
                </li>
                <li className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>{language === 'ar' ? 'إدارة أساسية للموارد البشرية' : 'Basic HR management'}</span>
                </li>
                <li className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>{language === 'ar' ? 'تقارير شهرية' : 'Monthly reports'}</span>
                </li>
                <li className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>{language === 'ar' ? 'دعم فني عبر البريد' : 'Email support'}</span>
                </li>
              </ul>
              <Button className="w-full glass-button border-white/30 hover:border-white/50">
                {t.choosePlan}
              </Button>
            </div>

            {/* Pro Plan */}
            <div className="glass-card p-8 rounded-2xl border-2 border-blue-500/50 text-center relative hover:border-blue-500/70 transition-all duration-300 animate-fade-in-up animation-delay-600">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                  {t.mostPopular}
                </span>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">{t.proPlan}</h3>
              <div className="mb-6">
                <span className="text-4xl font-bold text-white">599</span>
                <span className="text-white/70 text-lg"> ريال/{t.monthlyBilling}</span>
              </div>
              <ul className="space-y-3 mb-8 text-white/80">
                <li className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>{language === 'ar' ? 'حتى 200 موظف' : 'Up to 200 employees'}</span>
                </li>
                <li className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>{language === 'ar' ? 'إدارة متقدمة للموارد البشرية' : 'Advanced HR management'}</span>
                </li>
                <li className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>{language === 'ar' ? 'تقارير أسبوعية وشهرية' : 'Weekly & monthly reports'}</span>
                </li>
                <li className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>{language === 'ar' ? 'دعم فني 24/7' : '24/7 support'}</span>
                </li>
                <li className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>{language === 'ar' ? 'تكامل مع الأنظمة الخارجية' : 'External integrations'}</span>
                </li>
              </ul>
              <Button className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600">
                {t.choosePlan}
              </Button>
            </div>

            {/* Enterprise Plan */}
            <div className="glass-card p-8 rounded-2xl border border-white/20 text-center hover:border-white/40 transition-all duration-300 animate-fade-in-up animation-delay-800">
              <h3 className="text-2xl font-bold text-white mb-4">{t.enterprisePlan}</h3>
              <div className="mb-6">
                <span className="text-4xl font-bold text-white">{language === 'ar' ? 'مخصص' : 'Custom'}</span>
              </div>
              <ul className="space-y-3 mb-8 text-white/80">
                <li className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>{language === 'ar' ? 'عدد غير محدود من الموظفين' : 'Unlimited employees'}</span>
                </li>
                <li className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>{language === 'ar' ? 'حلول مخصصة' : 'Custom solutions'}</span>
                </li>
                <li className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>{language === 'ar' ? 'تقارير مخصصة' : 'Custom reports'}</span>
                </li>
                <li className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>{language === 'ar' ? 'مدير حساب مخصص' : 'Dedicated account manager'}</span>
                </li>
                <li className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span>{language === 'ar' ? 'تدريب متقدم' : 'Advanced training'}</span>
                </li>
              </ul>
              <Button className="w-full glass-button border-white/30 hover:border-white/50">
                {language === 'ar' ? 'تواصل معنا' : 'Contact Us'}
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="relative z-10 py-20 bg-black/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in-up">{t.processTitle}</h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto animate-fade-in-up animation-delay-200">{t.processSubtitle}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Process Step 1 */}
            <div className="text-center animate-fade-in-up animation-delay-400">
              <div className="relative mb-8">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4 floating">
                  <Users className="h-10 w-10 text-white" />
                </div>
                <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-px h-16 bg-gradient-to-b from-blue-500 to-transparent lg:hidden"></div>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">{t.process1Title}</h3>
              <p className="text-white/70">{t.process1Desc}</p>
            </div>

            {/* Process Step 2 */}
            <div className="text-center animate-fade-in-up animation-delay-600">
              <div className="relative mb-8">
                <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4 floating animation-delay-2000">
                  <Building className="h-10 w-10 text-white" />
                </div>
                <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-px h-16 bg-gradient-to-b from-purple-500 to-transparent lg:hidden"></div>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">{t.process2Title}</h3>
              <p className="text-white/70">{t.process2Desc}</p>
            </div>

            {/* Process Step 3 */}
            <div className="text-center animate-fade-in-up animation-delay-800">
              <div className="relative mb-8">
                <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4 floating animation-delay-4000">
                  <Shield className="h-10 w-10 text-white" />
                </div>
                <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-px h-16 bg-gradient-to-b from-green-500 to-transparent lg:hidden"></div>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">{t.process3Title}</h3>
              <p className="text-white/70">{t.process3Desc}</p>
            </div>

            {/* Process Step 4 */}
            <div className="text-center animate-fade-in-up animation-delay-1000">
              <div className="relative mb-8">
                <div className="w-20 h-20 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4 floating animation-delay-1000">
                  <TrendingUp className="h-10 w-10 text-white" />
                </div>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">{t.process4Title}</h3>
              <p className="text-white/70">{t.process4Desc}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Integration Section */}
      <section className="relative z-10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in-up">{t.integrationTitle}</h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto animate-fade-in-up animation-delay-200">{t.integrationSubtitle}</p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
            {/* Integration Icons */}
            <div className="flex flex-col items-center group animate-fade-in-up animation-delay-400">
              <div className="w-16 h-16 glass-card border border-white/20 rounded-2xl flex items-center justify-center mb-4 group-hover:border-white/40 transition-all duration-300 floating">
                <svg className="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M23.643 4.937c-.835.37-1.732.62-2.675.733.962-.576 1.7-1.49 2.048-2.578-.9.534-1.897.922-2.958 1.13-.85-.904-2.06-1.47-3.4-1.47-2.572 0-4.658 2.086-4.658 4.66 0 .364.042.718.12 1.06-3.873-.195-7.304-2.05-9.602-4.868-.4.69-.63 1.49-.63 2.342 0 1.616.823 3.043 2.072 3.878-.764-.025-1.482-.234-2.11-.583v.06c0 2.257 1.605 4.14 3.737 4.568-.392.106-.803.162-1.227.162-.3 0-.593-.028-.877-.082.593 1.85 2.313 3.198 4.352 3.234-1.595 1.25-3.604 1.995-5.786 1.995-.376 0-.747-.022-1.112-.065 2.062 1.323 4.51 2.093 7.14 2.093 8.57 0 13.255-7.098 13.255-13.254 0-.2-.005-.402-.014-.602.91-.658 1.7-1.477 2.323-2.41z"/>
                </svg>
              </div>
              <span className="text-white/70 text-sm">Twitter</span>
            </div>

            <div className="flex flex-col items-center group animate-fade-in-up animation-delay-600">
              <div className="w-16 h-16 glass-card border border-white/20 rounded-2xl flex items-center justify-center mb-4 group-hover:border-white/40 transition-all duration-300 floating animation-delay-2000">
                <svg className="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                </svg>
              </div>
              <span className="text-white/70 text-sm">Slack</span>
            </div>

            <div className="flex flex-col items-center group animate-fade-in-up animation-delay-800">
              <div className="w-16 h-16 glass-card border border-white/20 rounded-2xl flex items-center justify-center mb-4 group-hover:border-white/40 transition-all duration-300 floating animation-delay-4000">
                <svg className="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418Z"/>
                </svg>
              </div>
              <span className="text-white/70 text-sm">Discord</span>
            </div>

            <div className="flex flex-col items-center group animate-fade-in-up animation-delay-1000">
              <div className="w-16 h-16 glass-card border border-white/20 rounded-2xl flex items-center justify-center mb-4 group-hover:border-white/40 transition-all duration-300 floating animation-delay-1000">
                <svg className="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                </svg>
              </div>
              <span className="text-white/70 text-sm">Zoom</span>
            </div>

            <div className="flex flex-col items-center group animate-fade-in-up animation-delay-1200">
              <div className="w-16 h-16 glass-card border border-white/20 rounded-2xl flex items-center justify-center mb-4 group-hover:border-white/40 transition-all duration-300 floating animation-delay-2000">
                <svg className="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                </svg>
              </div>
              <span className="text-white/70 text-sm">Pinterest</span>
            </div>

            <div className="flex flex-col items-center group animate-fade-in-up animation-delay-1400">
              <div className="w-16 h-16 glass-card border border-white/20 rounded-2xl flex items-center justify-center mb-4 group-hover:border-white/40 transition-all duration-300 floating animation-delay-4000">
                <svg className="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </div>
              <span className="text-white/70 text-sm">LinkedIn</span>
            </div>
          </div>

          <div className="text-center mt-16">
            <p className="text-white/60 text-lg mb-8">
              {language === 'ar'
                ? 'وأكثر من 100+ تطبيق وخدمة أخرى'
                : 'And 100+ more apps and services'
              }
            </p>
            <Button className="glass-button border-white/30 hover:border-white/50">
              {language === 'ar' ? 'عرض جميع التكاملات' : 'View All Integrations'}
            </Button>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="relative z-10 py-20 bg-black/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in-up">{t.faqTitle}</h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto animate-fade-in-up animation-delay-200">{t.faqSubtitle}</p>
          </div>

          <div className="space-y-6">
            {/* FAQ Item 1 */}
            <div className="glass-card p-6 rounded-2xl border border-white/20 animate-fade-in-up animation-delay-400">
              <h3 className="text-xl font-bold text-white mb-3">
                {language === 'ar' ? 'ما هي مدة التجربة المجانية؟' : 'How long is the free trial?'}
              </h3>
              <p className="text-white/70">
                {language === 'ar'
                  ? 'نوفر تجربة مجانية لمدة 30 يوماً كاملة مع إمكانية الوصول لجميع الميزات الأساسية.'
                  : 'We offer a full 30-day free trial with access to all basic features.'
                }
              </p>
            </div>

            {/* FAQ Item 2 */}
            <div className="glass-card p-6 rounded-2xl border border-white/20 animate-fade-in-up animation-delay-600">
              <h3 className="text-xl font-bold text-white mb-3">
                {language === 'ar' ? 'هل يمكنني تغيير الخطة لاحقاً؟' : 'Can I change my plan later?'}
              </h3>
              <p className="text-white/70">
                {language === 'ar'
                  ? 'نعم، يمكنك ترقية أو تخفيض خطتك في أي وقت. التغييرات تطبق فوراً.'
                  : 'Yes, you can upgrade or downgrade your plan at any time. Changes apply immediately.'
                }
              </p>
            </div>

            {/* FAQ Item 3 */}
            <div className="glass-card p-6 rounded-2xl border border-white/20 animate-fade-in-up animation-delay-800">
              <h3 className="text-xl font-bold text-white mb-3">
                {language === 'ar' ? 'هل بياناتي آمنة؟' : 'Is my data secure?'}
              </h3>
              <p className="text-white/70">
                {language === 'ar'
                  ? 'نعم، نستخدم أحدث تقنيات التشفير ونحتفظ بنسخ احتياطية يومية من جميع البيانات.'
                  : 'Yes, we use the latest encryption technologies and maintain daily backups of all data.'
                }
              </p>
            </div>

            {/* FAQ Item 4 */}
            <div className="glass-card p-6 rounded-2xl border border-white/20 animate-fade-in-up animation-delay-1000">
              <h3 className="text-xl font-bold text-white mb-3">
                {language === 'ar' ? 'هل يوجد دعم فني؟' : 'Is technical support available?'}
              </h3>
              <p className="text-white/70">
                {language === 'ar'
                  ? 'نوفر دعماً فنياً 24/7 عبر الهاتف والبريد الإلكتروني والدردشة المباشرة.'
                  : 'We provide 24/7 technical support via phone, email, and live chat.'
                }
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="glass-card p-12 rounded-3xl border border-white/20 text-center">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">{t.ctaTitle}</h2>
            <p className="text-xl text-white/70 mb-8 max-w-3xl mx-auto">{t.ctaSubtitle}</p>
            <Link to="/login">
              <Button size="lg" className="glass-button bg-gradient-to-r from-blue-500 to-purple-500 text-lg px-8 py-4 h-auto">
                <ArrowRight className="h-5 w-5 mr-2" />
                {t.ctaButton}
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer id="contact" className="relative z-10 bg-black/40 border-t border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
            {/* Company Info */}
            <div className="md:col-span-2">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center glow">
                  <span className="text-white text-xl font-bold">ن</span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">{t.appName}</h3>
                  <p className="text-white/60 text-sm">{t.footerTagline}</p>
                </div>
              </div>
              <p className="text-white/70 mb-6 max-w-md leading-relaxed">{t.footerDescription}</p>

              {/* Contact Info */}
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-white/70">
                  <Phone className="h-5 w-5" />
                  <span>+966 11 123 4567</span>
                </div>
                <div className="flex items-center gap-3 text-white/70">
                  <Mail className="h-5 w-5" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center gap-3 text-white/70">
                  <MapPin className="h-5 w-5" />
                  <span>الرياض، المملكة العربية السعودية</span>
                </div>
              </div>
            </div>

            {/* Solutions */}
            <div>
              <h4 className="text-lg font-semibold text-white mb-6">{t.solutions}</h4>
              <ul className="space-y-3">
                <li><a href="#" className="text-white/70 hover:text-white transition-colors">{t.erpSolution}</a></li>
                <li><a href="#" className="text-white/70 hover:text-white transition-colors">{t.crmSolution}</a></li>
                <li><a href="#" className="text-white/70 hover:text-white transition-colors">{t.hrSolution}</a></li>
                <li><a href="#" className="text-white/70 hover:text-white transition-colors">{t.projectManagement}</a></li>
                <li><a href="#" className="text-white/70 hover:text-white transition-colors">{t.kpiManagement}</a></li>
                <li><a href="#" className="text-white/70 hover:text-white transition-colors">{t.aiAnalytics}</a></li>
                <li><a href="#" className="text-white/70 hover:text-white transition-colors">{t.compliance}</a></li>
              </ul>
            </div>

            {/* Industries */}
            <div>
              <h4 className="text-lg font-semibold text-white mb-6">{t.industries}</h4>
              <ul className="space-y-3">
                <li><a href="#" className="text-white/70 hover:text-white transition-colors">{t.manufacturing}</a></li>
                <li><a href="#" className="text-white/70 hover:text-white transition-colors">{t.consulting}</a></li>
                <li><a href="#" className="text-white/70 hover:text-white transition-colors">{t.services}</a></li>
                <li><a href="#" className="text-white/70 hover:text-white transition-colors">{t.government}</a></li>
                <li><a href="#" className="text-white/70 hover:text-white transition-colors">{t.support}</a></li>
                <li><a href="#" className="text-white/70 hover:text-white transition-colors">{t.pricing}</a></li>
              </ul>
            </div>

            {/* Legal & Social */}
            <div>
              <h4 className="text-lg font-semibold text-white mb-6">{t.followUs}</h4>

              {/* Social Links */}
              <div className="flex gap-4 mb-6">
                <a href="#" className="w-10 h-10 glass-card border-white/20 rounded-lg flex items-center justify-center hover:border-white/40 transition-colors">
                  <Facebook className="h-5 w-5 text-white/70" />
                </a>
                <a href="#" className="w-10 h-10 glass-card border-white/20 rounded-lg flex items-center justify-center hover:border-white/40 transition-colors">
                  <Twitter className="h-5 w-5 text-white/70" />
                </a>
                <a href="#" className="w-10 h-10 glass-card border-white/20 rounded-lg flex items-center justify-center hover:border-white/40 transition-colors">
                  <Linkedin className="h-5 w-5 text-white/70" />
                </a>
                <a href="#" className="w-10 h-10 glass-card border-white/20 rounded-lg flex items-center justify-center hover:border-white/40 transition-colors">
                  <Instagram className="h-5 w-5 text-white/70" />
                </a>
              </div>

              {/* Legal Links */}
              <ul className="space-y-3">
                <li><a href="#" className="text-white/70 hover:text-white transition-colors text-sm">{t.privacy}</a></li>
                <li><a href="#" className="text-white/70 hover:text-white transition-colors text-sm">{t.terms}</a></li>
              </ul>
            </div>
          </div>

          {/* Copyright */}
          <div className="border-t border-white/10 mt-12 pt-8 text-center">
            <p className="text-white/60">
              © 2024 {t.appName}. {t.allRightsReserved}
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}