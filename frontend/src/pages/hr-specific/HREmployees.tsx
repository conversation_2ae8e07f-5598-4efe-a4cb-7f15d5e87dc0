/**
 * HR Employees Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  UserCheck,
  Calendar,
  Award,
  AlertTriangle,
  CheckCircle,
  Eye,
  Edit,
  Trash2,
  User,
  Mail,
  Phone,
  MapPin
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { hrEmployeesService } from '@/services/crudService'
import { employeeAPI, Employee } from '@/services/employeeAPI'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import EnhancedCrudModal, { EnhancedFormField } from '@/components/common/EnhancedCrudModal'
import { enhancedAPI } from '@/services/enhancedAPI'
import { employeeValidationSchema } from '@/utils/validation'

interface HREmployeesProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    hrEmployees: 'إدارة الموظفين - الموارد البشرية',
    addEmployee: 'إضافة موظف',
    editEmployee: 'تعديل الموظف',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا الموظف؟',
    searchPlaceholder: 'البحث في الموظفين...',
    name: 'الاسم',
    email: 'البريد الإلكتروني',
    phone: 'الهاتف',
    department: 'القسم',
    position: 'المنصب',
    status: 'الحالة',
    performance: 'الأداء',
    salary: 'الراتب',
    joinDate: 'تاريخ الانضمام',
    address: 'العنوان',
    emergencyContact: 'جهة الاتصال الطارئة',
    nationalId: 'رقم الهوية الوطنية',
    active: 'نشط',
    inactive: 'غير نشط',
    onLeave: 'في إجازة',
    excellent: 'ممتاز',
    good: 'جيد',
    average: 'متوسط',
    needsImprovement: 'يحتاج تحسين',
    departments: {
      hr: 'الموارد البشرية',
      it: 'تقنية المعلومات',
      finance: 'المالية',
      sales: 'المبيعات',
      marketing: 'التسويق',
      operations: 'العمليات'
    }
  },
  en: {
    hrEmployees: 'HR Employee Management',
    addEmployee: 'Add Employee',
    editEmployee: 'Edit Employee',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this employee?',
    searchPlaceholder: 'Search employees...',
    name: 'Name',
    email: 'Email',
    phone: 'Phone',
    department: 'Department',
    position: 'Position',
    status: 'Status',
    performance: 'Performance',
    salary: 'Salary',
    joinDate: 'Join Date',
    address: 'Address',
    emergencyContact: 'Emergency Contact',
    nationalId: 'National ID',
    active: 'Active',
    inactive: 'Inactive',
    onLeave: 'On Leave',
    excellent: 'Excellent',
    good: 'Good',
    average: 'Average',
    needsImprovement: 'Needs Improvement',
    departments: {
      hr: 'Human Resources',
      it: 'Information Technology',
      finance: 'Finance',
      sales: 'Sales',
      marketing: 'Marketing',
      operations: 'Operations'
    }
  }
}

export default function HREmployees({ language }: HREmployeesProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: employees,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Employee>({
    service: hrEmployeesService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'onLeave':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent':
        return 'text-green-400'
      case 'good':
        return 'text-blue-400'
      case 'average':
        return 'text-yellow-400'
      case 'needsImprovement':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'excellent':
        return <Award className="h-4 w-4" />
      case 'good':
        return <CheckCircle className="h-4 w-4" />
      case 'average':
        return <Calendar className="h-4 w-4" />
      case 'needsImprovement':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <Calendar className="h-4 w-4" />
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Event handlers (defined before they're used)
  const handleCreate = () => {
    console.log('🔧 DEBUG: handleCreate called')
    console.log('🔧 DEBUG: Setting modal mode to create')
    setModalMode('create')
    console.log('🔧 DEBUG: Setting showModal to true')
    setShowModal(true)
    console.log('🔧 DEBUG: Modal should now be open')
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Employee>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleDelete = async (employee: Employee) => {
    try {
      if (window.confirm(t.confirmDelete)) {
        await deleteItem(employee.id)
      }
    } catch (error) {
      console.error('Delete error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('excel')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  // Table columns configuration
  const columns: TableColumn<Employee>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: Employee) => {
        const displayName = language === 'ar' ? (item.nameAr || item.name) : (item.name || item.nameAr)
        const firstLetter = displayName ? displayName.charAt(0).toUpperCase() : '?'

        return (
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
              {item.avatar || firstLetter}
            </div>
            <div>
              <div className="font-medium text-white">
                {displayName || 'N/A'}
              </div>
              <div className="text-sm text-white/60 flex items-center gap-1">
                <Mail className="h-3 w-3" />
                {item.email || 'N/A'}
              </div>
            </div>
          </div>
        )
      }
    },
    {
      key: 'department',
      label: t.department,
      sortable: true,
      render: (item: Employee) => {
        const department = language === 'ar' ? (item.departmentAr || item.department) : (item.department || item.departmentAr)
        return (
          <span className="text-white/80">
            {department || 'N/A'}
          </span>
        )
      }
    },
    {
      key: 'position',
      label: t.position,
      render: (item: Employee) => {
        const position = language === 'ar' ? (item.positionAr || item.position) : (item.position || item.positionAr)
        return (
          <span className="text-white/80">
            {position || 'N/A'}
          </span>
        )
      }
    },
    {
      key: 'phone',
      label: t.phone,
      render: (item: Employee) => (
        <div className="flex items-center gap-1">
          <Phone className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.phone || 'N/A'}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Employee) => {
        const status = item.status || 'active'
        return (
          <Badge className={getStatusColor(status)}>
            {t[status as keyof typeof t]}
          </Badge>
        )
      }
    },
    {
      key: 'performance',
      label: t.performance,
      sortable: true,
      render: (item: Employee) => {
        const performance = item.performance || 'average'
        return (
          <div className={`flex items-center gap-2 ${getPerformanceColor(performance)}`}>
            {getPerformanceIcon(performance)}
            <span className="text-sm font-medium">
              {t[performance as keyof typeof t]}
            </span>
          </div>
        )
      }
    },
    {
      key: 'salary',
      label: t.salary,
      sortable: true,
      render: (item: Employee) => (
        <span className="text-white font-medium">{formatCurrency(item.salary || 0)}</span>
      )
    },
    {
      key: 'joinDate',
      label: t.joinDate,
      sortable: true,
      render: (item: Employee) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.joinDate || 'N/A'}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Employee) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Employee) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: (item: Employee) => handleDelete(item),
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.onLeave, value: 'onLeave' }
      ]
    },
    {
      key: 'performance',
      label: t.performance,
      options: [
        { label: t.excellent, value: 'excellent' },
        { label: t.good, value: 'good' },
        { label: t.average, value: 'average' },
        { label: t.needsImprovement, value: 'needsImprovement' }
      ]
    },
    {
      key: 'department',
      label: t.department,
      options: [
        { label: t.departments.hr, value: 'hr' },
        { label: t.departments.it, value: 'it' },
        { label: t.departments.finance, value: 'finance' },
        { label: t.departments.sales, value: 'sales' },
        { label: t.departments.marketing, value: 'marketing' },
        { label: t.departments.operations, value: 'operations' }
      ]
    }
  ]

  // Enhanced form fields configuration with validation
  const formFields: EnhancedFormField[] = [
    {
      name: 'firstName',
      label: language === 'ar' ? 'الاسم الأول' : 'First Name',
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل الاسم الأول' : 'Enter first name'
    },
    {
      name: 'lastName',
      label: language === 'ar' ? 'الاسم الأخير' : 'Last Name',
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل الاسم الأخير' : 'Enter last name'
    },
    {
      name: 'email',
      label: t.email,
      type: 'email',
      required: true,
      placeholder: language === 'ar' ? 'أدخل البريد الإلكتروني' : 'Enter email address'
    },
    {
      name: 'phone',
      label: t.phone,
      type: 'tel',
      required: true,
      placeholder: language === 'ar' ? 'أدخل رقم الهاتف' : 'Enter phone number'
    },
    {
      name: 'departmentId',
      label: t.department,
      type: 'select',
      required: true,
      options: [
        { label: t.departments.hr, value: '1' },
        { label: t.departments.it, value: '2' },
        { label: t.departments.finance, value: '3' },
        { label: t.departments.sales, value: '4' },
        { label: t.departments.marketing, value: '5' },
        { label: t.departments.operations, value: '6' }
      ]
    },
    {
      name: 'position',
      label: t.position,
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل المنصب' : 'Enter position'
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.onLeave, value: 'onLeave' }
      ]
    },
    {
      name: 'performance',
      label: t.performance,
      type: 'select',
      required: true,
      options: [
        { label: t.excellent, value: 'excellent' },
        { label: t.good, value: 'good' },
        { label: t.average, value: 'average' },
        { label: t.needsImprovement, value: 'needsImprovement' }
      ]
    },
    {
      name: 'salary',
      label: t.salary,
      type: 'number',
      required: true,
      min: 1000,
      max: 1000000,
      placeholder: language === 'ar' ? 'أدخل الراتب' : 'Enter salary'
    },
    {
      name: 'hireDate',
      label: t.joinDate,
      type: 'date',
      required: true
    },
    {
      name: 'address',
      label: t.address,
      type: 'textarea',
      required: true,
      rows: 3,
      placeholder: language === 'ar' ? 'أدخل العنوان' : 'Enter address'
    },
    {
      name: 'emergencyContact',
      label: t.emergencyContact,
      type: 'tel',
      required: true,
      placeholder: language === 'ar' ? 'أدخل رقم الاتصال الطارئ' : 'Enter emergency contact'
    },
    {
      name: 'nationalId',
      label: t.nationalId,
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل رقم الهوية الوطنية' : 'Enter national ID'
    }
  ]



  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.hrEmployees}
        data={employees}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* Enhanced CRUD Modal with Validation */}
      {console.log('🔧 DEBUG: Rendering modal with showModal =', showModal)}
      <EnhancedCrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addEmployee : modalMode === 'edit' ? t.editEmployee : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        validationSchema={employeeValidationSchema}
      />
    </div>
  )

}
