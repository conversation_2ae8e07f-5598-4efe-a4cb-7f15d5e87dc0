/**
 * Employees Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  Eye,
  Edit,
  Trash2,
  Mail,
  Phone,
  Calendar,
  Building,
  User,
  MapPin
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { employeeService } from '@/services/crudService'
import { employeeAPI, Employee } from '@/services/employeeAPI'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface EmployeesProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    employees: 'الموظفين',
    addEmployee: 'إضافة موظف',
    editEmployee: 'تعديل الموظف',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا الموظف؟',
    searchPlaceholder: 'البحث في الموظفين...',
    firstName: 'الاسم الأول',
    lastName: 'الاسم الأخير',
    email: 'البريد الإلكتروني',
    employeeId: 'رقم الموظف',
    position: 'المنصب',
    department: 'القسم',
    salary: 'الراتب',
    hireDate: 'تاريخ التوظيف',
    phone: 'الهاتف',
    address: 'العنوان',
    status: 'الحالة',
    isActive: 'نشط',
    active: 'نشط',
    inactive: 'غير نشط',
    terminated: 'منتهي الخدمة',
    'on-leave': 'في إجازة',
    departments: {
      hr: 'الموارد البشرية',
      finance: 'المالية',
      it: 'تقنية المعلومات',
      sales: 'المبيعات',
      marketing: 'التسويق',
      operations: 'العمليات'
    }
  },
  en: {
    employees: 'Employees',
    addEmployee: 'Add Employee',
    editEmployee: 'Edit Employee',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this employee?',
    searchPlaceholder: 'Search employees...',
    firstName: 'First Name',
    lastName: 'Last Name',
    email: 'Email',
    employeeId: 'Employee ID',
    position: 'Position',
    department: 'Department',
    salary: 'Salary',
    hireDate: 'Hire Date',
    phone: 'Phone',
    address: 'Address',
    status: 'Status',
    isActive: 'Active',
    active: 'Active',
    inactive: 'Inactive',
    terminated: 'Terminated',
    'on-leave': 'On Leave',
    departments: {
      hr: 'Human Resources',
      finance: 'Finance',
      it: 'Information Technology',
      sales: 'Sales',
      marketing: 'Marketing',
      operations: 'Operations'
    }
  }
}

export default function Employees({ language }: EmployeesProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: employees,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Employee>({
    service: employeeService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive': return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'terminated': return 'bg-red-100 text-red-800 border-red-200'
      case 'on-leave': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Table columns configuration
  const columns: TableColumn<Employee>[] = [
    {
      key: 'name',
      label: t.firstName + ' & ' + t.lastName,
      sortable: true,
      render: (item: Employee) => (
        <div className="flex items-center gap-2">
          <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
            {((item.firstName || '').charAt(0) + (item.lastName || '').charAt(0)).toUpperCase() || 'N/A'}
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar'
                ? `${item.firstNameAr || ''} ${item.lastNameAr || ''}`.trim() || 'غير محدد'
                : `${item.firstName || ''} ${item.lastName || ''}`.trim() || 'Not specified'
              }
            </div>
            <div className="text-sm text-white/60">{item.employeeId}</div>
          </div>
        </div>
      )
    },
    {
      key: 'position',
      label: t.position,
      render: (item: Employee) => (
        <span className="text-white/80">
          {language === 'ar' ? item.positionAr : item.position}
        </span>
      )
    },
    {
      key: 'department',
      label: t.department,
      render: (item: Employee) => (
        <div className="flex items-center gap-1">
          <Building className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.departmentAr : item.department}
          </span>
        </div>
      )
    },
    {
      key: 'email',
      label: t.email,
      render: (item: Employee) => (
        <div className="flex items-center gap-1">
          <Mail className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.email}</span>
        </div>
      )
    },
    {
      key: 'phone',
      label: t.phone,
      render: (item: Employee) => (
        <div className="flex items-center gap-1">
          <Phone className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.phone}</span>
        </div>
      )
    },
    {
      key: 'hireDate',
      label: t.hireDate,
      sortable: true,
      render: (item: Employee) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-orange-400" />
          <span className="text-white/80">{item.hireDate}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Employee) => (
        <Badge className={getStatusColor(item.status)}>
          {t[item.status as keyof typeof t]}
        </Badge>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Employee) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Employee) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: Employee) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.terminated, value: 'terminated' },
        { label: t['on-leave'], value: 'on-leave' }
      ]
    },
    {
      key: 'department',
      label: t.department,
      options: [
        { label: t.departments.hr, value: 'hr' },
        { label: t.departments.finance, value: 'finance' },
        { label: t.departments.it, value: 'it' },
        { label: t.departments.sales, value: 'sales' },
        { label: t.departments.marketing, value: 'marketing' },
        { label: t.departments.operations, value: 'operations' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'firstName',
      label: t.firstName,
      type: 'text',
      required: true
    },
    {
      name: 'firstNameAr',
      label: t.firstName + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'lastName',
      label: t.lastName,
      type: 'text',
      required: true
    },
    {
      name: 'lastNameAr',
      label: t.lastName + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'email',
      label: t.email,
      type: 'email',
      required: true
    },
    {
      name: 'employee_id',
      label: t.employeeId,
      type: 'text',
      required: true
    },
    {
      name: 'position',
      label: t.position,
      type: 'text',
      required: true
    },
    {
      name: 'position_ar',
      label: t.position + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'department',
      label: t.department,
      type: 'select',
      required: true,
      options: [
        { label: 'Customer Service', value: 30 },
        { label: 'Finance', value: 25 },
        { label: 'Human Resources', value: 24 },
        { label: 'IT', value: 32 },
        { label: 'Information Technology', value: 26 },
        { label: 'Marketing', value: 28 },
        { label: 'Operations', value: 29 },
        { label: 'Research & Development', value: 31 },
        { label: 'Sales', value: 27 }
      ]
    },
    {
      name: 'salary',
      label: t.salary,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'hire_date',
      label: t.hireDate,
      type: 'date',
      required: true
    },
    {
      name: 'gender',
      label: language === 'ar' ? 'الجنس' : 'Gender',
      type: 'select',
      required: true,
      options: [
        { label: language === 'ar' ? 'ذكر' : 'Male', value: 'M' },
        { label: language === 'ar' ? 'أنثى' : 'Female', value: 'F' }
      ]
    },
    {
      name: 'phone',
      label: t.phone,
      type: 'tel'
    },
    {
      name: 'address',
      label: t.address,
      type: 'textarea'
    },
    {
      name: 'addressAr',
      label: t.address + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.terminated, value: 'terminated' },
        { label: t['on-leave'], value: 'on-leave' }
      ]
    },
    {
      name: 'isActive',
      label: t.isActive,
      type: 'checkbox'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Employee>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' | 'pdf' = 'csv') => {
    try {
      if (format === 'pdf') {
        // Use new PDF generation API for employee reports
        const response = await fetch(`http://localhost:8001/api/pdf/generate/employee-report/?language=${language}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const pdfBlob = await response.blob()
        const url = window.URL.createObjectURL(pdfBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `employee-report-${language}-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        await exportData('employees')
      }
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.employees}
        data={employees}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addEmployee : modalMode === 'edit' ? t.editEmployee : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )

}
