/**
 * Department Customers Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  Building,
  Eye,
  Edit,
  Trash2,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  User,
  Briefcase,
  Target,
  TrendingUp,
  DollarSign
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { departmentCustomerService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface DepartmentCustomersProps {
  language: 'ar' | 'en'
}

interface DepartmentCustomer {
  id: number
  name: string
  nameAr: string
  projectName: string
  projectNameAr: string
  assignedTo: string
  assignedToAr: string
  projectStatus: 'inProgress' | 'completed' | 'onHold' | 'planning'
  priority: 'high' | 'medium' | 'low'
  deadline: string
  progress: number
  budget: number
  lastUpdate: string
  startDate: string
  teamMembers: string[]
  teamMembersAr: string[]
  department: string
  departmentAr: string
  contactEmail: string
  contactPhone: string
}

const translations = {
  ar: {
    departmentCustomers: 'عملاء القسم',
    addCustomer: 'إضافة عميل',
    editCustomer: 'تعديل العميل',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا العميل؟',
    searchPlaceholder: 'البحث في العملاء...',
    name: 'اسم العميل',
    projectName: 'اسم المشروع',
    assignedTo: 'مخصص لـ',
    projectStatus: 'حالة المشروع',
    priority: 'الأولوية',
    deadline: 'الموعد النهائي',
    progress: 'التقدم',
    budget: 'الميزانية',
    lastUpdate: 'آخر تحديث',
    startDate: 'تاريخ البداية',
    teamMembers: 'أعضاء الفريق',
    department: 'القسم',
    contactEmail: 'البريد الإلكتروني',
    contactPhone: 'رقم الهاتف',
    inProgress: 'قيد التنفيذ',
    completed: 'مكتمل',
    onHold: 'معلق',
    planning: 'تخطيط',
    high: 'عالية',
    medium: 'متوسطة',
    low: 'منخفضة'
  },
  en: {
    departmentCustomers: 'Department Customers',
    addCustomer: 'Add Customer',
    editCustomer: 'Edit Customer',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this customer?',
    searchPlaceholder: 'Search customers...',
    name: 'Customer Name',
    projectName: 'Project Name',
    assignedTo: 'Assigned To',
    projectStatus: 'Project Status',
    priority: 'Priority',
    deadline: 'Deadline',
    progress: 'Progress',
    budget: 'Budget',
    lastUpdate: 'Last Update',
    startDate: 'Start Date',
    teamMembers: 'Team Members',
    department: 'Department',
    contactEmail: 'Contact Email',
    contactPhone: 'Contact Phone',
    inProgress: 'In Progress',
    completed: 'Completed',
    onHold: 'On Hold',
    planning: 'Planning',
    high: 'High',
    medium: 'Medium',
    low: 'Low'
  }
}

export default function DepartmentCustomers({ language }: DepartmentCustomersProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: customers,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<DepartmentCustomer>({
    service: departmentCustomerService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'inProgress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'onHold':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'planning':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'inProgress':
        return <Clock className="h-3 w-3" />
      case 'completed':
        return <CheckCircle className="h-3 w-3" />
      case 'onHold':
        return <AlertTriangle className="h-3 w-3" />
      case 'planning':
        return <Target className="h-3 w-3" />
      default:
        return <Clock className="h-3 w-3" />
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 90) return 'bg-green-500'
    if (progress >= 70) return 'bg-blue-500'
    if (progress >= 50) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<DepartmentCustomer>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: DepartmentCustomer) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
            {((language === 'ar' ? item.nameAr : item.name) || '').charAt(0) || '?'}
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.nameAr : item.name}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.projectNameAr : item.projectName}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'projectStatus',
      label: t.projectStatus,
      sortable: true,
      render: (item: DepartmentCustomer) => (
        <div className="flex items-center gap-1">
          {getStatusIcon(item.projectStatus)}
          <Badge className={getStatusColor(item.projectStatus)}>
            {t[item.projectStatus as keyof typeof t]}
          </Badge>
        </div>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: DepartmentCustomer) => (
        <Badge className={getPriorityColor(item.priority)}>
          {t[item.priority as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'progress',
      label: t.progress,
      sortable: true,
      render: (item: DepartmentCustomer) => (
        <div className="flex items-center gap-2">
          <div className="w-16 bg-white/20 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${getProgressColor(item.progress)}`}
              style={{ width: `${item.progress}%` }}
            ></div>
          </div>
          <span className="text-white text-sm font-medium">{item.progress}%</span>
        </div>
      )
    },
    {
      key: 'assignedTo',
      label: t.assignedTo,
      render: (item: DepartmentCustomer) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.assignedToAr : item.assignedTo}
          </span>
        </div>
      )
    },
    {
      key: 'budget',
      label: t.budget,
      sortable: true,
      render: (item: DepartmentCustomer) => (
        <div className="flex items-center gap-1">
          <DollarSign className="h-3 w-3 text-green-400" />
          <span className="text-green-400 font-medium">
            {formatCurrency(item.budget)}
          </span>
        </div>
      )
    },
    {
      key: 'deadline',
      label: t.deadline,
      sortable: true,
      render: (item: DepartmentCustomer) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.deadline}</span>
        </div>
      )
    },
    {
      key: 'teamMembers',
      label: t.teamMembers,
      render: (item: DepartmentCustomer) => (
        <div className="flex items-center gap-1">
          <Users className="h-3 w-3 text-orange-400" />
          <span className="text-white/80">{item.teamMembers.length}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: DepartmentCustomer) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: DepartmentCustomer) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: DepartmentCustomer) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'projectStatus',
      label: t.projectStatus,
      options: [
        { label: t.inProgress, value: 'inProgress' },
        { label: t.completed, value: 'completed' },
        { label: t.onHold, value: 'onHold' },
        { label: t.planning, value: 'planning' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'projectName',
      label: t.projectName,
      type: 'text',
      required: true
    },
    {
      name: 'projectNameAr',
      label: t.projectName + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'assignedTo',
      label: t.assignedTo,
      type: 'text',
      required: true
    },
    {
      name: 'assignedToAr',
      label: t.assignedTo + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'projectStatus',
      label: t.projectStatus,
      type: 'select',
      required: true,
      options: [
        { label: t.inProgress, value: 'inProgress' },
        { label: t.completed, value: 'completed' },
        { label: t.onHold, value: 'onHold' },
        { label: t.planning, value: 'planning' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    },
    {
      name: 'deadline',
      label: t.deadline,
      type: 'date',
      required: true
    },
    {
      name: 'progress',
      label: t.progress,
      type: 'number',
      required: true,
      min: 0,
      max: 100
    },
    {
      name: 'budget',
      label: t.budget,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'lastUpdate',
      label: t.lastUpdate,
      type: 'date',
      required: true
    },
    {
      name: 'startDate',
      label: t.startDate,
      type: 'date',
      required: true
    },
    {
      name: 'department',
      label: t.department,
      type: 'text',
      required: true
    },
    {
      name: 'departmentAr',
      label: t.department + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'contactEmail',
      label: t.contactEmail,
      type: 'email',
      required: true
    },
    {
      name: 'contactPhone',
      label: t.contactPhone,
      type: 'tel',
      required: true
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<DepartmentCustomer>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('department-customers')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.departmentCustomers}
        data={customers}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addCustomer : modalMode === 'edit' ? t.editCustomer : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
