#!/usr/bin/env python3
"""
Test Super Admin Pages Specifically
Tests all the pages that should be available to Super Admin users
"""

import requests
import json

# Configuration
FRONTEND_URL = "http://localhost:5174"
BACKEND_URL = "http://localhost:8000"

def test_superadmin_pages():
    """Test all Super Admin pages that should be available"""
    
    # Get authentication token
    try:
        response = requests.post(
            f"{BACKEND_URL}/api/auth/login/",
            json={"username": "superadmin", "password": "password123"},
            timeout=5
        )
        if response.status_code != 200:
            print("❌ Authentication failed")
            return
        
        token = response.json().get('access')
        print("✅ Authentication successful")
    except Exception as e:
        print(f"❌ Authentication error: {str(e)}")
        return

    # Super Admin pages that should be available after the fix
    superadmin_pages = [
        # Core System Administration (from PureSuperAdminRoutes)
        ("/admin/dashboard", "Super Admin Dashboard"),
        ("/admin/system", "System Settings"),
        ("/admin/security", "Security Center"),
        ("/admin/ai", "AI Management"),
        ("/admin/analytics", "Advanced Analytics"),
        ("/admin/compliance", "Compliance Center"),
        ("/admin/users", "User Management"),
        
        # Additional pages (from SuperAdminRoutes)
        ("/admin/employees", "Employee Management"),
        ("/admin/departments", "Department Management"),
        ("/admin/customers", "Customer Management"),
        ("/admin/reports", "Reports"),
        ("/admin/settings", "Settings"),
        
        # HR Management
        ("/admin/hr/leave", "Leave Management"),
        ("/admin/hr/attendance", "Attendance"),
        ("/admin/hr/performance", "Performance"),
        ("/admin/hr/payroll", "Payroll"),
        
        # Project Management
        ("/admin/projects", "Projects"),
        ("/admin/tasks", "Tasks"),
        ("/admin/project-reports", "Project Reports"),
        
        # Financial Management
        ("/admin/finance/budgets", "Finance Budgets"),
        ("/admin/finance/reports", "Finance Reports"),
        ("/admin/expenses", "Expenses"),
        
        # Asset Management
        ("/admin/assets", "Assets"),
        ("/admin/suppliers", "Suppliers"),
        ("/admin/purchase-orders", "Purchase Orders"),
        ("/admin/inventory", "Inventory"),
        
        # Sales Management
        ("/admin/sales/orders", "Sales Orders"),
        ("/admin/sales/quotations", "Quotations"),
        ("/admin/sales/pipeline", "Sales Pipeline"),
        
        # Communication
        ("/admin/communication/messages", "Messages"),
        ("/admin/communication/announcements", "Announcements"),
        ("/admin/communication/documents", "Documents"),
        ("/admin/communication/meetings", "Meetings"),
        
        # Personal Features
        ("/admin/personal/profile", "Personal Profile"),
        ("/admin/personal/messages", "Personal Messages"),
        ("/admin/personal/calendar", "Personal Calendar"),
        
        # Advanced Features
        ("/admin/business-intelligence", "Business Intelligence"),
        ("/admin/advanced-dashboard", "Advanced Dashboard"),
        ("/admin/kpi/dashboard", "KPI Dashboard"),
        ("/admin/kpi/management", "KPI Management"),
        ("/admin/workflows", "Workflow Automation"),
        ("/admin/report-generator", "Report Generator"),
        
        # Products & Inventory
        ("/admin/products", "Products"),
        ("/admin/vendor-management", "Vendor Management"),
        
        # Employee Specific
        ("/admin/employee/profile", "Employee Profile"),
        ("/admin/employee/leave", "Employee Leave"),
        ("/admin/employee/tasks", "Employee Tasks"),
        
        # Calendar
        ("/admin/calendar", "Calendar"),
    ]

    print(f"\n🧪 Testing {len(superadmin_pages)} Super Admin pages...")
    
    working_pages = []
    broken_pages = []
    
    for route, page_name in superadmin_pages:
        try:
            # Test if the frontend can serve the page
            full_url = f"{FRONTEND_URL}{route}"
            response = requests.get(full_url, timeout=3)
            
            if response.status_code == 200:
                # Check if it's actually serving the React app
                if 'react' in response.text.lower() or 'vite' in response.text.lower() or len(response.text) > 1000:
                    working_pages.append((route, page_name))
                    print(f"✅ {page_name}: {route}")
                else:
                    broken_pages.append((route, page_name, "Invalid content"))
                    print(f"⚠️ {page_name}: {route} - Invalid content")
            else:
                broken_pages.append((route, page_name, f"HTTP {response.status_code}"))
                print(f"❌ {page_name}: {route} - HTTP {response.status_code}")
                
        except Exception as e:
            broken_pages.append((route, page_name, f"Error: {str(e)}"))
            print(f"❌ {page_name}: {route} - {str(e)}")

    # Generate report
    print("\n" + "=" * 70)
    print("📋 SUPER ADMIN PAGES TEST REPORT")
    print("=" * 70)
    
    total_pages = len(working_pages) + len(broken_pages)
    
    print(f"\n📊 SUMMARY:")
    print(f"Total Super Admin pages tested: {total_pages}")
    print(f"✅ Working pages: {len(working_pages)}")
    print(f"❌ Broken pages: {len(broken_pages)}")
    
    if total_pages > 0:
        success_rate = (len(working_pages) / total_pages) * 100
        print(f"🎯 Success rate: {success_rate:.1f}%")
    
    if broken_pages:
        print(f"\n🚨 BROKEN PAGES ({len(broken_pages)}):")
        for route, page_name, error in broken_pages:
            print(f"  ❌ {page_name}")
            print(f"     Route: {route}")
            print(f"     Issue: {error}")
            print()
    
    if len(broken_pages) == 0:
        print("\n🎉 ALL SUPER ADMIN PAGES ARE WORKING!")
        print("Super Admin users now have access to all functionality!")
    else:
        print(f"\n💡 NEXT STEPS:")
        print("1. Check if the broken routes are properly defined in SuperAdminRoutes.tsx")
        print("2. Verify that the corresponding page components exist")
        print("3. Check the navigation configuration matches the actual routes")

if __name__ == "__main__":
    test_superadmin_pages()
