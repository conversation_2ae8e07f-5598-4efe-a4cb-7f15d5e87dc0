#!/usr/bin/env python3
"""
Test for Missing Frontend Pages
Identifies which pages are referenced in routes but don't actually exist
"""

import os
import re

def find_missing_pages():
    """Find pages that are referenced in lazy routes but don't exist"""
    
    # Read the lazy routes file
    lazy_routes_path = "frontend/src/routes/lazyRoutes.tsx"
    with open(lazy_routes_path, 'r') as f:
        lazy_routes_content = f.read()
    
    # Extract all import paths from lazy routes
    import_pattern = r"import\(['\"]([^'\"]+)['\"]"
    imports = re.findall(import_pattern, lazy_routes_content)
    
    missing_pages = []
    existing_pages = []
    
    for import_path in imports:
        # Convert import path to file path
        if import_path.startswith('../'):
            file_path = import_path.replace('../', 'frontend/src/')
        else:
            file_path = f"frontend/src/{import_path}"
        
        # Add .tsx extension if not present
        if not file_path.endswith('.tsx'):
            file_path += '.tsx'
        
        # Check if file exists
        if os.path.exists(file_path):
            existing_pages.append({
                'import': import_path,
                'file': file_path,
                'status': 'EXISTS'
            })
        else:
            missing_pages.append({
                'import': import_path,
                'file': file_path,
                'status': 'MISSING'
            })
    
    return missing_pages, existing_pages

def analyze_route_files():
    """Analyze all route files to find referenced pages"""
    route_files = [
        "frontend/src/routes/PureSuperAdminRoutes.tsx",
        "frontend/src/routes/HRManagerRoutes.tsx",
        "frontend/src/routes/FinanceManagerRoutes.tsx",
        "frontend/src/routes/EmployeeRoutes.tsx",
        "frontend/src/routes/AdminRoutes.tsx",
        "frontend/src/routes/SalesManagerRoutes.tsx",
        "frontend/src/routes/DepartmentManagerRoutes.tsx"
    ]
    
    all_referenced_components = set()
    
    for route_file in route_files:
        if os.path.exists(route_file):
            with open(route_file, 'r') as f:
                content = f.read()
                
            # Find component imports
            import_pattern = r"import\s+(\w+)\s+from\s+['\"]([^'\"]+)['\"]"
            imports = re.findall(import_pattern, content)
            
            for component_name, import_path in imports:
                all_referenced_components.add((component_name, import_path, route_file))
    
    return all_referenced_components

def main():
    print("🔍 Analyzing Missing Frontend Pages...")
    print("=" * 60)
    
    # Find missing pages from lazy routes
    missing_pages, existing_pages = find_missing_pages()
    
    print(f"\n📊 LAZY ROUTES ANALYSIS:")
    print(f"Total imports found: {len(missing_pages) + len(existing_pages)}")
    print(f"✅ Existing pages: {len(existing_pages)}")
    print(f"❌ Missing pages: {len(missing_pages)}")
    
    if missing_pages:
        print(f"\n🚨 MISSING PAGES FROM LAZY ROUTES:")
        for page in missing_pages:
            print(f"  ❌ {page['import']} → {page['file']}")
    
    # Analyze route files
    referenced_components = analyze_route_files()
    
    print(f"\n📋 ROUTE FILES ANALYSIS:")
    print(f"Total referenced components: {len(referenced_components)}")
    
    missing_from_routes = []
    for component_name, import_path, route_file in referenced_components:
        # Convert to file path
        if import_path.startswith('../'):
            file_path = import_path.replace('../', 'frontend/src/')
        else:
            file_path = f"frontend/src/{import_path}"
        
        if not file_path.endswith('.tsx'):
            file_path += '.tsx'
        
        if not os.path.exists(file_path):
            missing_from_routes.append({
                'component': component_name,
                'import': import_path,
                'file': file_path,
                'route_file': route_file
            })
    
    if missing_from_routes:
        print(f"\n🚨 MISSING PAGES FROM ROUTE FILES:")
        for page in missing_from_routes:
            print(f"  ❌ {page['component']} ({page['route_file']})")
            print(f"     Import: {page['import']}")
            print(f"     Expected: {page['file']}")
            print()
    
    # Summary
    total_missing = len(missing_pages) + len(missing_from_routes)
    print(f"\n📈 SUMMARY:")
    print(f"Total missing pages: {total_missing}")
    
    if total_missing == 0:
        print("🎉 All pages exist!")
    else:
        print(f"⚠️ {total_missing} pages need to be created")
        
        # Create list of unique missing pages
        all_missing = set()
        for page in missing_pages:
            all_missing.add(page['file'])
        for page in missing_from_routes:
            all_missing.add(page['file'])
        
        print(f"\n📝 PAGES TO CREATE:")
        for i, page_path in enumerate(sorted(all_missing), 1):
            print(f"{i:2d}. {page_path}")

if __name__ == "__main__":
    main()
