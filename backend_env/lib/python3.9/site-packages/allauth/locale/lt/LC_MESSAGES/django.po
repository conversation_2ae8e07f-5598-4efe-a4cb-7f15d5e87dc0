# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-07 04:22-0500\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < "
"11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? "
"1 : n % 1 != 0 ? 2: 3);\n"

#: account/adapter.py:48
msgid "Username can not be used. Please use other username."
msgstr ""
"Šis naudotojo vardas negalimas. Prašome pasirinkti kitą naudotojo vardą."

#: account/adapter.py:54
msgid "Too many failed login attempts. Try again later."
msgstr ""

#: account/adapter.py:56
msgid "A user is already registered with this email address."
msgstr "Šiuo el. pašto adresu jau yra užsiregistravęs kitas naudotojas."

#: account/adapter.py:57
#, fuzzy
#| msgid "Current Password"
msgid "Incorrect password."
msgstr "Esamas slaptažodis"

#: account/adapter.py:308
#, python-brace-format
msgid "Password must be a minimum of {0} characters."
msgstr "Slaptažodis turi būti sudarytas mažiausiai iš {0} simbolių."

#: account/apps.py:9
msgid "Accounts"
msgstr "Paskyros"

#: account/forms.py:58 account/forms.py:432
msgid "You must type the same password each time."
msgstr "Turite įvesti tą patį slaptažodį kiekvieną kartą."

#: account/forms.py:90 account/forms.py:397 account/forms.py:522
#: account/forms.py:658
msgid "Password"
msgstr "Slaptažodis"

#: account/forms.py:91
msgid "Remember Me"
msgstr "Prisimink mane"

#: account/forms.py:95
msgid "This account is currently inactive."
msgstr "Šiuo metu ši paskyra yra neaktyvi."

#: account/forms.py:97
msgid "The email address and/or password you specified are not correct."
msgstr "Pateiktas el. pašto adresas ir/arba slaptažodis yra neteisingi."

#: account/forms.py:100
msgid "The username and/or password you specified are not correct."
msgstr "Pateiktas naudotojo vardas ir/arba slaptažodis yra neteisingi."

#: account/forms.py:111 account/forms.py:271 account/forms.py:459
#: account/forms.py:540
msgid "Email address"
msgstr "El. pašto adresas"

#: account/forms.py:115 account/forms.py:316 account/forms.py:456
#: account/forms.py:535
msgid "Email"
msgstr "El. paštas"

#: account/forms.py:118 account/forms.py:121 account/forms.py:261
#: account/forms.py:264
msgid "Username"
msgstr "Naudotojo vardas"

#: account/forms.py:131
msgid "Username or email"
msgstr "Naudotojo vardas arba el. paštas"

#: account/forms.py:134
msgctxt "field label"
msgid "Login"
msgstr "Prisijungimo vardas"

#: account/forms.py:307
#, fuzzy
#| msgid "Email (optional)"
msgid "Email (again)"
msgstr "El. paštas (neprivalomas)"

#: account/forms.py:311
#, fuzzy
#| msgid "email confirmation"
msgid "Email address confirmation"
msgstr "el. pašto patvirtinimas"

#: account/forms.py:319
msgid "Email (optional)"
msgstr "El. paštas (neprivalomas)"

#: account/forms.py:368
#, fuzzy
#| msgid "You must type the same password each time."
msgid "You must type the same email each time."
msgstr "Turite įvesti tą patį slaptažodį kiekvieną kartą."

#: account/forms.py:401 account/forms.py:523
msgid "Password (again)"
msgstr "Slaptažodis (pakartoti)"

#: account/forms.py:470
msgid "This email address is already associated with this account."
msgstr "Šis el. pašto adresas jau susietas su šia paskyra."

#: account/forms.py:472
#, fuzzy, python-format
#| msgid "Your account has no verified email address."
msgid "You cannot add more than %d email addresses."
msgstr "Jūsų paskyra neturi patvirtinto el. pašto adreso."

#: account/forms.py:503
msgid "Current Password"
msgstr "Esamas slaptažodis"

#: account/forms.py:505 account/forms.py:607
msgid "New Password"
msgstr "Naujas slaptažodis"

#: account/forms.py:506 account/forms.py:608
msgid "New Password (again)"
msgstr "Naujas slaptažodis (pakartoti)"

#: account/forms.py:514
msgid "Please type your current password."
msgstr "Prašome įvesti esamą jūsų slaptažodį."

#: account/forms.py:552
msgid "The email address is not assigned to any user account"
msgstr "El. pašto adresas nėra susietas su jokia naudotojo paskyra"

#: account/forms.py:628
msgid "The password reset token was invalid."
msgstr "Neteisingas slaptažodžio atstatymo atpažinimo ženklas."

#: account/models.py:21
msgid "user"
msgstr "naudotojas"

#: account/models.py:26 account/models.py:34 account/models.py:138
msgid "email address"
msgstr "el. pašto adresas"

#: account/models.py:28
msgid "verified"
msgstr "patvirtintas"

#: account/models.py:29
msgid "primary"
msgstr "pirminis"

#: account/models.py:35
msgid "email addresses"
msgstr "el. pašto adresai"

#: account/models.py:141
msgid "created"
msgstr "sukurtas"

#: account/models.py:142
msgid "sent"
msgstr "išsiųstas"

#: account/models.py:143 socialaccount/models.py:62
msgid "key"
msgstr "raktas"

#: account/models.py:148
msgid "email confirmation"
msgstr "el. pašto patvirtinimas"

#: account/models.py:149
msgid "email confirmations"
msgstr "el. pašto patvirtinimai"

#: mfa/adapter.py:19
msgid ""
"You cannot activate two-factor authentication until you have verified your "
"email address."
msgstr ""

#: mfa/adapter.py:22
msgid ""
"You cannot add an email address to an account protected by two-factor "
"authentication."
msgstr ""

#: mfa/adapter.py:24
msgid "Incorrect code."
msgstr ""

#: mfa/apps.py:7
msgid "MFA"
msgstr ""

#: mfa/forms.py:12
msgid "Code"
msgstr ""

#: mfa/forms.py:29
msgid "Authenticator code"
msgstr ""

#: mfa/models.py:15
msgid "Recovery codes"
msgstr ""

#: mfa/models.py:16
msgid "TOTP Authenticator"
msgstr ""

#: socialaccount/adapter.py:30
#, fuzzy, python-format
#| msgid ""
#| "An account already exists with this e-mail address. Please sign in to "
#| "that account first, then connect your %s account."
msgid ""
"An account already exists with this email address. Please sign in to that "
"account first, then connect your %s account."
msgstr ""
"Paskyra su šiuo el. pašto adresu jau egzistuoja. Prašome pirmiausia "
"prisijungti prie tos paskyros ir tada prijunkite %s paskyrą."

#: socialaccount/adapter.py:136
msgid "Your account has no password set up."
msgstr "Jūsų paskyra neturi nustatyto slaptažodžio."

#: socialaccount/adapter.py:143
msgid "Your account has no verified email address."
msgstr "Jūsų paskyra neturi patvirtinto el. pašto adreso."

#: socialaccount/apps.py:7
msgid "Social Accounts"
msgstr "Socialinės paskyros"

#: socialaccount/models.py:36 socialaccount/models.py:97
msgid "provider"
msgstr "tiekėjas"

#: socialaccount/models.py:45
#, fuzzy
#| msgid "provider"
msgid "provider ID"
msgstr "tiekėjas"

#: socialaccount/models.py:49
msgid "name"
msgstr "pavadinimas"

#: socialaccount/models.py:51
msgid "client id"
msgstr "kliento id"

#: socialaccount/models.py:53
msgid "App ID, or consumer key"
msgstr "App ID arba consumer key"

#: socialaccount/models.py:56
msgid "secret key"
msgstr "secret key"

#: socialaccount/models.py:59
msgid "API secret, client secret, or consumer secret"
msgstr "API secret, client secret, arba consumer secret"

#: socialaccount/models.py:62
msgid "Key"
msgstr "Raktas"

#: socialaccount/models.py:81
msgid "social application"
msgstr "socialinė programėlė"

#: socialaccount/models.py:82
msgid "social applications"
msgstr "socialinės programėlės"

#: socialaccount/models.py:117
msgid "uid"
msgstr "uid"

#: socialaccount/models.py:119
msgid "last login"
msgstr "paskutinis prisijungimas"

#: socialaccount/models.py:120
msgid "date joined"
msgstr "registracijos data"

#: socialaccount/models.py:121
msgid "extra data"
msgstr "papildomi duomenys"

#: socialaccount/models.py:125
msgid "social account"
msgstr "socialinė paskyra"

#: socialaccount/models.py:126
msgid "social accounts"
msgstr "socialinės paskyros"

#: socialaccount/models.py:160
msgid "token"
msgstr "atpažinimo ženklas"

#: socialaccount/models.py:161
msgid "\"oauth_token\" (OAuth1) or access token (OAuth2)"
msgstr "\"oauth_token\" (OAuth1) arba prieigos atpažinimo ženklas (OAuth2)"

#: socialaccount/models.py:165
msgid "token secret"
msgstr "token secret"

#: socialaccount/models.py:166
msgid "\"oauth_token_secret\" (OAuth1) or refresh token (OAuth2)"
msgstr ""
"\"oauth_token_secret\" (OAuth1) arba atnaujintas atpažinimo ženklas (OAuth2)"

#: socialaccount/models.py:169
msgid "expires at"
msgstr "galiojimas"

#: socialaccount/models.py:174
msgid "social application token"
msgstr "socialinės programėlės atpažinimo ženklas"

#: socialaccount/models.py:175
msgid "social application tokens"
msgstr "socialinės programėlės atpažinimo ženklai"

#: socialaccount/providers/douban/views.py:36
msgid "Invalid profile data"
msgstr ""

#: socialaccount/providers/oauth/client.py:85
#, fuzzy, python-format
#| msgid "Invalid response while obtaining request token from \"%s\"."
msgid ""
"Invalid response while obtaining request token from \"%s\". Response was: %s."
msgstr "Klaidingas atsakymas gaunant užklausos atpažinimo ženklą iš \"%s\"."

#: socialaccount/providers/oauth/client.py:119
#: socialaccount/providers/pocket/client.py:78
#, python-format
msgid "Invalid response while obtaining access token from \"%s\"."
msgstr "Klaidingas atsakymas gaunant prieigos atpažinimo ženklą iš \"%s\"."

#: socialaccount/providers/oauth/client.py:140
#, python-format
msgid "No request token saved for \"%s\"."
msgstr "Nėra užklausos atpažinimo žymės šiam \"%s\"."

#: socialaccount/providers/oauth/client.py:191
#, python-format
msgid "No access token saved for \"%s\"."
msgstr "Nėra prieigos atpažinimo žymės šiam \"%s\"."

#: socialaccount/providers/oauth/client.py:212
#, python-format
msgid "No access to private resources at \"%s\"."
msgstr "Nėra prieigos prie privataus resurso iš \"%s\"."

#: socialaccount/providers/pocket/client.py:37
#, python-format
msgid "Invalid response while obtaining request token from \"%s\"."
msgstr "Klaidingas atsakymas gaunant užklausos atpažinimo ženklą iš \"%s\"."

#: templates/account/account_inactive.html:5
#: templates/account/account_inactive.html:8
msgid "Account Inactive"
msgstr "Paskyra neaktyvi"

#: templates/account/account_inactive.html:10
msgid "This account is inactive."
msgstr "Ši paskyra neaktyvi."

#: templates/account/base.html:16
msgid "Messages:"
msgstr ""

#: templates/account/base.html:26
msgid "Menu:"
msgstr ""

#: templates/account/base.html:29 templates/account/email_change.html:31
#, fuzzy
#| msgid "Email"
msgid "Change Email"
msgstr "El. paštas"

#: templates/account/base.html:30 templates/account/logout.html:5
#: templates/account/logout.html:8 templates/account/logout.html:17
msgid "Sign Out"
msgstr "Atsijungti"

#: templates/account/base.html:32 templates/account/login.html:6
#: templates/account/login.html:10 templates/account/login.html:43
#: templates/mfa/authenticate.html:4 templates/mfa/authenticate.html:16
#: templates/socialaccount/login.html:4
msgid "Sign In"
msgstr "Prisijungti"

#: templates/account/base.html:33 templates/account/signup.html:8
#: templates/account/signup.html:18 templates/socialaccount/signup.html:8
#: templates/socialaccount/signup.html:19
msgid "Sign Up"
msgstr "Registruotis"

#: templates/account/email.html:5 templates/account/email.html:8
msgid "Email Addresses"
msgstr "El. pašto adresai"

#: templates/account/email.html:10
msgid "The following email addresses are associated with your account:"
msgstr "Šie el. pašto adresas yra susieti su jūsų paskyra:"

#: templates/account/email.html:24
msgid "Verified"
msgstr "Patvirtintas"

#: templates/account/email.html:26
msgid "Unverified"
msgstr "Nepatvirtintas"

#: templates/account/email.html:28
msgid "Primary"
msgstr "Pirminis"

#: templates/account/email.html:34
msgid "Make Primary"
msgstr "Padaryti pirminiu"

#: templates/account/email.html:35 templates/account/email_change.html:21
msgid "Re-send Verification"
msgstr "Pakartotinai siųsti patvirtinimą"

#: templates/account/email.html:36 templates/socialaccount/connections.html:35
msgid "Remove"
msgstr "Šalinti"

#: templates/account/email.html:47
msgid "Add Email Address"
msgstr "Pridėti el. pašto adresą"

#: templates/account/email.html:52
msgid "Add Email"
msgstr "Pridėti el. paštą"

#: templates/account/email.html:62
msgid "Do you really want to remove the selected email address?"
msgstr "Ar tikrai norite ištrinti pasirinktą el. pašto adresą?"

#: templates/account/email/account_already_exists_message.txt:4
#, python-format
msgid ""
"You are receiving this email because you or someone else tried to signup for "
"an\n"
"account using email address:\n"
"\n"
"%(email)s\n"
"\n"
"However, an account using that email address already exists.  In case you "
"have\n"
"forgotten about this, please use the password forgotten procedure to "
"recover\n"
"your account:\n"
"\n"
"%(password_reset_url)s"
msgstr ""

#: templates/account/email/account_already_exists_subject.txt:3
msgid "Account Already Exists"
msgstr ""

#: templates/account/email/base_message.txt:1
#, fuzzy, python-format
#| msgid ""
#| "Thank you from %(site_name)s!\n"
#| "%(site_domain)s"
msgid "Hello from %(site_name)s!"
msgstr ""
"Ačiū nuo %(site_name)s\n"
"%(site_domain)s"

#: templates/account/email/base_message.txt:5
#, python-format
msgid ""
"Thank you for using %(site_name)s!\n"
"%(site_domain)s"
msgstr ""
"Ačiū, kad naudojate %(site_name)s!\n"
"%(site_domain)s"

#: templates/account/email/email_confirmation_message.txt:5
#, fuzzy, python-format
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because user %(user_display)s at "
#| "%(site_domain)s has given yours as an e-mail address to connect their "
#| "account.\n"
#| "\n"
#| "To confirm this is correct, go to %(activate_url)s\n"
msgid ""
"You're receiving this email because user %(user_display)s has given your "
"email address to register an account on %(site_domain)s.\n"
"\n"
"To confirm this is correct, go to %(activate_url)s"
msgstr ""
"Su jumis sveikinasi %(site_name)s\n"
"\n"
"Jūs gavote šį laišką, kadangi naudotojas %(user_display)s iš %(site_domain)s "
"prijungė šį el. pašto adresą prie savo paskyros.\n"
"\n"
"Tam, kad patvirtintumėte prijungimą sekite šia nuoroda: %(activate_url)s\n"

#: templates/account/email/email_confirmation_subject.txt:3
msgid "Please Confirm Your Email Address"
msgstr "Prašome patvirtinti savo el. pašto adresą"

#: templates/account/email/password_reset_key_message.txt:4
#, fuzzy
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account at %(site_domain)s.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You're receiving this email because you or someone else has requested a "
"password reset for your user account.\n"
"It can be safely ignored if you did not request a password reset. Click the "
"link below to reset your password."
msgstr ""
"Su jumis sveikinasi %(site_name)s\n"
"\n"
"Jūs gavote šį laišką, kadangi jūs arba kažkas kitas pateikė slaptažodžio "
"keitimo užklausą paskyrai susietai su šiuo el. pašto adresu iš "
"%(site_domain)s.\n"
"Jei jūs neteikėte slaptažodžio keitimo užklausos, galite ignoruoti šį "
"laišką. Kad pakeistumėte savo slaptažodį sekite žemiau esančia nuoroda."

#: templates/account/email/password_reset_key_message.txt:9
#, python-format
msgid "In case you forgot, your username is %(username)s."
msgstr "Primename, kad jūsų naudotojo vardas yra %(username)s."

#: templates/account/email/password_reset_key_subject.txt:3
#: templates/account/email/unknown_account_subject.txt:3
msgid "Password Reset Email"
msgstr "Slaptažodžio keitimo el. paštas"

#: templates/account/email/unknown_account_message.txt:4
#, fuzzy, python-format
#| msgid ""
#| "Hello from %(site_name)s!\n"
#| "\n"
#| "You're receiving this e-mail because you or someone else has requested a "
#| "password for your user account at %(site_domain)s.\n"
#| "It can be safely ignored if you did not request a password reset. Click "
#| "the link below to reset your password."
msgid ""
"You are receiving this email because you or someone else has requested a\n"
"password for your user account. However, we do not have any record of a "
"user\n"
"with email %(email)s in our database.\n"
"\n"
"This mail can be safely ignored if you did not request a password reset.\n"
"\n"
"If it was you, you can sign up for an account using the link below."
msgstr ""
"Su jumis sveikinasi %(site_name)s\n"
"\n"
"Jūs gavote šį laišką, kadangi jūs arba kažkas kitas pateikė slaptažodžio "
"keitimo užklausą paskyrai susietai su šiuo el. pašto adresu iš "
"%(site_domain)s.\n"
"Jei jūs neteikėte slaptažodžio keitimo užklausos, galite ignoruoti šį "
"laišką. Kad pakeistumėte savo slaptažodį sekite žemiau esančia nuoroda."

#: templates/account/email_change.html:4 templates/account/email_change.html:7
#, fuzzy
#| msgid "Email Addresses"
msgid "Email Address"
msgstr "El. pašto adresai"

#: templates/account/email_change.html:11
#, fuzzy
#| msgid "The following email addresses are associated with your account:"
msgid "The following email address is associated with your account:"
msgstr "Šie el. pašto adresas yra susieti su jūsų paskyra:"

#: templates/account/email_change.html:16
#, fuzzy
#| msgid "Your primary email address must be verified."
msgid "Your email address is still pending verification:"
msgstr "Pirminis el. pašto adresas turi būti patvirtintas."

#: templates/account/email_change.html:27
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Change Email Address"
msgstr "Patvirtinkite el. pašto adresą"

#: templates/account/email_confirm.html:6
#: templates/account/email_confirm.html:10
msgid "Confirm Email Address"
msgstr "Patvirtinkite el. pašto adresą"

#: templates/account/email_confirm.html:17
#, fuzzy, python-format
#| msgid ""
#| "Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an e-"
#| "mail address for user %(user_display)s."
msgid ""
"Please confirm that <a href=\"mailto:%(email)s\">%(email)s</a> is an email "
"address for user %(user_display)s."
msgstr ""
"Prašome patvirtinti, kad <a href=\"mailto:%(email)s\">%(email)s</a> yra "
"%(user_display)s el. pašto adresas."

#: templates/account/email_confirm.html:21
#: templates/account/reauthenticate.html:19
msgid "Confirm"
msgstr "Patvirtinti"

#: templates/account/email_confirm.html:24
#: templates/account/messages/email_confirmation_failed.txt:2
#, fuzzy, python-format
#| msgid "The social account is already connected to a different account."
msgid ""
"Unable to confirm %(email)s because it is already confirmed by a different "
"account."
msgstr "Socialinė paskyra jau yra prijungta prie kitos paskyros."

#: templates/account/email_confirm.html:31
#, fuzzy, python-format
#| msgid ""
#| "This e-mail confirmation link expired or is invalid. Please <a href="
#| "\"%(email_url)s\">issue a new e-mail confirmation request</a>."
msgid ""
"This email confirmation link expired or is invalid. Please <a href="
"\"%(email_url)s\">issue a new email confirmation request</a>."
msgstr ""
"Šios el. pašto patvirtinimo nuorodos galiojimas baigėsi arba nuoroda yra "
"klaidinga. Prašome <a href=\"%(email_url)s\">pateikti naują el. pašto "
"patvirtinimo užklausimą</a>."

#: templates/account/login.html:15
#, python-format
msgid ""
"Please sign in with one\n"
"of your existing third party accounts. Or, <a href=\"%(signup_url)s\">sign "
"up</a>\n"
"for a %(site_name)s account and sign in below:"
msgstr ""
"Prašome prisijungti viena\n"
"iš jūsų turimų trečios šalies paskyrų. Arba, <a href=\"%(signup_url)s"
"\">susikurkite</a>\n"
"naują %(site_name)s paskyrą ir prisijunkite žemiau:"

#: templates/account/login.html:25
msgid "or"
msgstr "arba"

#: templates/account/login.html:32
#, python-format
msgid ""
"If you have not created an account yet, then please\n"
"<a href=\"%(signup_url)s\">sign up</a> first."
msgstr ""
"Jei dar nesusikūrėte paskyros, tuomet prašome pirmiausia\n"
"<a href=\"%(signup_url)s\">susikurti</a>."

#: templates/account/login.html:42 templates/account/password_change.html:14
msgid "Forgot Password?"
msgstr "Pamiršote slaptažodį?"

#: templates/account/logout.html:10
msgid "Are you sure you want to sign out?"
msgstr "Ar tikrai norite atsijungti?"

#: templates/account/messages/cannot_delete_primary_email.txt:2
#, python-format
msgid "You cannot remove your primary email address (%(email)s)."
msgstr "Negalite ištrinti pirminio (%(email)s) el. pašto adreso."

#: templates/account/messages/email_confirmation_sent.txt:2
#, python-format
msgid "Confirmation email sent to %(email)s."
msgstr "Patvirtinimo laiškas išsiųstas adresu %(email)s."

#: templates/account/messages/email_confirmed.txt:2
#, python-format
msgid "You have confirmed %(email)s."
msgstr "%(email)s patvirtintas."

#: templates/account/messages/email_deleted.txt:2
#, python-format
msgid "Removed email address %(email)s."
msgstr "%(email)s el. pašto adresas ištrintas."

#: templates/account/messages/logged_in.txt:4
#, python-format
msgid "Successfully signed in as %(name)s."
msgstr "Sėkmingai prisijungėte kaip %(name)s."

#: templates/account/messages/logged_out.txt:2
msgid "You have signed out."
msgstr "Atsijungėte."

#: templates/account/messages/password_changed.txt:2
msgid "Password successfully changed."
msgstr "Slaptažodis sėkmingai pakeistas."

#: templates/account/messages/password_set.txt:2
msgid "Password successfully set."
msgstr "Slaptažodis pakeistas sėkmingai."

#: templates/account/messages/primary_email_set.txt:2
msgid "Primary email address set."
msgstr "Pirminis el. pašto adresas pakeistas sėkmingai."

#: templates/account/messages/unverified_primary_email.txt:2
msgid "Your primary email address must be verified."
msgstr "Pirminis el. pašto adresas turi būti patvirtintas."

#: templates/account/password_change.html:5
#: templates/account/password_change.html:8
#: templates/account/password_change.html:13
#: templates/account/password_reset_from_key.html:4
#: templates/account/password_reset_from_key.html:7
#: templates/account/password_reset_from_key_done.html:4
#: templates/account/password_reset_from_key_done.html:7
msgid "Change Password"
msgstr "Keisti slaptažodį"

#: templates/account/password_reset.html:6
#: templates/account/password_reset.html:10
#: templates/account/password_reset_done.html:6
#: templates/account/password_reset_done.html:9
msgid "Password Reset"
msgstr "Slaptažodžio atstatymas"

#: templates/account/password_reset.html:15
#, fuzzy
#| msgid ""
#| "Forgotten your password? Enter your e-mail address below, and we'll send "
#| "you an e-mail allowing you to reset it."
msgid ""
"Forgotten your password? Enter your email address below, and we'll send you "
"an email allowing you to reset it."
msgstr ""
"Pamiršote slaptažodį? Įveskite savo el. pašto adresą žemiau ir mes išsiūsime "
"jums laišką, kurio pagalba galėsite pasikeisti slaptažodį."

#: templates/account/password_reset.html:20
msgid "Reset My Password"
msgstr "Atstatyti mano slaptažodį"

#: templates/account/password_reset.html:23
msgid "Please contact us if you have any trouble resetting your password."
msgstr "Prašome susisiekti su mumis jei negalite atstatyti slaptažodžio."

#: templates/account/password_reset_done.html:15
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent you an email. If you have not received it please check your "
"spam folder. Otherwise contact us if you do not receive it in a few minutes."
msgstr ""
"Išsiuntėme jums patvirtinimo laišką.\n"
"Prašome sekite nuoroda pateiktą laiške. Susisiekite su mumis\n"
"jei negausite laiško per kelias minutes."

#: templates/account/password_reset_from_key.html:7
msgid "Bad Token"
msgstr "Klaidinga atpažinimo žymė"

#: templates/account/password_reset_from_key.html:11
#, python-format
msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a <a href=\"%(passwd_reset_url)s\">new password reset</"
"a>."
msgstr ""
"Slaptažodžio atstatymo nuoroda klaidinga, taip gali būti dėl to, kad nuoroda "
"jau buvo kartą panaudota.  Prašome <a href=\"%(passwd_reset_url)s"
"\">pakartoti slaptažodžio atstatymą</a>."

#: templates/account/password_reset_from_key.html:16
msgid "change password"
msgstr "keisti slaptažodį"

#: templates/account/password_reset_from_key_done.html:8
msgid "Your password is now changed."
msgstr "Jūsų slaptažodis pakeistas."

#: templates/account/password_set.html:5 templates/account/password_set.html:8
#: templates/account/password_set.html:13
msgid "Set Password"
msgstr "Nustatyti slaptažodį"

#: templates/account/reauthenticate.html:5
#: templates/account/reauthenticate.html:9
#, fuzzy
#| msgid "Confirm Email Address"
msgid "Confirm Access"
msgstr "Patvirtinkite el. pašto adresą"

#: templates/account/reauthenticate.html:11
msgid "To safeguard the security of your account, please enter your password:"
msgstr ""

#: templates/account/signup.html:5 templates/socialaccount/signup.html:5
msgid "Signup"
msgstr "Registracija"

#: templates/account/signup.html:10
#, python-format
msgid ""
"Already have an account? Then please <a href=\"%(login_url)s\">sign in</a>."
msgstr "Jau turite paskyrą? Prašome <a href=\"%(login_url)s\">prisijungti</a>."

#: templates/account/signup_closed.html:5
#: templates/account/signup_closed.html:8
msgid "Sign Up Closed"
msgstr "Registracija uždaryta"

#: templates/account/signup_closed.html:10
msgid "We are sorry, but the sign up is currently closed."
msgstr "Atsiprašome, tačiau registracija šiuo metu yra uždaryta."

#: templates/account/snippets/already_logged_in.html:5
msgid "Note"
msgstr "Pastaba"

#: templates/account/snippets/already_logged_in.html:5
#, python-format
msgid "you are already logged in as %(user_display)s."
msgstr "jūs jau esate prisijungęs, kaip %(user_display)s."

#: templates/account/snippets/warn_no_email.html:3
msgid "Warning:"
msgstr "Įspėjimas:"

#: templates/account/snippets/warn_no_email.html:3
#, fuzzy
#| msgid ""
#| "You currently do not have any e-mail address set up. You should really "
#| "add an e-mail address so you can receive notifications, reset your "
#| "password, etc."
msgid ""
"You currently do not have any email address set up. You should really add an "
"email address so you can receive notifications, reset your password, etc."
msgstr ""
"Šiuo metu jūs neturite nustatyto el. pašto adreso. Rekomenduojame pridėti "
"el. pašto adresą, kad gautumėte pranešimus, galėtumėte atstatyti slaptažodį "
"ir pan."

#: templates/account/verification_sent.html:5
#: templates/account/verification_sent.html:8
#: templates/account/verified_email_required.html:5
#: templates/account/verified_email_required.html:8
msgid "Verify Your Email Address"
msgstr "Patvirtinkite savo el. pašto adresą"

#: templates/account/verification_sent.html:10
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for verification. Follow the link provided "
#| "to finalize the signup process. Please contact us if you do not receive "
#| "it within a few minutes."
msgid ""
"We have sent an email to you for verification. Follow the link provided to "
"finalize the signup process. If you do not see the verification email in "
"your main inbox, check your spam folder. Please contact us if you do not "
"receive the verification email within a few minutes."
msgstr ""
"Išsiuntėme jums laišką patvirtinimui. Sekite laiške pateikta nuoroda, kad "
"užbaigtumėte registraciją. Prašome susisiekti su mumis, jei laiško negavote "
"per kelias minutes."

#: templates/account/verified_email_required.html:12
#, fuzzy
#| msgid ""
#| "This part of the site requires us to verify that\n"
#| "you are who you claim to be. For this purpose, we require that you\n"
#| "verify ownership of your e-mail address. "
msgid ""
"This part of the site requires us to verify that\n"
"you are who you claim to be. For this purpose, we require that you\n"
"verify ownership of your email address. "
msgstr ""
"Šioje svetainės vietoje privalome gauti iš jūsų patvirtinimą,\n"
"kad jūs tikrai esate tas asmuo, kaip teigiate. Dėl šios priežasties\n"
"prašome patvirtinti el. pašto adreso nuosavybę. "

#: templates/account/verified_email_required.html:16
#, fuzzy
#| msgid ""
#| "We have sent an e-mail to you for\n"
#| "verification. Please click on the link inside this e-mail. Please\n"
#| "contact us if you do not receive it within a few minutes."
msgid ""
"We have sent an email to you for\n"
"verification. Please click on the link inside that email. If you do not see "
"the verification email in your main inbox, check your spam folder. "
"Otherwise\n"
"contact us if you do not receive it within a few minutes."
msgstr ""
"Išsiuntėme jums patvirtinimo laišką.\n"
"Prašome sekite nuoroda pateiktą laiške. Susisiekite su mumis\n"
"jei negausite laiško per kelias minutes."

#: templates/account/verified_email_required.html:20
#, fuzzy, python-format
#| msgid ""
#| "<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change "
#| "your e-mail address</a>."
msgid ""
"<strong>Note:</strong> you can still <a href=\"%(email_url)s\">change your "
"email address</a>."
msgstr ""
"<strong>Pastaba:</strong> vis dar galite <a href=\"%(email_url)s\">pakeisti "
"savo el. pašto adresą</a>."

#: templates/mfa/authenticate.html:7 templates/mfa/index.html:4
#: templates/mfa/index.html:7
msgid "Two-Factor Authentication"
msgstr ""

#: templates/mfa/authenticate.html:9
msgid ""
"Your account is protected by two-factor authentication. Please enter an "
"authenticator code:"
msgstr ""

#: templates/mfa/index.html:9 templates/mfa/totp/base.html:4
msgid "Authenticator App"
msgstr ""

#: templates/mfa/index.html:11
msgid "Authentication using an authenticator app is active."
msgstr ""

#: templates/mfa/index.html:14 templates/mfa/totp/deactivate_form.html:11
msgid "Deactivate"
msgstr ""

#: templates/mfa/index.html:18
msgid "An authenticator app is not active."
msgstr ""

#: templates/mfa/index.html:21 templates/mfa/totp/activate_form.html:16
msgid "Activate"
msgstr ""

#: templates/mfa/index.html:27 templates/mfa/recovery_codes/base.html:4
#: templates/mfa/recovery_codes/generate.html:4
#: templates/mfa/recovery_codes/index.html:4
msgid "Recovery Codes"
msgstr ""

#: templates/mfa/index.html:30 templates/mfa/recovery_codes/index.html:6
#, python-format
msgid ""
"There is %(unused_count)s out of %(total_count)s recovery codes available."
msgid_plural ""
"There are %(unused_count)s out of %(total_count)s recovery codes available."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""

#: templates/mfa/index.html:34
msgid "View codes"
msgstr ""

#: templates/mfa/index.html:37 templates/mfa/recovery_codes/index.html:16
msgid "Download codes"
msgstr ""

#: templates/mfa/index.html:40 templates/mfa/recovery_codes/index.html:20
msgid "Generate new codes"
msgstr ""

#: templates/mfa/index.html:44
msgid "No recovery codes set up."
msgstr ""

#: templates/mfa/messages/recovery_codes_generated.txt:2
msgid "A new set of recovery codes has been generated."
msgstr ""

#: templates/mfa/messages/totp_activated.txt:2
msgid "Authenticator app activated."
msgstr ""

#: templates/mfa/messages/totp_deactivated.txt:2
msgid "Authenticator app deactivated."
msgstr ""

#: templates/mfa/recovery_codes/generate.html:6
msgid ""
"You are about to generate a new set of recovery codes for your account. This "
"action will invalidate your existing codes. Are you sure?"
msgstr ""

#: templates/mfa/recovery_codes/generate.html:11
msgid "Generate"
msgstr ""

#: templates/mfa/totp/activate_form.html:4
msgid "Activate Authenticator App"
msgstr ""

#: templates/mfa/totp/activate_form.html:9
#, fuzzy
#| msgid "token secret"
msgid "Authenticator secret"
msgstr "token secret"

#: templates/mfa/totp/deactivate_form.html:4
msgid "Deactivate Authenticator App"
msgstr ""

#: templates/mfa/totp/deactivate_form.html:6
msgid ""
"You are about to deactivate authenticator app based authentication. Are you "
"sure?"
msgstr ""

#: templates/openid/login.html:9
msgid "OpenID Sign In"
msgstr "OpenID prisijungimas"

#: templates/socialaccount/authentication_error.html:5
#: templates/socialaccount/authentication_error.html:8
msgid "Social Network Login Failure"
msgstr "Socialinio tinklo prisijungimo klaida"

#: templates/socialaccount/authentication_error.html:10
msgid ""
"An error occurred while attempting to login via your social network account."
msgstr ""
"Įvyko nenumatyta klaida bandant prisijungti per jūsų socialinio tinklo "
"paskyrą."

#: templates/socialaccount/connections.html:5
#: templates/socialaccount/connections.html:8
msgid "Account Connections"
msgstr "Paskyros ryšiai"

#: templates/socialaccount/connections.html:11
msgid ""
"You can sign in to your account using any of the following third party "
"accounts:"
msgstr ""
"Galite prisijungti prie savo paskyros naudodami vieną iš galimų trečios "
"šalies paskyrų:"

#: templates/socialaccount/connections.html:43
msgid ""
"You currently have no social network accounts connected to this account."
msgstr "Šiuo metu jūs neturite nei vienos prijungtos socialinės paskyros."

#: templates/socialaccount/connections.html:46
msgid "Add a 3rd Party Account"
msgstr "Pridėti trečios šalies paskyrą"

#: templates/socialaccount/login.html:8
#, python-format
msgid "Connect %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:10
#, python-format
msgid "You are about to connect a new third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:12
#, python-format
msgid "Sign In Via %(provider)s"
msgstr ""

#: templates/socialaccount/login.html:14
#, python-format
msgid "You are about to sign in using a third party account from %(provider)s."
msgstr ""

#: templates/socialaccount/login.html:19
msgid "Continue"
msgstr ""

#: templates/socialaccount/login_cancelled.html:5
#: templates/socialaccount/login_cancelled.html:9
msgid "Login Cancelled"
msgstr "Prisijungimas atšauktas"

#: templates/socialaccount/login_cancelled.html:13
#, python-format
msgid ""
"You decided to cancel logging in to our site using one of your existing "
"accounts. If this was a mistake, please proceed to <a href=\"%(login_url)s"
"\">sign in</a>."
msgstr ""
"Nusprendėte atšaukti prisijungimą naudojant vieną iš esamų paskyrų.  Jei tai "
"buvo klaida, prašome pakartoti <a href=\"%(login_url)s\">prisijungimą</a>."

#: templates/socialaccount/messages/account_connected.txt:2
msgid "The social account has been connected."
msgstr "Socialinė paskyra buvo prijungta."

#: templates/socialaccount/messages/account_connected_other.txt:2
msgid "The social account is already connected to a different account."
msgstr "Socialinė paskyra jau yra prijungta prie kitos paskyros."

#: templates/socialaccount/messages/account_disconnected.txt:2
msgid "The social account has been disconnected."
msgstr "Socialinė paskyra buvo atjungta."

#: templates/socialaccount/signup.html:10
#, python-format
msgid ""
"You are about to use your %(provider_name)s account to login to\n"
"%(site_name)s. As a final step, please complete the following form:"
msgstr ""
"Jūs beveik prisijungėte prie %(site_name)s naudodami %(provider_name)s\n"
"paskyrą. Liko paskutinis žingsnis, užpildyti sekančią formą:"

#~ msgid "This email address is already associated with another account."
#~ msgstr "Šis el. pašto adresas jau susietas su kita paskyra."

#~ msgid ""
#~ "We have sent you an e-mail. Please contact us if you do not receive it "
#~ "within a few minutes."
#~ msgstr ""
#~ "Išsiuntėme jums laišką. Prašome susisiekti su mums jei per kelias minutes "
#~ "negausite laiško."

#~ msgid "Account"
#~ msgstr "Paskyra"

#~ msgid "The login and/or password you specified are not correct."
#~ msgstr "Pateiktas prisijungimo vardas ir/arba slaptažodis yra neteisingi."

#~ msgid "Usernames can only contain letters, digits and @/./+/-/_."
#~ msgstr "Naudotojo vardui galima naudoti tik raides, skaičius ir @/./+/-/_."

#~ msgid "This username is already taken. Please choose another."
#~ msgstr "Šis naudotojo vardas jau užimtas. Prašome pasirinkti kitą."

#, fuzzy
#~| msgid "Sign In"
#~ msgid "Shopify Sign In"
#~ msgstr "Prisijungti"

#~ msgid ""
#~ "You have confirmed that <a href=\"mailto:%(email)s\">%(email)s</a> is an "
#~ "e-mail address for user %(user_display)s."
#~ msgstr ""
#~ "Jūs patvirtinote, kad <a href=\"mailto:%(email)s\">%(email)s</a> yra "
#~ "%(user_display)s naudotojo el. pašto adresas."
