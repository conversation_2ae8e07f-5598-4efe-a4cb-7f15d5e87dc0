from allauth.socialaccount.tests import OAuth2TestsMixin
from allauth.tests import MockedResponse, TestCase

from .provider import FrontierProvider


class FrontierTests(OAuth2TestsMixin, TestCase):
    provider_id = FrontierProvider.id

    def get_mocked_response(self):
        return MockedResponse(
            200,
            """
        {
            "email": "<EMAIL>",
            "customer_id": "1234567",
            "firstname": "<PERSON>",
            "developer": false,
            "lastname": "<PERSON><PERSON>",
            "allowedDownloads": ["FORC-FDEV-D-1010", "FORC-FDEV-D-1012", "COMBAT_TUTORIAL_DEMO", "FORC-FDEV-D-1013", "PUBLIC_TEST_SERVER", "FORC_FDEV_V_ADDER_LRPO", "FORC_FDEV_V_CHALLENGER_LRPO", "FORC_FDEV_V_CHIEFTAIN_LRPO", "FORC_FDEV_V_CRUSADER_LRPO", "FORC_FDEV_V_ANACONDA_LRPO", "FORC_FDEV_V_ASP_LRPO", "FORC_FDEV_V_ASP_SCOUT_LRPO", "FORC_FDEV_V_BELUGA_LRPO", "FORC_FDEV_V_COBRA_MKIII_LRPO", "FORC_FDEV_V_DIAMOND_EXPLORER_LRPO", "FORC_FDEV_V_COBRA_MKIV_LRPO", "FORC_FDEV_V_DIAMOND_SCOUT_LRPO", "FORC_FDEV_V_DOLPHIN_LRPO", "FORC_FDEV_V_EAGLE_LRPO", "FORC_FDEV_V_FEDERAL_ASSAULT_LRPO", "FORC_FDEV_V_FEDERAL_CORVETTE_LRPO", "FORC_FDEV_V_FEDDROP_LRPO", "FORC_FDEV_V_FEDERAL_FIGHTER_LRPO", "FORC_FDEV_V_FEDERAL_GUNSHIP_LRPO", "FORC_FDEV_V_FERDELANCE_LRPO", "FORC_FDEV_V_HAULER_LRPO", "FORC_FDEV_V_CLIPPER_LRPO", "FORC_FDEV_V_IMPERIAL_COURIER_LRPO", "FORC_FDEV_V_IMPERIAL_CUTTER_LRPO", "FORC_FDEV_V_IMPERIAL_EAGLE_LRPO", "FORC_FDEV_V_IMPERIAL_FIGHTER_LRPO", "FORC_FDEV_V_KEELBACK_LRPO", "FORC_FDEV_V_KRAIT_LRPO", "FORC_FDEV_V_KRAIT_LITE_LRPO", "FORC_FDEV_V_MAMBA_LRPO", "FORC_FDEV_V_ORCA_LRPO", "FORC_FDEV_V_PYTHON_LRPO", "FORC_FDEV_V_SIDEWINDER_LRPO", "FORC_FDEV_V_TAIPAN_LRPO", "FORC_FDEV_V_MAMMOTH_LRPO", "FORC_FDEV_V_TYPE6_LRPO", "FORC_FDEV_V_TYPE7_LRPO", "FORC_FDEV_V_TYPE9_LRPO", "FORC_FDEV_V_VIPER_MKIII_LRPO", "FORC_FDEV_V_VIPER_MKIV_LRPO", "FORC_FDEV_V_VULTURE_LRPO", "FORC-FDEV-D-1022", "FORC_FDEV_V_DECAL_1091", "FORC_FDEV_V_DECAL_1100", "FORC_FDEV_V_DECAL_1149", "FORC_FDEV_V_DECAL_1150", "FORC_FDEV_V_DECAL_1151", "FORC_FDEV_V_DECAL_1176", "FORC_FDEV_V_DECAL_1177", "FORC-FDEV-DO-1000", "FORC-FDEV-DO-1003", "FORC-FDEV-DO-1006", "PUBLIC_TEST_SERVER_OD"],
            "platform": "frontier"
        }""",
        )
