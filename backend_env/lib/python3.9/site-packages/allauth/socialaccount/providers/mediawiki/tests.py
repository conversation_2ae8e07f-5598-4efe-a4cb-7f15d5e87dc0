from allauth.socialaccount.tests import OAuth2TestsMixin
from allauth.tests import MockedResponse, TestCase

from .provider import MediaWikiProvider


class MediaWikiTests(OAuth2TestsMixin, TestCase):
    provider_id = MediaWikiProvider.id

    def get_mocked_response(self):
        return MockedResponse(
            200,
            """
                {
                    "iss": "https://meta.wikimedia.org",
                    "sub": 12345,
                    "aud": "1234567890abcdef",
                    "exp": *********,
                    "iat": *********,
                    "username": "<PERSON>",
                    "editcount": 123,
                    "confirmed_email": true,
                    "blocked": false,
                    "registered": "**************",
                    "groups": ["*", "user", "autoconfirmed"],
                    "rights": ["read", "edit"],
                    "grants": ["basic"],
                    "email": "<EMAIL>"
                }
            """,
        )
