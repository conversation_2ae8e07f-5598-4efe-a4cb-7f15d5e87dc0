{% extends 'actstream/base.html' %}
{% load activity_tags i18n %}
{% block extra_head %}
<script>
$(function () {
    $("#follow_button, #unfollow_button").click(function () {
        $.post($(this).attr("href"), {});
        $(this).parent().find("#follow_button, #unfollow_button").toggle();
        return false
    });
});
</script>
{% endblock %}


{% block title %}{% if user == actor %}Your feed{% else %}{{ actor }}{% endif %}{% endblock %}
{% block content_title %}{% if user == actor %}Your feed{% else %}{{ actor }}{% endif %}{% endblock %}

{% block navlinks %}
<li><a href="{% url 'actstream_object_feed' content_type_id=ctype.pk  object_id=actor.pk %}">RSS</a></li>
<li><a href="{% url 'actstream_object_feed_atom' content_type_id=ctype.pk  object_id=actor.pk %}">ATOM</a></li>
{% if user == actor %}{% else %}
    <li>
        <a href="{% url 'actstream_unfollow' content_type_id=ctype.pk  object_id=actor.pk %}" id="unfollow_button" style="display:none">{% trans "UNFOLLOW" %}</a>
        <a href="{% url 'actstream_follow' content_type_id=ctype.pk  object_id=actor.pk %}" id="follow_button" >{% trans "FOLLOW" %}</a>
    </li>
{% endif %}
{% endblock %}

{% block content %}
{% if user.is_authenticated %}
<p><b><a href="{% actor_url user %}">{% trans "View my actions" %}</a></b></p>
{% endif %}
<ul>
{% for action in action_list %}
    <li>{% include 'actstream/action.html' %}</li>
{% empty %}
{% trans "No actions yet" %}
{% endfor %}
</ul>
{% endblock %}
