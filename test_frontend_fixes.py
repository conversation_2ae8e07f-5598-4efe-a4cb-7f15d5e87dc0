#!/usr/bin/env python3
"""
Frontend Fixes Verification Test
Tests all the fixes applied to the EMS frontend
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:5174"

class FrontendFixTester:
    def __init__(self):
        self.issues_found = []
        self.fixes_verified = []
        self.token = None

    def log_issue(self, component, description):
        self.issues_found.append({
            'component': component,
            'description': description,
            'timestamp': datetime.now().isoformat()
        })
        print(f"❌ ISSUE: {component} - {description}")

    def log_fix_verified(self, component, description):
        self.fixes_verified.append({
            'component': component,
            'description': description,
            'timestamp': datetime.now().isoformat()
        })
        print(f"✅ FIXED: {component} - {description}")

    def get_auth_token(self):
        """Get authentication token for API testing"""
        try:
            response = requests.post(
                f"{BACKEND_URL}/api/auth/login/",
                json={"username": "superadmin", "password": "password123"},
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('access')
                self.log_fix_verified("Authentication", "Login successful, token obtained")
                return True
            else:
                self.log_issue("Authentication", f"Login failed: {response.status_code}")
                return False
        except Exception as e:
            self.log_issue("Authentication", f"Login error: {str(e)}")
            return False

    def test_dashboard_api_fixes(self):
        """Test that dashboard API fixes are working"""
        if not self.token:
            self.log_issue("Dashboard API", "No authentication token available")
            return

        headers = {"Authorization": f"Bearer {self.token}"}
        
        # Test dashboard stats (the main fix)
        try:
            response = requests.get(f"{BACKEND_URL}/api/dashboard-stats/", headers=headers, timeout=5)
            if response.status_code == 200:
                data = response.json()
                required_fields = ['total_employees', 'total_departments', 'active_projects', 'pending_tasks']
                
                if all(field in data for field in required_fields):
                    self.log_fix_verified("Dashboard API", "Dashboard stats API returning all required fields")
                    
                    # Check if we're getting real data (not just zeros)
                    if data['total_employees'] > 0 and data['total_departments'] > 0:
                        self.log_fix_verified("Dashboard API", "Dashboard returning real data from database")
                    else:
                        self.log_issue("Dashboard API", "Dashboard returning zero values - possible data issue")
                else:
                    missing_fields = [f for f in required_fields if f not in data]
                    self.log_issue("Dashboard API", f"Missing fields: {missing_fields}")
            else:
                self.log_issue("Dashboard API", f"Dashboard stats API error: {response.status_code}")
        except Exception as e:
            self.log_issue("Dashboard API", f"Dashboard stats API exception: {str(e)}")

        # Test superadmin system stats
        try:
            response = requests.get(f"{BACKEND_URL}/api/superadmin/system-stats/", headers=headers, timeout=10)
            if response.status_code == 200:
                self.log_fix_verified("System Stats API", "Superadmin system stats API working")
            else:
                self.log_issue("System Stats API", f"System stats API error: {response.status_code}")
        except Exception as e:
            self.log_issue("System Stats API", f"System stats API exception: {str(e)}")

    def test_core_api_endpoints(self):
        """Test that core API endpoints are accessible"""
        if not self.token:
            return

        headers = {"Authorization": f"Bearer {self.token}"}
        
        # Test core endpoints that frontend components use
        endpoints = [
            ("/api/employees/", "Employee Management"),
            ("/api/departments/", "Department Management"), 
            ("/api/projects/", "Project Management"),
            ("/api/leave-requests/", "Leave Management"),
            ("/api/tasks/", "Task Management")
        ]

        for endpoint, name in endpoints:
            try:
                response = requests.get(f"{BACKEND_URL}{endpoint}", headers=headers, timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    if isinstance(data, dict) and 'results' in data:
                        # Paginated response
                        count = data.get('count', 0)
                        self.log_fix_verified(name, f"API working - {count} records available")
                    elif isinstance(data, list):
                        # Direct list response
                        self.log_fix_verified(name, f"API working - {len(data)} records available")
                    else:
                        self.log_fix_verified(name, "API responding with data")
                else:
                    self.log_issue(name, f"API error: {response.status_code}")
            except Exception as e:
                self.log_issue(name, f"API exception: {str(e)}")

    def test_frontend_accessibility(self):
        """Test that frontend is accessible and loading"""
        try:
            response = requests.get(FRONTEND_URL, timeout=5)
            if response.status_code == 200:
                self.log_fix_verified("Frontend", "Frontend server accessible")
                
                # Check if it's actually serving the React app
                if 'react' in response.text.lower() or 'vite' in response.text.lower():
                    self.log_fix_verified("Frontend", "React application loading")
                else:
                    self.log_issue("Frontend", "Frontend not serving React application")
            else:
                self.log_issue("Frontend", f"Frontend server error: {response.status_code}")
        except Exception as e:
            self.log_issue("Frontend", f"Frontend server not accessible: {str(e)}")

    def test_environment_configuration(self):
        """Test that environment configuration is working"""
        # This is indirect - we test if the API calls are working with the env vars
        if self.token:
            self.log_fix_verified("Environment Config", "API base URL configuration working (API calls successful)")
        else:
            self.log_issue("Environment Config", "API configuration may be incorrect")

    def run_comprehensive_test(self):
        """Run all tests to verify fixes"""
        print("🧪 Starting Frontend Fixes Verification...")
        print("=" * 60)
        
        # Test infrastructure
        print("\n📡 Testing Infrastructure...")
        self.test_frontend_accessibility()
        
        # Test authentication
        print("\n🔐 Testing Authentication...")
        auth_success = self.get_auth_token()
        
        if auth_success:
            # Test API fixes
            print("\n📊 Testing Dashboard API Fixes...")
            self.test_dashboard_api_fixes()
            
            print("\n🔧 Testing Core API Endpoints...")
            self.test_core_api_endpoints()
            
            print("\n⚙️ Testing Environment Configuration...")
            self.test_environment_configuration()
        
        # Generate report
        self.generate_report()

    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📋 FRONTEND FIXES VERIFICATION REPORT")
        print("=" * 60)
        
        print(f"\n✅ Fixes Verified: {len(self.fixes_verified)}")
        for fix in self.fixes_verified:
            print(f"  • {fix['component']}: {fix['description']}")
        
        print(f"\n❌ Issues Found: {len(self.issues_found)}")
        for issue in self.issues_found:
            print(f"  • {issue['component']}: {issue['description']}")
        
        if len(self.issues_found) == 0:
            print("\n🎉 ALL FIXES VERIFIED SUCCESSFULLY!")
            print("The application should now be fully functional for end-to-end testing.")
        else:
            print(f"\n⚠️ {len(self.issues_found)} issues still need attention.")
        
        # Calculate success rate
        total_tests = len(self.fixes_verified) + len(self.issues_found)
        if total_tests > 0:
            success_rate = (len(self.fixes_verified) / total_tests) * 100
            print(f"\n📈 Success Rate: {success_rate:.1f}%")

if __name__ == "__main__":
    tester = FrontendFixTester()
    tester.run_comprehensive_test()
