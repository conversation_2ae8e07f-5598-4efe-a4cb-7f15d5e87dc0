#!/usr/bin/env python3
"""
Comprehensive Feature Audit
Checks for missing features and functionality that users would expect
"""

import requests
import json

# Configuration
BACKEND_URL = "http://localhost:8000"

class FeatureAuditor:
    def __init__(self):
        self.missing_features = []
        self.token = None

    def get_auth_token(self):
        """Get authentication token"""
        try:
            response = requests.post(
                f"{BACKEND_URL}/api/auth/login/",
                json={"username": "superadmin", "password": "password123"},
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('access')
                return True
            return False
        except Exception as e:
            print(f"❌ Authentication failed: {str(e)}")
            return False

    def log_missing_feature(self, category, feature, description, priority="MEDIUM"):
        self.missing_features.append({
            'category': category,
            'feature': feature,
            'description': description,
            'priority': priority
        })
        print(f"❌ {priority}: {category} - Missing {feature}: {description}")

    def audit_advanced_features(self):
        """Audit advanced features that users might expect"""
        print("\n🔍 Auditing Advanced Features...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        # Check for bulk operations
        try:
            # Test bulk delete (should exist in modern EMS)
            response = requests.post(f"{BACKEND_URL}/api/employees/bulk_delete/", 
                                   json={"ids": []}, headers=headers, timeout=5)
            if response.status_code not in [200, 400, 405]:  # 400/405 expected for empty array
                self.log_missing_feature("Employee Management", "Bulk Delete", 
                                        "No bulk delete functionality for employees", "HIGH")
            else:
                print("✅ Bulk operations endpoint exists")
        except Exception as e:
            self.log_missing_feature("Employee Management", "Bulk Operations", 
                                    f"Bulk operations not accessible: {str(e)}", "HIGH")

        # Check for data export functionality
        try:
            response = requests.get(f"{BACKEND_URL}/api/employees/export/", 
                                  headers=headers, timeout=5)
            if response.status_code == 404:
                self.log_missing_feature("Data Management", "Export Functionality", 
                                        "No data export functionality", "MEDIUM")
            else:
                print("✅ Export functionality available")
        except Exception as e:
            self.log_missing_feature("Data Management", "Export Functionality", 
                                    f"Export not accessible: {str(e)}", "MEDIUM")

        # Check for file upload functionality
        try:
            response = requests.post(f"{BACKEND_URL}/api/employees/import/", 
                                   headers=headers, timeout=5)
            if response.status_code == 404:
                self.log_missing_feature("Data Management", "Import Functionality", 
                                        "No data import functionality", "MEDIUM")
            else:
                print("✅ Import functionality available")
        except Exception as e:
            self.log_missing_feature("Data Management", "Import Functionality", 
                                    f"Import not accessible: {str(e)}", "MEDIUM")

    def audit_reporting_features(self):
        """Audit reporting and analytics features"""
        print("\n🔍 Auditing Reporting Features...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        # Check for advanced reports
        expected_reports = [
            ("employee-reports", "Employee Reports"),
            ("financial-reports", "Financial Reports"),
            ("project-reports", "Project Reports"),
            ("performance-reports", "Performance Reports"),
            ("attendance-reports", "Attendance Reports")
        ]
        
        for endpoint, report_name in expected_reports:
            try:
                response = requests.get(f"{BACKEND_URL}/api/{endpoint}/", 
                                      headers=headers, timeout=5)
                if response.status_code == 404:
                    self.log_missing_feature("Reporting", report_name, 
                                            f"No {report_name.lower()} functionality", "MEDIUM")
                else:
                    print(f"✅ {report_name} available")
            except Exception as e:
                self.log_missing_feature("Reporting", report_name, 
                                        f"{report_name} not accessible: {str(e)}", "MEDIUM")

    def audit_notification_features(self):
        """Audit notification and communication features"""
        print("\n🔍 Auditing Notification Features...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        # Check for notifications system
        try:
            response = requests.get(f"{BACKEND_URL}/api/notifications/", 
                                  headers=headers, timeout=5)
            if response.status_code == 404:
                self.log_missing_feature("Communication", "Notifications", 
                                        "No notification system", "HIGH")
            else:
                print("✅ Notification system available")
        except Exception as e:
            self.log_missing_feature("Communication", "Notifications", 
                                    f"Notifications not accessible: {str(e)}", "HIGH")

        # Check for email integration
        try:
            response = requests.post(f"{BACKEND_URL}/api/send-email/", 
                                   json={"test": True}, headers=headers, timeout=5)
            if response.status_code == 404:
                self.log_missing_feature("Communication", "Email Integration", 
                                        "No email sending functionality", "MEDIUM")
            else:
                print("✅ Email integration available")
        except Exception as e:
            self.log_missing_feature("Communication", "Email Integration", 
                                    f"Email integration not accessible: {str(e)}", "MEDIUM")

    def audit_security_features(self):
        """Audit security and access control features"""
        print("\n🔍 Auditing Security Features...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        # Check for audit logs
        try:
            response = requests.get(f"{BACKEND_URL}/api/audit-logs/", 
                                  headers=headers, timeout=5)
            if response.status_code == 404:
                self.log_missing_feature("Security", "Audit Logs", 
                                        "No audit logging system", "HIGH")
            else:
                print("✅ Audit logging available")
        except Exception as e:
            self.log_missing_feature("Security", "Audit Logs", 
                                    f"Audit logs not accessible: {str(e)}", "HIGH")

        # Check for role management
        try:
            response = requests.get(f"{BACKEND_URL}/api/roles/", 
                                  headers=headers, timeout=5)
            if response.status_code == 404:
                self.log_missing_feature("Security", "Role Management", 
                                        "No role management system", "HIGH")
            else:
                print("✅ Role management available")
        except Exception as e:
            self.log_missing_feature("Security", "Role Management", 
                                    f"Role management not accessible: {str(e)}", "HIGH")

    def audit_integration_features(self):
        """Audit integration and API features"""
        print("\n🔍 Auditing Integration Features...")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        # Check for API documentation
        try:
            response = requests.get(f"{BACKEND_URL}/api/docs/", timeout=5)
            if response.status_code == 404:
                self.log_missing_feature("Integration", "API Documentation", 
                                        "No API documentation available", "MEDIUM")
            else:
                print("✅ API documentation available")
        except Exception as e:
            self.log_missing_feature("Integration", "API Documentation", 
                                    f"API docs not accessible: {str(e)}", "MEDIUM")

        # Check for webhooks
        try:
            response = requests.get(f"{BACKEND_URL}/api/webhooks/", 
                                  headers=headers, timeout=5)
            if response.status_code == 404:
                self.log_missing_feature("Integration", "Webhooks", 
                                        "No webhook functionality", "LOW")
            else:
                print("✅ Webhook functionality available")
        except Exception as e:
            self.log_missing_feature("Integration", "Webhooks", 
                                    f"Webhooks not accessible: {str(e)}", "LOW")

    def run_comprehensive_audit(self):
        """Run comprehensive feature audit"""
        print("🔍 Starting Comprehensive Feature Audit...")
        print("=" * 70)
        
        # Get authentication
        if not self.get_auth_token():
            print("❌ Cannot proceed without authentication")
            return
        
        # Audit all feature categories
        self.audit_advanced_features()
        self.audit_reporting_features()
        self.audit_notification_features()
        self.audit_security_features()
        self.audit_integration_features()
        
        # Generate report
        self.generate_feature_report()

    def generate_feature_report(self):
        """Generate comprehensive feature report"""
        print("\n" + "=" * 70)
        print("📋 COMPREHENSIVE FEATURE AUDIT REPORT")
        print("=" * 70)
        
        if not self.missing_features:
            print("\n🎉 ALL EXPECTED FEATURES ARE AVAILABLE!")
            print("The EMS application has comprehensive functionality.")
        else:
            print(f"\n🚨 MISSING FEATURES ({len(self.missing_features)} features):")
            
            # Group by priority
            high_priority = [f for f in self.missing_features if f['priority'] == 'HIGH']
            medium_priority = [f for f in self.missing_features if f['priority'] == 'MEDIUM']
            low_priority = [f for f in self.missing_features if f['priority'] == 'LOW']
            
            if high_priority:
                print(f"\n🔥 HIGH PRIORITY ({len(high_priority)} features):")
                for feature in high_priority:
                    print(f"  • {feature['category']}: {feature['feature']}")
                    print(f"    {feature['description']}")
                    print()
            
            if medium_priority:
                print(f"\n⚠️ MEDIUM PRIORITY ({len(medium_priority)} features):")
                for feature in medium_priority:
                    print(f"  • {feature['category']}: {feature['feature']}")
                    print(f"    {feature['description']}")
                    print()
            
            if low_priority:
                print(f"\n💡 LOW PRIORITY ({len(low_priority)} features):")
                for feature in low_priority:
                    print(f"  • {feature['category']}: {feature['feature']}")
                    print(f"    {feature['description']}")
                    print()
        
        print(f"\n📈 SUMMARY:")
        print(f"Total missing features: {len(self.missing_features)}")
        if len(self.missing_features) > 0:
            high_count = len([f for f in self.missing_features if f['priority'] == 'HIGH'])
            if high_count > 0:
                print(f"🔥 {high_count} high priority features need immediate attention")
            print(f"💡 Consider implementing missing features to enhance user experience")

if __name__ == "__main__":
    auditor = FeatureAuditor()
    auditor.run_comprehensive_audit()
