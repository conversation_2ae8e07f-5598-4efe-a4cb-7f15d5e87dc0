#!/usr/bin/env python3
"""
Backend Issue Fix Script
Run this from the backend directory to fix authentication issues
"""

import os
import sys
import django
from django.core.management import call_command

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from ems.models import UserProfile, Role

def main():
    print("🔧 Fixing EMS Backend Issues...")
    
    try:
        # Run migrations first
        print("\n📋 Running migrations...")
        call_command('migrate')
        print("✅ Migrations completed")
        
        # Fix superadmin user
        print("\n📋 Fixing superadmin user...")
        try:
            superadmin = User.objects.get(username='superadmin')
            superadmin.set_password('superadmin123')
            superadmin.save()
            print("✅ Updated superadmin password")
            
            # Check role
            try:
                profile = superadmin.userprofile
                if profile.role.name != 'SUPERADMIN':
                    superadmin_role = Role.objects.get(name='SUPERADMIN')
                    profile.role = superadmin_role
                    profile.save()
                    print("✅ Updated superadmin role")
            except UserProfile.DoesNotExist:
                print("⚠️  Superadmin profile missing")
                
        except User.DoesNotExist:
            print("⚠️  Superadmin user not found")
        
        # Fix admin user
        print("\n📋 Fixing admin user...")
        try:
            admin = User.objects.get(username='admin')
            admin.set_password('admin123')
            admin.save()
            print("✅ Updated admin password")
        except User.DoesNotExist:
            print("⚠️  Admin user not found")
        
        # Create SUPERADMIN role if it doesn't exist
        print("\n📋 Creating SUPERADMIN role...")
        try:
            from ems.models import Role
            superadmin_role, created = Role.objects.get_or_create(
                name='SUPERADMIN',
                defaults={
                    'name_ar': 'مدير النظام الأعلى',
                    'description': 'Super Administrator with full system access',
                    'description_ar': 'مدير النظام الأعلى مع صلاحيات كاملة للنظام'
                }
            )
            if created:
                print("✅ Created SUPERADMIN role")
            else:
                print("✅ SUPERADMIN role already exists")
        except Exception as e:
            print(f"⚠️  Role creation issue: {e}")
        
        # Verify credentials
        print("\n📋 Verifying credentials...")
        
        superadmin = User.objects.get(username='superadmin')
        if superadmin.check_password('superadmin123'):
            print("✅ superadmin / superadmin123 - WORKING")
        else:
            print("❌ superadmin password verification failed")
        
        admin = User.objects.get(username='admin')
        if admin.check_password('admin123'):
            print("✅ admin / admin123 - WORKING")
        else:
            print("❌ admin password verification failed")
        
        hrmanager = User.objects.get(username='hrmanager')
        if hrmanager.check_password('password123'):
            print("✅ hrmanager / password123 - WORKING")
        else:
            print("❌ hrmanager password verification failed")
        
        print("\n🎉 Backend fixes completed!")
        print("\n📋 Test Credentials:")
        print("- superadmin / superadmin123")
        print("- admin / admin123")
        print("- hrmanager / password123")
        print("- financemanager / password123")
        print("- employee1 / password123")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
