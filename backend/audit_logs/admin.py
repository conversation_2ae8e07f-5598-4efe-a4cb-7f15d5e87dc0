from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
import json

from .models import AuditLog, SecurityEvent, AuditConfiguration

@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    list_display = [
        'timestamp', 'username', 'action', 'severity', 'content_type',
        'object_repr', 'ip_address', 'success', 'colored_severity'
    ]
    list_filter = [
        'action', 'severity', 'success', 'timestamp', 'content_type'
    ]
    search_fields = [
        'username', 'object_repr', 'ip_address', 'request_path', 'error_message'
    ]
    readonly_fields = [
        'id', 'timestamp', 'user', 'username', 'session_key', 'action',
        'severity', 'content_type', 'object_id', 'content_object',
        'object_repr', 'ip_address', 'user_agent', 'request_method',
        'request_path', 'formatted_request_params', 'formatted_changes',
        'formatted_additional_data', 'success', 'error_message',
        'compliance_tags', 'retention_date'
    ]
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('timestamp', 'user', 'username', 'session_key', 'action', 'severity')
        }),
        ('Target Object', {
            'fields': ('content_type', 'object_id', 'object_repr')
        }),
        ('Request Information', {
            'fields': ('ip_address', 'user_agent', 'request_method', 'request_path', 'formatted_request_params')
        }),
        ('Changes & Data', {
            'fields': ('formatted_changes', 'formatted_additional_data')
        }),
        ('Outcome', {
            'fields': ('success', 'error_message')
        }),
        ('Compliance & Retention', {
            'fields': ('compliance_tags', 'is_sensitive', 'retention_date')
        }),
    )
    
    def colored_severity(self, obj):
        colors = {
            'low': 'green',
            'medium': 'orange',
            'high': 'red',
            'critical': 'darkred'
        }
        color = colors.get(obj.severity, 'black')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_severity_display()
        )
    colored_severity.short_description = 'Severity'
    
    def formatted_request_params(self, obj):
        if obj.request_params:
            return format_html('<pre>{}</pre>', json.dumps(obj.request_params, indent=2))
        return 'None'
    formatted_request_params.short_description = 'Request Parameters'
    
    def formatted_changes(self, obj):
        if obj.changes:
            return format_html('<pre>{}</pre>', json.dumps(obj.changes, indent=2))
        return 'No changes'
    formatted_changes.short_description = 'Changes'
    
    def formatted_additional_data(self, obj):
        if obj.additional_data:
            return format_html('<pre>{}</pre>', json.dumps(obj.additional_data, indent=2))
        return 'None'
    formatted_additional_data.short_description = 'Additional Data'
    
    def has_add_permission(self, request):
        return False  # Audit logs should not be manually created
    
    def has_change_permission(self, request, obj=None):
        return False  # Audit logs should not be modified
    
    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser  # Only superusers can delete audit logs

@admin.register(SecurityEvent)
class SecurityEventAdmin(admin.ModelAdmin):
    list_display = [
        'created_at', 'event_type', 'risk_score', 'colored_risk_level',
        'investigation_status', 'is_blocked', 'audit_log_link'
    ]
    list_filter = [
        'event_type', 'investigation_status', 'is_blocked', 'created_at'
    ]
    search_fields = [
        'audit_log__username', 'audit_log__ip_address', 'manual_response'
    ]
    readonly_fields = [
        'id', 'audit_log', 'event_type', 'risk_score', 'is_blocked',
        'automated_response', 'notifications_sent', 'created_at'
    ]
    date_hierarchy = 'created_at'
    ordering = ['-created_at']
    
    fieldsets = (
        ('Event Information', {
            'fields': ('audit_log', 'event_type', 'risk_score', 'is_blocked', 'created_at')
        }),
        ('Investigation', {
            'fields': ('investigation_status', 'manual_response')
        }),
        ('Automated Response', {
            'fields': ('automated_response', 'notifications_sent')
        }),
    )
    
    def colored_risk_level(self, obj):
        if obj.risk_score >= 80:
            color = 'darkred'
            level = 'Critical'
        elif obj.risk_score >= 60:
            color = 'red'
            level = 'High'
        elif obj.risk_score >= 40:
            color = 'orange'
            level = 'Medium'
        else:
            color = 'green'
            level = 'Low'
        
        return format_html(
            '<span style="color: {}; font-weight: bold;">{} ({})</span>',
            color,
            level,
            obj.risk_score
        )
    colored_risk_level.short_description = 'Risk Level'
    
    def audit_log_link(self, obj):
        if obj.audit_log:
            url = reverse('admin:audit_logs_auditlog_change', args=[obj.audit_log.id])
            return format_html('<a href="{}">View Audit Log</a>', url)
        return 'No audit log'
    audit_log_link.short_description = 'Audit Log'
    
    def has_add_permission(self, request):
        return False  # Security events should not be manually created

@admin.register(AuditConfiguration)
class AuditConfigurationAdmin(admin.ModelAdmin):
    list_display = ['id', 'updated_at', 'log_admin_actions', 'gdpr_compliance']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Logging Settings', {
            'fields': (
                'log_read_operations', 'log_failed_requests', 'log_admin_actions',
                'log_data_exports', 'log_bulk_operations'
            )
        }),
        ('Data Protection', {
            'fields': ('mask_sensitive_fields', 'sensitive_field_patterns')
        }),
        ('Retention Policies', {
            'fields': (
                'default_retention_days', 'security_event_retention_days',
                'compliance_retention_days'
            )
        }),
        ('Performance', {
            'fields': ('async_logging', 'batch_size')
        }),
        ('Security Thresholds', {
            'fields': ('failed_login_threshold', 'suspicious_activity_threshold')
        }),
        ('Compliance', {
            'fields': ('gdpr_compliance', 'hipaa_compliance', 'sox_compliance')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )
    
    def has_add_permission(self, request):
        # Only allow one configuration instance
        return not AuditConfiguration.objects.exists()
    
    def has_delete_permission(self, request, obj=None):
        return False  # Don't allow deletion of configuration
