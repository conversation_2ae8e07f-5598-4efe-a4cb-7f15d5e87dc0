from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth.models import User
from django.utils import timezone
from django.db.models import Q, Count
from datetime import timedelta

from .models import AuditLog, SecurityEvent, AuditConfiguration
from .serializers import (
    AuditLogSerializer, SecurityEventSerializer, AuditConfigurationSerializer,
    AuditStatsSerializer, UserActivityReportSerializer, SecuritySummarySerializer,
    AuditSearchSerializer
)
from .services import AuditReporter

class AuditLogViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for viewing audit logs (admin only)"""
    queryset = AuditLog.objects.all()
    serializer_class = AuditLogSerializer
    permission_classes = [permissions.IsAdminUser]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Apply filters
        user_id = self.request.query_params.get('user_id')
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        username = self.request.query_params.get('username')
        if username:
            queryset = queryset.filter(username__icontains=username)
        
        action = self.request.query_params.get('action')
        if action:
            queryset = queryset.filter(action=action)
        
        severity = self.request.query_params.get('severity')
        if severity:
            queryset = queryset.filter(severity=severity)
        
        ip_address = self.request.query_params.get('ip_address')
        if ip_address:
            queryset = queryset.filter(ip_address=ip_address)
        
        success = self.request.query_params.get('success')
        if success is not None:
            queryset = queryset.filter(success=success.lower() == 'true')
        
        # Date range filtering
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(timestamp__gte=start_date)
        if end_date:
            queryset = queryset.filter(timestamp__lte=end_date)
        
        # Search in object representation and error messages
        search_term = self.request.query_params.get('search')
        if search_term:
            queryset = queryset.filter(
                Q(object_repr__icontains=search_term) |
                Q(error_message__icontains=search_term) |
                Q(request_path__icontains=search_term)
            )
        
        return queryset.order_by('-timestamp')
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get audit log statistics"""
        now = timezone.now()
        today = now.date()
        week_ago = now - timedelta(days=7)
        month_ago = now - timedelta(days=30)
        
        # Basic counts
        total_logs = AuditLog.objects.count()
        logs_today = AuditLog.objects.filter(timestamp__date=today).count()
        logs_this_week = AuditLog.objects.filter(timestamp__gte=week_ago).count()
        logs_this_month = AuditLog.objects.filter(timestamp__gte=month_ago).count()
        failed_actions = AuditLog.objects.filter(success=False).count()
        
        # Security events
        security_events = SecurityEvent.objects.count()
        high_risk_events = SecurityEvent.objects.filter(risk_score__gte=70).count()
        
        # Unique counts
        unique_users = AuditLog.objects.values('user').distinct().count()
        unique_ips = AuditLog.objects.values('ip_address').distinct().count()
        
        # Group by action type
        actions_by_type = dict(
            AuditLog.objects.values('action')
            .annotate(count=Count('id'))
            .values_list('action', 'count')
        )
        
        # Group by severity
        severity_distribution = dict(
            AuditLog.objects.values('severity')
            .annotate(count=Count('id'))
            .values_list('severity', 'count')
        )
        
        # Top users by activity
        top_users = list(
            AuditLog.objects.values('username')
            .annotate(count=Count('id'))
            .order_by('-count')[:10]
            .values_list('username', 'count')
        )
        
        # Top IPs by activity
        top_ips = list(
            AuditLog.objects.values('ip_address')
            .annotate(count=Count('id'))
            .order_by('-count')[:10]
            .values_list('ip_address', 'count')
        )
        
        # Recent security events
        recent_security_events = SecurityEvent.objects.order_by('-created_at')[:5]
        
        stats_data = {
            'total_logs': total_logs,
            'logs_today': logs_today,
            'logs_this_week': logs_this_week,
            'logs_this_month': logs_this_month,
            'failed_actions': failed_actions,
            'security_events': security_events,
            'high_risk_events': high_risk_events,
            'unique_users': unique_users,
            'unique_ips': unique_ips,
            'actions_by_type': actions_by_type,
            'severity_distribution': severity_distribution,
            'top_users': top_users,
            'top_ips': top_ips,
            'recent_security_events': recent_security_events
        }
        
        serializer = AuditStatsSerializer(stats_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def search(self, request):
        """Advanced search for audit logs"""
        serializer = AuditSearchSerializer(data=request.data)
        if serializer.is_valid():
            data = serializer.validated_data
            queryset = AuditLog.objects.all()
            
            # Apply search filters
            if 'user_id' in data:
                queryset = queryset.filter(user_id=data['user_id'])
            if 'username' in data:
                queryset = queryset.filter(username__icontains=data['username'])
            if 'action' in data:
                queryset = queryset.filter(action=data['action'])
            if 'severity' in data:
                queryset = queryset.filter(severity=data['severity'])
            if 'ip_address' in data:
                queryset = queryset.filter(ip_address=data['ip_address'])
            if 'start_date' in data:
                queryset = queryset.filter(timestamp__gte=data['start_date'])
            if 'end_date' in data:
                queryset = queryset.filter(timestamp__lte=data['end_date'])
            if 'success' in data:
                queryset = queryset.filter(success=data['success'])
            if 'search_term' in data:
                term = data['search_term']
                queryset = queryset.filter(
                    Q(object_repr__icontains=term) |
                    Q(error_message__icontains=term) |
                    Q(request_path__icontains=term)
                )
            if 'compliance_tag' in data:
                queryset = queryset.filter(compliance_tags__contains=[data['compliance_tag']])
            
            # Paginate results
            page = self.paginate_queryset(queryset.order_by('-timestamp'))
            if page is not None:
                serializer = AuditLogSerializer(page, many=True)
                return self.get_paginated_response(serializer.data)
            
            serializer = AuditLogSerializer(queryset.order_by('-timestamp'), many=True)
            return Response(serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def user_activity(self, request):
        """Get user activity report"""
        user_id = request.query_params.get('user_id')
        days = int(request.query_params.get('days', 30))
        
        if not user_id:
            return Response(
                {'error': 'user_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        report = AuditReporter.generate_user_activity_report(user, start_date, end_date)
        serializer = UserActivityReportSerializer(report)
        return Response(serializer.data)

class SecurityEventViewSet(viewsets.ModelViewSet):
    """ViewSet for managing security events (admin only)"""
    queryset = SecurityEvent.objects.all()
    serializer_class = SecurityEventSerializer
    permission_classes = [permissions.IsAdminUser]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by event type
        event_type = self.request.query_params.get('event_type')
        if event_type:
            queryset = queryset.filter(event_type=event_type)
        
        # Filter by risk score
        min_risk = self.request.query_params.get('min_risk_score')
        if min_risk:
            queryset = queryset.filter(risk_score__gte=int(min_risk))
        
        # Filter by investigation status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(investigation_status=status_filter)
        
        return queryset.order_by('-created_at')
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get security summary report"""
        days = int(request.query_params.get('days', 30))
        report = AuditReporter.generate_security_summary(days)
        serializer = SecuritySummarySerializer(report)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def update_investigation(self, request, pk=None):
        """Update investigation status and notes"""
        security_event = self.get_object()
        
        investigation_status = request.data.get('investigation_status')
        manual_response = request.data.get('manual_response', '')
        
        if investigation_status:
            security_event.investigation_status = investigation_status
        
        if manual_response:
            security_event.manual_response = manual_response
        
        security_event.save()
        
        serializer = self.get_serializer(security_event)
        return Response(serializer.data)

class AuditConfigurationViewSet(viewsets.ModelViewSet):
    """ViewSet for managing audit configuration (admin only)"""
    queryset = AuditConfiguration.objects.all()
    serializer_class = AuditConfigurationSerializer
    permission_classes = [permissions.IsAdminUser]
    
    @action(detail=False, methods=['get', 'put', 'patch'])
    def current(self, request):
        """Get or update current audit configuration"""
        config = AuditConfiguration.get_config()
        
        if request.method == 'GET':
            serializer = self.get_serializer(config)
            return Response(serializer.data)
        
        elif request.method in ['PUT', 'PATCH']:
            partial = request.method == 'PATCH'
            serializer = self.get_serializer(config, data=request.data, partial=partial)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
