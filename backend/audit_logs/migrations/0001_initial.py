# Generated by Django 4.2.7 on 2025-07-14 16:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AuditConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('log_read_operations', models.BooleanField(default=False, help_text='Log read/view operations')),
                ('log_failed_requests', models.BooleanField(default=True, help_text='Log failed requests')),
                ('log_admin_actions', models.BooleanField(default=True, help_text='Log all admin actions')),
                ('log_data_exports', models.BooleanField(default=True, help_text='Log data exports')),
                ('log_bulk_operations', models.BooleanField(default=True, help_text='Log bulk operations')),
                ('mask_sensitive_fields', models.BooleanField(default=True, help_text='Mask sensitive data in logs')),
                ('sensitive_field_patterns', models.JSONField(blank=True, default=list, help_text='Regex patterns for sensitive fields')),
                ('default_retention_days', models.IntegerField(default=2555, help_text='Default retention period (7 years)')),
                ('security_event_retention_days', models.IntegerField(default=3650, help_text='Security events retention (10 years)')),
                ('compliance_retention_days', models.IntegerField(default=2555, help_text='Compliance logs retention (7 years)')),
                ('async_logging', models.BooleanField(default=True, help_text='Use async logging for performance')),
                ('batch_size', models.IntegerField(default=100, help_text='Batch size for bulk logging')),
                ('failed_login_threshold', models.IntegerField(default=5, help_text='Failed logins before alert')),
                ('suspicious_activity_threshold', models.IntegerField(default=10, help_text='Suspicious activities before alert')),
                ('gdpr_compliance', models.BooleanField(default=True, help_text='Enable GDPR compliance features')),
                ('hipaa_compliance', models.BooleanField(default=False, help_text='Enable HIPAA compliance features')),
                ('sox_compliance', models.BooleanField(default=False, help_text='Enable SOX compliance features')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Audit Configuration',
                'verbose_name_plural': 'Audit Configurations',
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('username', models.CharField(help_text='Username at time of action (preserved even if user deleted)', max_length=150)),
                ('session_key', models.CharField(blank=True, help_text='Django session key', max_length=40)),
                ('action', models.CharField(choices=[('create', 'Create'), ('read', 'Read'), ('update', 'Update'), ('delete', 'Delete'), ('login', 'Login'), ('logout', 'Logout'), ('login_failed', 'Login Failed'), ('password_change', 'Password Change'), ('permission_denied', 'Permission Denied'), ('export', 'Export'), ('import', 'Import'), ('bulk_update', 'Bulk Update'), ('bulk_delete', 'Bulk Delete'), ('system_config', 'System Configuration'), ('security_event', 'Security Event')], max_length=20)),
                ('severity', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='low', max_length=10)),
                ('object_id', models.CharField(blank=True, max_length=255)),
                ('object_repr', models.CharField(blank=True, help_text='String representation of the object', max_length=200)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('request_method', models.CharField(blank=True, max_length=10)),
                ('request_path', models.CharField(blank=True, max_length=500)),
                ('request_params', models.JSONField(blank=True, default=dict, help_text='GET/POST parameters')),
                ('changes', models.JSONField(blank=True, default=dict, help_text='Before/after values for updates')),
                ('additional_data', models.JSONField(blank=True, default=dict, help_text='Additional context data')),
                ('success', models.BooleanField(default=True)),
                ('error_message', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
                ('retention_date', models.DateTimeField(blank=True, help_text='When this log can be deleted', null=True)),
                ('is_sensitive', models.BooleanField(default=False, help_text='Contains sensitive data')),
                ('compliance_tags', models.JSONField(blank=True, default=list, help_text='Tags for compliance requirements')),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='contenttypes.contenttype')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='audit_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='SecurityEvent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('event_type', models.CharField(choices=[('suspicious_login', 'Suspicious Login'), ('multiple_failed_logins', 'Multiple Failed Logins'), ('privilege_escalation', 'Privilege Escalation'), ('data_breach_attempt', 'Data Breach Attempt'), ('unauthorized_access', 'Unauthorized Access'), ('malicious_request', 'Malicious Request'), ('account_lockout', 'Account Lockout'), ('password_policy_violation', 'Password Policy Violation'), ('session_hijacking', 'Session Hijacking'), ('sql_injection_attempt', 'SQL Injection Attempt'), ('xss_attempt', 'XSS Attempt'), ('csrf_attempt', 'CSRF Attempt')], max_length=50)),
                ('risk_score', models.IntegerField(default=0, help_text='Risk score from 0-100')),
                ('is_blocked', models.BooleanField(default=False, help_text='Was the action blocked')),
                ('investigation_status', models.CharField(choices=[('open', 'Open'), ('investigating', 'Investigating'), ('resolved', 'Resolved'), ('false_positive', 'False Positive')], default='open', max_length=20)),
                ('automated_response', models.JSONField(blank=True, default=dict, help_text='Automated responses taken')),
                ('manual_response', models.TextField(blank=True, help_text='Manual investigation notes')),
                ('notifications_sent', models.JSONField(blank=True, default=list, help_text='List of notifications sent')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('audit_log', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='security_event', to='audit_logs.auditlog')),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['event_type', '-created_at'], name='audit_logs__event_t_7c3851_idx'), models.Index(fields=['risk_score', '-created_at'], name='audit_logs__risk_sc_0551de_idx'), models.Index(fields=['investigation_status'], name='audit_logs__investi_4c8f64_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['user', '-timestamp'], name='audit_logs__user_id_64dfe5_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['action', '-timestamp'], name='audit_logs__action_406b32_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['severity', '-timestamp'], name='audit_logs__severit_96a515_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['content_type', 'object_id'], name='audit_logs__content_9ccfc6_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['ip_address', '-timestamp'], name='audit_logs__ip_addr_3590ff_idx'),
        ),
        migrations.AddIndex(
            model_name='auditlog',
            index=models.Index(fields=['success', '-timestamp'], name='audit_logs__success_3bea62_idx'),
        ),
    ]
