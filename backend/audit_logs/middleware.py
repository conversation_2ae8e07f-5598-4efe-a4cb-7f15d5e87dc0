from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth.models import AnonymousUser
from .signals import set_current_request, set_current_user

class AuditMiddleware(MiddlewareMixin):
    """Middleware to capture request context for audit logging"""
    
    def process_request(self, request):
        """Store request and user in thread-local storage"""
        set_current_request(request)
        
        # Set current user (authenticated or anonymous)
        user = getattr(request, 'user', None)
        if user and not isinstance(user, AnonymousUser):
            set_current_user(user)
        else:
            set_current_user(None)
        
        return None
    
    def process_response(self, request, response):
        """Clean up thread-local storage"""
        set_current_request(None)
        set_current_user(None)
        return response
    
    def process_exception(self, request, exception):
        """Clean up thread-local storage on exception"""
        set_current_request(None)
        set_current_user(None)
        return None
