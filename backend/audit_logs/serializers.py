from rest_framework import serializers
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from .models import AuditLog, SecurityEvent, AuditConfiguration

class AuditLogSerializer(serializers.ModelSerializer):
    user_display = serializers.CharField(source='username', read_only=True)
    content_type_display = serializers.SerializerMethodField()
    action_display = serializers.CharField(source='get_action_display', read_only=True)
    severity_display = serializers.CharField(source='get_severity_display', read_only=True)
    formatted_changes = serializers.CharField(read_only=True)
    time_ago = serializers.SerializerMethodField()
    
    class Meta:
        model = AuditLog
        fields = [
            'id', 'user_display', 'username', 'action', 'action_display',
            'severity', 'severity_display', 'content_type_display',
            'object_repr', 'ip_address', 'user_agent', 'request_method',
            'request_path', 'changes', 'formatted_changes', 'additional_data',
            'success', 'error_message', 'timestamp', 'time_ago',
            'compliance_tags', 'is_sensitive'
        ]
        read_only_fields = ['id', 'timestamp']
    
    def get_content_type_display(self, obj):
        """Get human-readable content type"""
        if obj.content_type:
            return f"{obj.content_type.app_label}.{obj.content_type.model}"
        return None
    
    def get_time_ago(self, obj):
        """Get human-readable time ago"""
        from django.utils.timesince import timesince
        return timesince(obj.timestamp)

class SecurityEventSerializer(serializers.ModelSerializer):
    audit_log = AuditLogSerializer(read_only=True)
    event_type_display = serializers.CharField(source='get_event_type_display', read_only=True)
    investigation_status_display = serializers.CharField(source='get_investigation_status_display', read_only=True)
    risk_level = serializers.SerializerMethodField()
    time_ago = serializers.SerializerMethodField()
    
    class Meta:
        model = SecurityEvent
        fields = [
            'id', 'audit_log', 'event_type', 'event_type_display',
            'risk_score', 'risk_level', 'is_blocked', 'investigation_status',
            'investigation_status_display', 'automated_response',
            'manual_response', 'notifications_sent', 'created_at',
            'updated_at', 'time_ago'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_risk_level(self, obj):
        """Get risk level based on score"""
        if obj.risk_score >= 80:
            return 'Critical'
        elif obj.risk_score >= 60:
            return 'High'
        elif obj.risk_score >= 40:
            return 'Medium'
        else:
            return 'Low'
    
    def get_time_ago(self, obj):
        """Get human-readable time ago"""
        from django.utils.timesince import timesince
        return timesince(obj.created_at)

class AuditConfigurationSerializer(serializers.ModelSerializer):
    class Meta:
        model = AuditConfiguration
        fields = [
            'log_read_operations', 'log_failed_requests', 'log_admin_actions',
            'log_data_exports', 'log_bulk_operations', 'mask_sensitive_fields',
            'sensitive_field_patterns', 'default_retention_days',
            'security_event_retention_days', 'compliance_retention_days',
            'async_logging', 'batch_size', 'failed_login_threshold',
            'suspicious_activity_threshold', 'gdpr_compliance',
            'hipaa_compliance', 'sox_compliance'
        ]

class AuditStatsSerializer(serializers.Serializer):
    """Serializer for audit statistics"""
    total_logs = serializers.IntegerField()
    logs_today = serializers.IntegerField()
    logs_this_week = serializers.IntegerField()
    logs_this_month = serializers.IntegerField()
    failed_actions = serializers.IntegerField()
    security_events = serializers.IntegerField()
    high_risk_events = serializers.IntegerField()
    unique_users = serializers.IntegerField()
    unique_ips = serializers.IntegerField()
    actions_by_type = serializers.DictField()
    severity_distribution = serializers.DictField()
    top_users = serializers.ListField()
    top_ips = serializers.ListField()
    recent_security_events = SecurityEventSerializer(many=True)

class UserActivityReportSerializer(serializers.Serializer):
    """Serializer for user activity reports"""
    user = serializers.CharField()
    period = serializers.CharField()
    total_actions = serializers.IntegerField()
    actions_by_type = serializers.DictField()
    failed_actions = serializers.IntegerField()
    security_events = serializers.IntegerField()
    logs = AuditLogSerializer(many=True)

class SecuritySummarySerializer(serializers.Serializer):
    """Serializer for security summary reports"""
    period_days = serializers.IntegerField()
    total_security_events = serializers.IntegerField()
    high_risk_events = serializers.IntegerField()
    failed_logins = serializers.IntegerField()
    unique_ips_failed_login = serializers.IntegerField()
    events_by_type = serializers.DictField()
    top_risk_events = SecurityEventSerializer(many=True)

class AuditSearchSerializer(serializers.Serializer):
    """Serializer for audit log search parameters"""
    user_id = serializers.IntegerField(required=False)
    username = serializers.CharField(required=False, max_length=150)
    action = serializers.CharField(required=False, max_length=20)
    severity = serializers.CharField(required=False, max_length=10)
    ip_address = serializers.IPAddressField(required=False)
    start_date = serializers.DateTimeField(required=False)
    end_date = serializers.DateTimeField(required=False)
    success = serializers.BooleanField(required=False)
    content_type = serializers.CharField(required=False, max_length=100)
    search_term = serializers.CharField(required=False, max_length=255)
    compliance_tag = serializers.CharField(required=False, max_length=50)
