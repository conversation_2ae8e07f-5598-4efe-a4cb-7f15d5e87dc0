from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import AuditLogViewSet, SecurityEventViewSet, AuditConfigurationViewSet

router = DefaultRouter()
router.register(r'logs', AuditLogViewSet, basename='audit-log')
router.register(r'security-events', SecurityEventViewSet, basename='security-event')
router.register(r'configuration', AuditConfigurationViewSet, basename='audit-configuration')

urlpatterns = [
    path('api/audit/', include(router.urls)),
]
