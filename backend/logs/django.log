INFO 2025-05-30 10:39:57,559 autoreload 7051 8417369856 Watching for file changes with StatReloader
INFO 2025-05-30 10:41:31,303 autoreload 7051 8417369856 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-05-30 10:41:31,792 autoreload 7064 8417369856 Watching for file changes with StatReloader
INFO 2025-05-30 10:41:50,680 autoreload 7064 8417369856 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-05-30 10:41:51,122 autoreload 7075 8417369856 Watching for file changes with StatReloader
INFO 2025-05-30 10:42:20,393 autoreload 7075 8417369856 /Users/<USER>/Desktop/EMS/backend/ems/urls.py changed, reloading.
INFO 2025-05-30 10:42:20,718 autoreload 7082 8417369856 Watching for file changes with StatReloader
INFO 2025-05-30 10:45:48,393 autoreload 7082 8417369856 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-05-30 10:45:48,904 autoreload 7105 8417369856 Watching for file changes with StatReloader
INFO 2025-05-30 10:45:49,104 server 7105 6184906752 HTTP/2 support not enabled (install the http2 and tls Twisted extras)
INFO 2025-05-30 10:45:49,105 server 7105 6184906752 Configuring endpoint tcp:port=8000:interface=127.0.0.1
INFO 2025-05-30 10:45:49,105 server 7105 6184906752 Listening on TCP address 127.0.0.1:8000
INFO 2025-05-30 10:55:47,986 autoreload 7105 8417369856 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-05-30 10:55:48,486 autoreload 7631 8417369856 Watching for file changes with StatReloader
INFO 2025-05-30 10:55:48,680 server 7631 6149910528 HTTP/2 support not enabled (install the http2 and tls Twisted extras)
INFO 2025-05-30 10:55:48,680 server 7631 6149910528 Configuring endpoint tcp:port=8000:interface=127.0.0.1
INFO 2025-05-30 10:55:48,681 server 7631 6149910528 Listening on TCP address 127.0.0.1:8000
INFO 2025-05-30 11:29:31,484 runserver 7631 6149910528 [mHTTP OPTIONS /api/auth/verify/ 200 [0.01, 127.0.0.1:60496][0m
INFO 2025-05-30 11:29:31,486 runserver 7631 6149910528 [mHTTP OPTIONS /api/auth/verify/ 200 [0.01, 127.0.0.1:60498][0m
WARNING 2025-05-30 11:29:31,508 log 7631 6183563264 Unauthorized: /api/auth/verify/
WARNING 2025-05-30 11:29:31,509 runserver 7631 6149910528 [31;1mHTTP GET /api/auth/verify/ 401 [0.02, 127.0.0.1:60496][0m
WARNING 2025-05-30 11:29:31,512 log 7631 6183563264 Unauthorized: /api/auth/verify/
WARNING 2025-05-30 11:29:31,512 runserver 7631 6149910528 [31;1mHTTP GET /api/auth/verify/ 401 [0.00, 127.0.0.1:60498][0m
INFO 2025-05-30 11:29:35,506 runserver 7631 6149910528 [mHTTP OPTIONS /api/auth/login/ 200 [0.00, 127.0.0.1:60525][0m
WARNING 2025-05-30 11:29:35,731 log 7631 6183563264 Unauthorized: /api/auth/login/
WARNING 2025-05-30 11:29:35,731 runserver 7631 6149910528 [31;1mHTTP POST /api/auth/login/ 401 [0.22, 127.0.0.1:60525][0m
WARNING 2025-05-30 11:29:38,633 log 7631 6183563264 Unauthorized: /api/auth/login/
WARNING 2025-05-30 11:29:38,633 runserver 7631 6149910528 [31;1mHTTP POST /api/auth/login/ 401 [0.22, 127.0.0.1:60525][0m
INFO 2025-05-30 11:29:49,279 autoreload 7631 8417369856 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
