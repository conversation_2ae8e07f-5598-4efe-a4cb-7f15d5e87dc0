from django.contrib import admin
from .models import Notification, NotificationPreference, NotificationTemplate, NotificationLog

@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ['title', 'recipient', 'notification_type', 'priority', 'is_read', 'created_at']
    list_filter = ['notification_type', 'priority', 'is_read', 'created_at']
    search_fields = ['title', 'message', 'recipient__username', 'recipient__email']
    readonly_fields = ['id', 'created_at', 'read_at']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('recipient', 'sender', 'title', 'title_ar', 'message', 'message_ar')
        }),
        ('Classification', {
            'fields': ('notification_type', 'priority')
        }),
        ('Status', {
            'fields': ('is_read', 'is_sent_email', 'is_sent_push', 'read_at')
        }),
        ('Action & Metadata', {
            'fields': ('action_url', 'metadata', 'related_object_type', 'related_object_id')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'expires_at')
        }),
    )

@admin.register(NotificationPreference)
class NotificationPreferenceAdmin(admin.ModelAdmin):
    list_display = ['user', 'email_enabled', 'push_enabled', 'digest_frequency']
    list_filter = ['email_enabled', 'push_enabled', 'digest_frequency']
    search_fields = ['user__username', 'user__email']

@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'notification_type', 'default_priority', 'is_active']
    list_filter = ['notification_type', 'default_priority', 'is_active']
    search_fields = ['name', 'title_template', 'message_template']

@admin.register(NotificationLog)
class NotificationLogAdmin(admin.ModelAdmin):
    list_display = ['notification', 'delivery_method', 'status', 'sent_at', 'created_at']
    list_filter = ['delivery_method', 'status', 'created_at']
    readonly_fields = ['created_at', 'sent_at', 'delivered_at']
    date_hierarchy = 'created_at'
