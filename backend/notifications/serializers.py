from rest_framework import serializers
from django.contrib.auth.models import User
from .models import Notification, NotificationPreference, NotificationTemplate, NotificationLog

class NotificationSerializer(serializers.ModelSerializer):
    sender_name = serializers.CharField(source='sender.get_full_name', read_only=True)
    sender_username = serializers.CharField(source='sender.username', read_only=True)
    time_ago = serializers.SerializerMethodField()
    is_expired = serializers.SerializerMethodField()
    
    class Meta:
        model = Notification
        fields = [
            'id', 'title', 'title_ar', 'message', 'message_ar',
            'notification_type', 'priority', 'is_read', 'action_url',
            'metadata', 'related_object_type', 'related_object_id',
            'sender_name', 'sender_username', 'created_at', 'read_at',
            'expires_at', 'time_ago', 'is_expired'
        ]
        read_only_fields = ['id', 'created_at', 'read_at', 'sender_name', 'sender_username']
    
    def get_time_ago(self, obj):
        """Get human-readable time ago"""
        from django.utils.timesince import timesince
        return timesince(obj.created_at)
    
    def get_is_expired(self, obj):
        """Check if notification is expired"""
        return obj.is_expired()

class NotificationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating notifications"""
    
    class Meta:
        model = Notification
        fields = [
            'recipient', 'title', 'title_ar', 'message', 'message_ar',
            'notification_type', 'priority', 'action_url', 'metadata',
            'related_object_type', 'related_object_id', 'expires_at'
        ]

class NotificationPreferenceSerializer(serializers.ModelSerializer):
    class Meta:
        model = NotificationPreference
        fields = [
            'email_enabled', 'email_employee_updates', 'email_project_updates',
            'email_leave_requests', 'email_task_assignments', 'email_system_alerts',
            'push_enabled', 'push_employee_updates', 'push_project_updates',
            'push_leave_requests', 'push_task_assignments', 'push_system_alerts',
            'digest_frequency', 'quiet_hours_enabled', 'quiet_hours_start',
            'quiet_hours_end'
        ]

class NotificationTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = NotificationTemplate
        fields = [
            'id', 'name', 'notification_type', 'title_template', 'title_template_ar',
            'message_template', 'message_template_ar', 'email_subject_template',
            'email_body_template', 'default_priority', 'send_email', 'send_push',
            'is_active'
        ]

class NotificationLogSerializer(serializers.ModelSerializer):
    notification_title = serializers.CharField(source='notification.title', read_only=True)
    
    class Meta:
        model = NotificationLog
        fields = [
            'id', 'notification_title', 'delivery_method', 'status',
            'error_message', 'sent_at', 'delivered_at', 'provider_id',
            'metadata', 'created_at'
        ]
        read_only_fields = ['id', 'created_at', 'notification_title']

class BulkNotificationSerializer(serializers.Serializer):
    """Serializer for sending bulk notifications"""
    recipient_ids = serializers.ListField(
        child=serializers.IntegerField(),
        help_text="List of user IDs to send notification to"
    )
    title = serializers.CharField(max_length=255)
    title_ar = serializers.CharField(max_length=255, required=False, allow_blank=True)
    message = serializers.CharField()
    message_ar = serializers.CharField(required=False, allow_blank=True)
    notification_type = serializers.CharField(max_length=20, required=False, default='info')
    priority = serializers.CharField(max_length=10, required=False, default='medium')
    action_url = serializers.URLField(required=False, allow_blank=True)
    send_email = serializers.BooleanField(default=False)
    send_push = serializers.BooleanField(default=True)

class NotificationStatsSerializer(serializers.Serializer):
    """Serializer for notification statistics"""
    total_notifications = serializers.IntegerField()
    unread_notifications = serializers.IntegerField()
    notifications_today = serializers.IntegerField()
    notifications_this_week = serializers.IntegerField()
    by_type = serializers.DictField()
    by_priority = serializers.DictField()
    recent_notifications = NotificationSerializer(many=True)
