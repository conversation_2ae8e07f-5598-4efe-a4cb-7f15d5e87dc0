from django.contrib.auth.models import User
from django.utils import timezone
from django.template import Template, Context
from django.core.mail import send_mail
from django.conf import settings
from typing import List, Dict, Any, Optional
import logging

from .models import (
    Notification, NotificationPreference, NotificationTemplate,
    NotificationLog, NotificationType, NotificationPriority
)

logger = logging.getLogger(__name__)

class NotificationService:
    """Service for creating and sending notifications"""
    
    @staticmethod
    def create_notification(
        recipient: User,
        title: str,
        message: str,
        notification_type: str = NotificationType.INFO,
        priority: str = NotificationPriority.MEDIUM,
        sender: Optional[User] = None,
        title_ar: str = "",
        message_ar: str = "",
        action_url: str = "",
        metadata: Dict[str, Any] = None,
        related_object_type: str = "",
        related_object_id: str = "",
        expires_at: Optional[timezone.datetime] = None,
        send_email: bool = False,
        send_push: bool = True
    ) -> Notification:
        """Create a new notification"""
        
        notification = Notification.objects.create(
            recipient=recipient,
            sender=sender,
            title=title,
            title_ar=title_ar,
            message=message,
            message_ar=message_ar,
            notification_type=notification_type,
            priority=priority,
            action_url=action_url,
            metadata=metadata or {},
            related_object_type=related_object_type,
            related_object_id=related_object_id,
            expires_at=expires_at
        )
        
        # Send notification based on user preferences
        NotificationService.send_notification(notification, send_email, send_push)
        
        return notification
    
    @staticmethod
    def create_from_template(
        template_name: str,
        recipient: User,
        context_data: Dict[str, Any],
        sender: Optional[User] = None,
        **kwargs
    ) -> Notification:
        """Create notification from template"""
        
        try:
            template = NotificationTemplate.objects.get(name=template_name, is_active=True)
        except NotificationTemplate.DoesNotExist:
            logger.error(f"Notification template '{template_name}' not found")
            raise ValueError(f"Notification template '{template_name}' not found")
        
        # Render templates with context
        title = Template(template.title_template).render(Context(context_data))
        title_ar = Template(template.title_template_ar).render(Context(context_data)) if template.title_template_ar else ""
        message = Template(template.message_template).render(Context(context_data))
        message_ar = Template(template.message_template_ar).render(Context(context_data)) if template.message_template_ar else ""
        
        return NotificationService.create_notification(
            recipient=recipient,
            sender=sender,
            title=title,
            title_ar=title_ar,
            message=message,
            message_ar=message_ar,
            notification_type=template.notification_type,
            priority=template.default_priority,
            send_email=template.send_email,
            send_push=template.send_push,
            **kwargs
        )
    
    @staticmethod
    def send_notification(notification: Notification, send_email: bool = False, send_push: bool = True):
        """Send notification via various channels"""
        
        # Get user preferences
        preferences, created = NotificationPreference.objects.get_or_create(
            user=notification.recipient
        )
        
        # Check if we're in quiet hours
        if NotificationService._is_quiet_hours(preferences):
            logger.info(f"Skipping notification {notification.id} due to quiet hours")
            return
        
        # Send email if enabled
        if send_email and preferences.email_enabled:
            NotificationService._send_email(notification, preferences)
        
        # Send push notification if enabled
        if send_push and preferences.push_enabled:
            NotificationService._send_push(notification, preferences)
    
    @staticmethod
    def _send_email(notification: Notification, preferences: NotificationPreference):
        """Send email notification"""
        
        # Check type-specific email preferences
        type_enabled = getattr(preferences, f'email_{notification.notification_type}', True)
        if not type_enabled:
            return
        
        try:
            subject = notification.title
            message = notification.message
            
            # Try to get email template
            try:
                template = NotificationTemplate.objects.get(
                    notification_type=notification.notification_type,
                    is_active=True
                )
                if template.email_subject_template:
                    subject = template.email_subject_template
                if template.email_body_template:
                    message = template.email_body_template
            except NotificationTemplate.DoesNotExist:
                pass
            
            # Create log entry
            log = NotificationLog.objects.create(
                notification=notification,
                delivery_method='email',
                status='pending'
            )
            
            # Send email
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[notification.recipient.email],
                fail_silently=False
            )
            
            # Update log and notification
            log.status = 'sent'
            log.sent_at = timezone.now()
            log.save()
            
            notification.is_sent_email = True
            notification.save(update_fields=['is_sent_email'])
            
            logger.info(f"Email sent for notification {notification.id}")
            
        except Exception as e:
            logger.error(f"Failed to send email for notification {notification.id}: {str(e)}")
            log.status = 'failed'
            log.error_message = str(e)
            log.save()
    
    @staticmethod
    def _send_push(notification: Notification, preferences: NotificationPreference):
        """Send push notification (placeholder for future implementation)"""
        
        # Check type-specific push preferences
        type_enabled = getattr(preferences, f'push_{notification.notification_type}', True)
        if not type_enabled:
            return
        
        # Create log entry
        log = NotificationLog.objects.create(
            notification=notification,
            delivery_method='push',
            status='sent',  # For now, assume success
            sent_at=timezone.now()
        )
        
        notification.is_sent_push = True
        notification.save(update_fields=['is_sent_push'])
        
        logger.info(f"Push notification marked as sent for notification {notification.id}")
    
    @staticmethod
    def _is_quiet_hours(preferences: NotificationPreference) -> bool:
        """Check if current time is within user's quiet hours"""
        
        if not preferences.quiet_hours_enabled:
            return False
        
        if not preferences.quiet_hours_start or not preferences.quiet_hours_end:
            return False
        
        now = timezone.now().time()
        start = preferences.quiet_hours_start
        end = preferences.quiet_hours_end
        
        # Handle overnight quiet hours (e.g., 22:00 to 08:00)
        if start > end:
            return now >= start or now <= end
        else:
            return start <= now <= end
    
    @staticmethod
    def bulk_notify(
        recipients: List[User],
        title: str,
        message: str,
        **kwargs
    ) -> List[Notification]:
        """Send notification to multiple users"""
        
        notifications = []
        for recipient in recipients:
            notification = NotificationService.create_notification(
                recipient=recipient,
                title=title,
                message=message,
                **kwargs
            )
            notifications.append(notification)
        
        return notifications
    
    @staticmethod
    def mark_all_read(user: User) -> int:
        """Mark all notifications as read for a user"""
        
        count = Notification.objects.filter(
            recipient=user,
            is_read=False
        ).update(
            is_read=True,
            read_at=timezone.now()
        )
        
        return count
    
    @staticmethod
    def get_unread_count(user: User) -> int:
        """Get count of unread notifications for a user"""
        
        return Notification.objects.filter(
            recipient=user,
            is_read=False
        ).exclude(
            expires_at__lt=timezone.now()
        ).count()
    
    @staticmethod
    def cleanup_expired_notifications():
        """Clean up expired notifications"""
        
        count = Notification.objects.filter(
            expires_at__lt=timezone.now()
        ).delete()[0]
        
        logger.info(f"Cleaned up {count} expired notifications")
        return count
