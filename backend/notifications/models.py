from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import uuid

class NotificationType(models.TextChoices):
    INFO = 'info', 'Information'
    SUCCESS = 'success', 'Success'
    WARNING = 'warning', 'Warning'
    ERROR = 'error', 'Error'
    EMPLOYEE_UPDATE = 'employee_update', 'Employee Update'
    PROJECT_UPDATE = 'project_update', 'Project Update'
    LEAVE_REQUEST = 'leave_request', 'Leave Request'
    TASK_ASSIGNED = 'task_assigned', 'Task Assigned'
    SYSTEM_ALERT = 'system_alert', 'System Alert'

class NotificationPriority(models.TextChoices):
    LOW = 'low', 'Low'
    MEDIUM = 'medium', 'Medium'
    HIGH = 'high', 'High'
    URGENT = 'urgent', 'Urgent'

class Notification(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    sender = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='sent_notifications')
    
    title = models.CharField(max_length=255)
    title_ar = models.CharField(max_length=255, blank=True)
    message = models.TextField()
    message_ar = models.TextField(blank=True)
    
    notification_type = models.CharField(max_length=20, choices=NotificationType.choices, default=NotificationType.INFO)
    priority = models.CharField(max_length=10, choices=NotificationPriority.choices, default=NotificationPriority.MEDIUM)
    
    # Status fields
    is_read = models.BooleanField(default=False)
    is_sent_email = models.BooleanField(default=False)
    is_sent_push = models.BooleanField(default=False)
    
    # Metadata
    action_url = models.URLField(blank=True, help_text="URL to navigate when notification is clicked")
    metadata = models.JSONField(default=dict, blank=True, help_text="Additional data for the notification")
    
    # Related object (generic foreign key)
    related_object_type = models.CharField(max_length=50, blank=True)
    related_object_id = models.CharField(max_length=50, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    read_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True, help_text="When this notification expires")
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', '-created_at']),
            models.Index(fields=['recipient', 'is_read']),
            models.Index(fields=['notification_type']),
            models.Index(fields=['priority']),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.recipient.username}"
    
    def mark_as_read(self):
        """Mark notification as read"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])
    
    def is_expired(self):
        """Check if notification is expired"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

class NotificationPreference(models.Model):
    """User preferences for notifications"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='notification_preferences')
    
    # Email preferences
    email_enabled = models.BooleanField(default=True)
    email_employee_updates = models.BooleanField(default=True)
    email_project_updates = models.BooleanField(default=True)
    email_leave_requests = models.BooleanField(default=True)
    email_task_assignments = models.BooleanField(default=True)
    email_system_alerts = models.BooleanField(default=True)
    
    # Push notification preferences
    push_enabled = models.BooleanField(default=True)
    push_employee_updates = models.BooleanField(default=True)
    push_project_updates = models.BooleanField(default=True)
    push_leave_requests = models.BooleanField(default=True)
    push_task_assignments = models.BooleanField(default=True)
    push_system_alerts = models.BooleanField(default=True)
    
    # Frequency settings
    digest_frequency = models.CharField(
        max_length=20,
        choices=[
            ('immediate', 'Immediate'),
            ('hourly', 'Hourly'),
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('never', 'Never'),
        ],
        default='immediate'
    )
    
    # Quiet hours
    quiet_hours_enabled = models.BooleanField(default=False)
    quiet_hours_start = models.TimeField(null=True, blank=True, default='22:00')
    quiet_hours_end = models.TimeField(null=True, blank=True, default='08:00')
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Notification preferences for {self.user.username}"

class NotificationTemplate(models.Model):
    """Templates for different types of notifications"""
    name = models.CharField(max_length=100, unique=True)
    notification_type = models.CharField(max_length=20, choices=NotificationType.choices)
    
    # Template content
    title_template = models.CharField(max_length=255, help_text="Use {variable} for dynamic content")
    title_template_ar = models.CharField(max_length=255, blank=True)
    message_template = models.TextField(help_text="Use {variable} for dynamic content")
    message_template_ar = models.TextField(blank=True)
    
    # Email template (optional)
    email_subject_template = models.CharField(max_length=255, blank=True)
    email_body_template = models.TextField(blank=True)
    
    # Default settings
    default_priority = models.CharField(max_length=10, choices=NotificationPriority.choices, default=NotificationPriority.MEDIUM)
    send_email = models.BooleanField(default=False)
    send_push = models.BooleanField(default=True)
    
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.notification_type})"

class NotificationLog(models.Model):
    """Log of all notification sending attempts"""
    notification = models.ForeignKey(Notification, on_delete=models.CASCADE, related_name='logs')
    
    delivery_method = models.CharField(
        max_length=20,
        choices=[
            ('in_app', 'In-App'),
            ('email', 'Email'),
            ('push', 'Push'),
            ('sms', 'SMS'),
        ]
    )
    
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('sent', 'Sent'),
            ('delivered', 'Delivered'),
            ('failed', 'Failed'),
            ('bounced', 'Bounced'),
        ],
        default='pending'
    )
    
    error_message = models.TextField(blank=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    
    # Metadata for tracking
    provider_id = models.CharField(max_length=255, blank=True, help_text="ID from email/push provider")
    metadata = models.JSONField(default=dict, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.delivery_method} - {self.status} - {self.notification.title}"
