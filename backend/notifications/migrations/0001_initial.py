# Generated by Django 4.2.7 on 2025-07-14 16:23

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255)),
                ('title_ar', models.CharField(blank=True, max_length=255)),
                ('message', models.TextField()),
                ('message_ar', models.TextField(blank=True)),
                ('notification_type', models.CharField(choices=[('info', 'Information'), ('success', 'Success'), ('warning', 'Warning'), ('error', 'Error'), ('employee_update', 'Employee Update'), ('project_update', 'Project Update'), ('leave_request', 'Leave Request'), ('task_assigned', 'Task Assigned'), ('system_alert', 'System Alert')], default='info', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10)),
                ('is_read', models.BooleanField(default=False)),
                ('is_sent_email', models.BooleanField(default=False)),
                ('is_sent_push', models.BooleanField(default=False)),
                ('action_url', models.URLField(blank=True, help_text='URL to navigate when notification is clicked')),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='Additional data for the notification')),
                ('related_object_type', models.CharField(blank=True, max_length=50)),
                ('related_object_id', models.CharField(blank=True, max_length=50)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('expires_at', models.DateTimeField(blank=True, help_text='When this notification expires', null=True)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='NotificationTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('notification_type', models.CharField(choices=[('info', 'Information'), ('success', 'Success'), ('warning', 'Warning'), ('error', 'Error'), ('employee_update', 'Employee Update'), ('project_update', 'Project Update'), ('leave_request', 'Leave Request'), ('task_assigned', 'Task Assigned'), ('system_alert', 'System Alert')], max_length=20)),
                ('title_template', models.CharField(help_text='Use {variable} for dynamic content', max_length=255)),
                ('title_template_ar', models.CharField(blank=True, max_length=255)),
                ('message_template', models.TextField(help_text='Use {variable} for dynamic content')),
                ('message_template_ar', models.TextField(blank=True)),
                ('email_subject_template', models.CharField(blank=True, max_length=255)),
                ('email_body_template', models.TextField(blank=True)),
                ('default_priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10)),
                ('send_email', models.BooleanField(default=False)),
                ('send_push', models.BooleanField(default=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='NotificationPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_enabled', models.BooleanField(default=True)),
                ('email_employee_updates', models.BooleanField(default=True)),
                ('email_project_updates', models.BooleanField(default=True)),
                ('email_leave_requests', models.BooleanField(default=True)),
                ('email_task_assignments', models.BooleanField(default=True)),
                ('email_system_alerts', models.BooleanField(default=True)),
                ('push_enabled', models.BooleanField(default=True)),
                ('push_employee_updates', models.BooleanField(default=True)),
                ('push_project_updates', models.BooleanField(default=True)),
                ('push_leave_requests', models.BooleanField(default=True)),
                ('push_task_assignments', models.BooleanField(default=True)),
                ('push_system_alerts', models.BooleanField(default=True)),
                ('digest_frequency', models.CharField(choices=[('immediate', 'Immediate'), ('hourly', 'Hourly'), ('daily', 'Daily'), ('weekly', 'Weekly'), ('never', 'Never')], default='immediate', max_length=20)),
                ('quiet_hours_enabled', models.BooleanField(default=False)),
                ('quiet_hours_start', models.TimeField(blank=True, default='22:00', null=True)),
                ('quiet_hours_end', models.TimeField(blank=True, default='08:00', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='NotificationLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('delivery_method', models.CharField(choices=[('in_app', 'In-App'), ('email', 'Email'), ('push', 'Push'), ('sms', 'SMS')], max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('delivered', 'Delivered'), ('failed', 'Failed'), ('bounced', 'Bounced')], default='pending', max_length=20)),
                ('error_message', models.TextField(blank=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('provider_id', models.CharField(blank=True, help_text='ID from email/push provider', max_length=255)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('notification', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='notifications.notification')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='notification',
            index=models.Index(fields=['recipient', '-created_at'], name='notificatio_recipie_a972ce_idx'),
        ),
        migrations.AddIndex(
            model_name='notification',
            index=models.Index(fields=['recipient', 'is_read'], name='notificatio_recipie_4e3567_idx'),
        ),
        migrations.AddIndex(
            model_name='notification',
            index=models.Index(fields=['notification_type'], name='notificatio_notific_f2898f_idx'),
        ),
        migrations.AddIndex(
            model_name='notification',
            index=models.Index(fields=['priority'], name='notificatio_priorit_bf8ea0_idx'),
        ),
    ]
