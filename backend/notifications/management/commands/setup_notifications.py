from django.core.management.base import BaseCommand
from notifications.models import NotificationTemplate, NotificationType, NotificationPriority

class Command(BaseCommand):
    help = 'Set up initial notification templates'

    def handle(self, *args, **options):
        templates = [
            {
                'name': 'employee_created',
                'notification_type': NotificationType.EMPLOYEE_UPDATE,
                'title_template': 'New Employee: {employee_name}',
                'title_template_ar': 'موظف جديد: {employee_name_ar}',
                'message_template': 'A new employee {employee_name} has been added to the {department} department.',
                'message_template_ar': 'تم إضافة موظف جديد {employee_name_ar} إلى قسم {department_ar}.',
                'email_subject_template': 'New Employee Added - {employee_name}',
                'email_body_template': 'Hello,\n\nA new employee {employee_name} has been added to the system.\n\nEmployee Details:\n- Name: {employee_name}\n- Department: {department}\n- Position: {position}\n- Start Date: {hire_date}\n\nBest regards,\nEMS System',
                'default_priority': NotificationPriority.MEDIUM,
                'send_email': True,
                'send_push': True
            },
            {
                'name': 'project_created',
                'notification_type': NotificationType.PROJECT_UPDATE,
                'title_template': 'New Project: {project_name}',
                'title_template_ar': 'مشروع جديد: {project_name_ar}',
                'message_template': 'A new project "{project_name}" has been created.',
                'message_template_ar': 'تم إنشاء مشروع جديد "{project_name_ar}".',
                'email_subject_template': 'New Project Created - {project_name}',
                'email_body_template': 'Hello,\n\nA new project "{project_name}" has been created.\n\nProject Details:\n- Name: {project_name}\n- Start Date: {start_date}\n- End Date: {end_date}\n- Status: {status}\n\nBest regards,\nEMS System',
                'default_priority': NotificationPriority.MEDIUM,
                'send_email': True,
                'send_push': True
            },
            {
                'name': 'leave_request_submitted',
                'notification_type': NotificationType.LEAVE_REQUEST,
                'title_template': 'Leave Request: {employee_name}',
                'title_template_ar': 'طلب إجازة: {employee_name_ar}',
                'message_template': '{employee_name} has submitted a leave request from {start_date} to {end_date}.',
                'message_template_ar': 'قدم {employee_name_ar} طلب إجازة من {start_date} إلى {end_date}.',
                'email_subject_template': 'Leave Request Submitted - {employee_name}',
                'email_body_template': 'Hello,\n\n{employee_name} has submitted a leave request.\n\nLeave Details:\n- Employee: {employee_name}\n- Start Date: {start_date}\n- End Date: {end_date}\n- Duration: {duration} days\n- Reason: {reason}\n\nPlease review and approve/reject this request.\n\nBest regards,\nEMS System',
                'default_priority': NotificationPriority.HIGH,
                'send_email': True,
                'send_push': True
            },
            {
                'name': 'leave_request_approved',
                'notification_type': NotificationType.LEAVE_REQUEST,
                'title_template': 'Leave Request Approved',
                'title_template_ar': 'تم الموافقة على طلب الإجازة',
                'message_template': 'Your leave request from {start_date} to {end_date} has been approved.',
                'message_template_ar': 'تم الموافقة على طلب إجازتك من {start_date} إلى {end_date}.',
                'email_subject_template': 'Leave Request Approved',
                'email_body_template': 'Hello {employee_name},\n\nYour leave request has been approved.\n\nLeave Details:\n- Start Date: {start_date}\n- End Date: {end_date}\n- Duration: {duration} days\n\nEnjoy your time off!\n\nBest regards,\nEMS System',
                'default_priority': NotificationPriority.HIGH,
                'send_email': True,
                'send_push': True
            },
            {
                'name': 'leave_request_rejected',
                'notification_type': NotificationType.LEAVE_REQUEST,
                'title_template': 'Leave Request Rejected',
                'title_template_ar': 'تم رفض طلب الإجازة',
                'message_template': 'Your leave request from {start_date} to {end_date} has been rejected.',
                'message_template_ar': 'تم رفض طلب إجازتك من {start_date} إلى {end_date}.',
                'email_subject_template': 'Leave Request Rejected',
                'email_body_template': 'Hello {employee_name},\n\nUnfortunately, your leave request has been rejected.\n\nLeave Details:\n- Start Date: {start_date}\n- End Date: {end_date}\n- Duration: {duration} days\n- Rejection Reason: {rejection_reason}\n\nPlease contact HR for more information.\n\nBest regards,\nEMS System',
                'default_priority': NotificationPriority.HIGH,
                'send_email': True,
                'send_push': True
            },
            {
                'name': 'task_assigned',
                'notification_type': NotificationType.TASK_ASSIGNED,
                'title_template': 'New Task Assigned: {task_name}',
                'title_template_ar': 'مهمة جديدة: {task_name_ar}',
                'message_template': 'You have been assigned a new task: {task_name}',
                'message_template_ar': 'تم تكليفك بمهمة جديدة: {task_name_ar}',
                'email_subject_template': 'New Task Assigned - {task_name}',
                'email_body_template': 'Hello {assignee_name},\n\nYou have been assigned a new task.\n\nTask Details:\n- Name: {task_name}\n- Project: {project_name}\n- Due Date: {due_date}\n- Priority: {priority}\n- Description: {description}\n\nBest regards,\nEMS System',
                'default_priority': NotificationPriority.MEDIUM,
                'send_email': True,
                'send_push': True
            },
            {
                'name': 'welcome_user',
                'notification_type': NotificationType.SYSTEM_ALERT,
                'title_template': 'Welcome to EMS!',
                'title_template_ar': 'مرحباً بك في نظام إدارة المؤسسة!',
                'message_template': 'Welcome to the Enterprise Management System. You can now access all features available to your role.',
                'message_template_ar': 'مرحباً بك في نظام إدارة المؤسسة. يمكنك الآن الوصول إلى جميع الميزات المتاحة لدورك.',
                'email_subject_template': 'Welcome to EMS - {user_name}',
                'email_body_template': 'Hello {user_name},\n\nWelcome to the Enterprise Management System!\n\nYour account has been created and you can now access the system with your credentials.\n\nRole: {user_role}\nLogin URL: {login_url}\n\nIf you have any questions, please contact your administrator.\n\nBest regards,\nEMS Team',
                'default_priority': NotificationPriority.MEDIUM,
                'send_email': True,
                'send_push': True
            }
        ]

        created_count = 0
        updated_count = 0

        for template_data in templates:
            template, created = NotificationTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults=template_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created template: {template.name}')
                )
            else:
                # Update existing template
                for key, value in template_data.items():
                    setattr(template, key, value)
                template.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated template: {template.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully set up notification templates: '
                f'{created_count} created, {updated_count} updated'
            )
        )
