from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth.models import User
from ems.models import Employee, Project, LeaveRequest
from .services import NotificationService
from .models import NotificationType, NotificationPriority

@receiver(post_save, sender=Employee)
def employee_created_notification(sender, instance, created, **kwargs):
    """Send notification when employee is created or updated"""
    if created:
        # Notify HR managers about new employee
        hr_users = User.objects.filter(
            groups__name__in=['HR Manager', 'Admin', 'Super Admin']
        )
        
        for user in hr_users:
            NotificationService.create_notification(
                recipient=user,
                title=f"New Employee Added: {instance.firstName} {instance.lastName}",
                title_ar=f"موظف جديد: {instance.firstNameAr or instance.firstName} {instance.lastNameAr or instance.lastName}",
                message=f"A new employee {instance.firstName} {instance.lastName} has been added to the system.",
                message_ar=f"تم إضافة موظف جديد {instance.firstNameAr or instance.firstName} {instance.lastNameAr or instance.lastName} إلى النظام.",
                notification_type=NotificationType.EMPLOYEE_UPDATE,
                priority=NotificationPriority.MEDIUM,
                action_url=f"/admin/employees/{instance.id}",
                related_object_type="employee",
                related_object_id=str(instance.id),
                send_email=True
            )

@receiver(post_save, sender=Project)
def project_created_notification(sender, instance, created, **kwargs):
    """Send notification when project is created or updated"""
    if created:
        # Notify project managers and admins
        admin_users = User.objects.filter(
            groups__name__in=['Admin', 'Super Admin', 'Project Manager']
        )
        
        for user in admin_users:
            NotificationService.create_notification(
                recipient=user,
                title=f"New Project Created: {instance.name}",
                title_ar=f"مشروع جديد: {instance.name_ar or instance.name}",
                message=f"A new project '{instance.name}' has been created.",
                message_ar=f"تم إنشاء مشروع جديد '{instance.name_ar or instance.name}'.",
                notification_type=NotificationType.PROJECT_UPDATE,
                priority=NotificationPriority.MEDIUM,
                action_url=f"/admin/projects/{instance.id}",
                related_object_type="project",
                related_object_id=str(instance.id),
                send_email=True
            )

@receiver(post_save, sender=LeaveRequest)
def leave_request_notification(sender, instance, created, **kwargs):
    """Send notification when leave request is created or updated"""
    if created:
        # Notify HR managers about new leave request
        hr_users = User.objects.filter(
            groups__name__in=['HR Manager', 'Admin', 'Super Admin']
        )
        
        for user in hr_users:
            NotificationService.create_notification(
                recipient=user,
                title=f"New Leave Request: {instance.employee.firstName} {instance.employee.lastName}",
                title_ar=f"طلب إجازة جديد: {instance.employee.firstNameAr or instance.employee.firstName} {instance.employee.lastNameAr or instance.employee.lastName}",
                message=f"{instance.employee.firstName} {instance.employee.lastName} has submitted a leave request from {instance.start_date} to {instance.end_date}.",
                message_ar=f"قدم {instance.employee.firstNameAr or instance.employee.firstName} {instance.employee.lastNameAr or instance.employee.lastName} طلب إجازة من {instance.start_date} إلى {instance.end_date}.",
                notification_type=NotificationType.LEAVE_REQUEST,
                priority=NotificationPriority.HIGH,
                action_url=f"/admin/hr/leave/{instance.id}",
                related_object_type="leave_request",
                related_object_id=str(instance.id),
                send_email=True
            )
        
        # Notify the employee that their request was submitted
        if hasattr(instance.employee, 'user') and instance.employee.user:
            NotificationService.create_notification(
                recipient=instance.employee.user,
                title="Leave Request Submitted",
                title_ar="تم تقديم طلب الإجازة",
                message=f"Your leave request from {instance.start_date} to {instance.end_date} has been submitted and is pending approval.",
                message_ar=f"تم تقديم طلب إجازتك من {instance.start_date} إلى {instance.end_date} وهو في انتظار الموافقة.",
                notification_type=NotificationType.LEAVE_REQUEST,
                priority=NotificationPriority.MEDIUM,
                action_url=f"/employee/leave/{instance.id}",
                related_object_type="leave_request",
                related_object_id=str(instance.id)
            )
    
    elif hasattr(instance, '_state') and instance._state.adding is False:
        # Leave request was updated (possibly approved/rejected)
        if hasattr(instance.employee, 'user') and instance.employee.user:
            status_text = instance.status.replace('_', ' ').title()
            status_text_ar = {
                'pending': 'في الانتظار',
                'approved': 'موافق عليه',
                'rejected': 'مرفوض'
            }.get(instance.status, instance.status)
            
            NotificationService.create_notification(
                recipient=instance.employee.user,
                title=f"Leave Request {status_text}",
                title_ar=f"طلب الإجازة {status_text_ar}",
                message=f"Your leave request from {instance.start_date} to {instance.end_date} has been {status_text.lower()}.",
                message_ar=f"طلب إجازتك من {instance.start_date} إلى {instance.end_date} تم {status_text_ar}.",
                notification_type=NotificationType.LEAVE_REQUEST,
                priority=NotificationPriority.HIGH if instance.status in ['approved', 'rejected'] else NotificationPriority.MEDIUM,
                action_url=f"/employee/leave/{instance.id}",
                related_object_type="leave_request",
                related_object_id=str(instance.id),
                send_email=True
            )

@receiver(post_save, sender=User)
def user_created_notification(sender, instance, created, **kwargs):
    """Send welcome notification to new users"""
    if created:
        NotificationService.create_notification(
            recipient=instance,
            title="Welcome to EMS!",
            title_ar="مرحباً بك في نظام إدارة المؤسسة!",
            message="Welcome to the Enterprise Management System. You can now access all the features available to your role.",
            message_ar="مرحباً بك في نظام إدارة المؤسسة. يمكنك الآن الوصول إلى جميع الميزات المتاحة لدورك.",
            notification_type=NotificationType.SYSTEM_ALERT,
            priority=NotificationPriority.MEDIUM,
            action_url="/dashboard",
            send_email=True
        )
