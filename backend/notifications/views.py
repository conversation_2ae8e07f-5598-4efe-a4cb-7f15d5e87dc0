from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth.models import User
from django.utils import timezone
from django.db.models import Q, Count
from datetime import timedelta

from .models import Notification, NotificationPreference, NotificationTemplate, NotificationLog
from .serializers import (
    NotificationSerializer, NotificationCreateSerializer, NotificationPreferenceSerializer,
    NotificationTemplateSerializer, NotificationLogSerializer, BulkNotificationSerializer,
    NotificationStatsSerializer
)
from .services import NotificationService

class NotificationViewSet(viewsets.ModelViewSet):
    """ViewSet for managing notifications"""
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Get notifications for the current user"""
        user = self.request.user
        queryset = Notification.objects.filter(recipient=user)
        
        # Filter by read status
        is_read = self.request.query_params.get('is_read')
        if is_read is not None:
            queryset = queryset.filter(is_read=is_read.lower() == 'true')
        
        # Filter by type
        notification_type = self.request.query_params.get('type')
        if notification_type:
            queryset = queryset.filter(notification_type=notification_type)
        
        # Filter by priority
        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)
        
        # Exclude expired notifications
        exclude_expired = self.request.query_params.get('exclude_expired', 'true')
        if exclude_expired.lower() == 'true':
            queryset = queryset.exclude(expires_at__lt=timezone.now())
        
        return queryset.order_by('-created_at')
    
    def get_serializer_class(self):
        if self.action == 'create':
            return NotificationCreateSerializer
        return NotificationSerializer

    def create(self, request, *args, **kwargs):
        """Create a new notification"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        notification = serializer.save()

        # Return the full notification data
        response_serializer = NotificationSerializer(notification)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)
    
    @action(detail=True, methods=['post'])
    def mark_read(self, request, pk=None):
        """Mark a notification as read"""
        notification = self.get_object()
        notification.mark_as_read()
        return Response({'status': 'marked as read'})
    
    @action(detail=False, methods=['post'])
    def mark_all_read(self, request):
        """Mark all notifications as read for the current user"""
        count = NotificationService.mark_all_read(request.user)
        return Response({'status': f'{count} notifications marked as read'})
    
    @action(detail=False, methods=['get'])
    def unread_count(self, request):
        """Get count of unread notifications"""
        count = NotificationService.get_unread_count(request.user)
        return Response({'unread_count': count})
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get notification statistics for the current user"""
        user = request.user
        now = timezone.now()
        today = now.date()
        week_ago = now - timedelta(days=7)
        
        # Basic counts
        total_notifications = Notification.objects.filter(recipient=user).count()
        unread_notifications = NotificationService.get_unread_count(user)
        notifications_today = Notification.objects.filter(
            recipient=user,
            created_at__date=today
        ).count()
        notifications_this_week = Notification.objects.filter(
            recipient=user,
            created_at__gte=week_ago
        ).count()
        
        # Group by type
        by_type = dict(
            Notification.objects.filter(recipient=user)
            .values('notification_type')
            .annotate(count=Count('id'))
            .values_list('notification_type', 'count')
        )
        
        # Group by priority
        by_priority = dict(
            Notification.objects.filter(recipient=user)
            .values('priority')
            .annotate(count=Count('id'))
            .values_list('priority', 'count')
        )
        
        # Recent notifications
        recent_notifications = Notification.objects.filter(
            recipient=user
        ).order_by('-created_at')[:5]
        
        stats_data = {
            'total_notifications': total_notifications,
            'unread_notifications': unread_notifications,
            'notifications_today': notifications_today,
            'notifications_this_week': notifications_this_week,
            'by_type': by_type,
            'by_priority': by_priority,
            'recent_notifications': recent_notifications
        }
        
        serializer = NotificationStatsSerializer(stats_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def send_bulk(self, request):
        """Send notification to multiple users"""
        serializer = BulkNotificationSerializer(data=request.data)
        if serializer.is_valid():
            data = serializer.validated_data
            
            # Get recipients
            recipients = User.objects.filter(id__in=data['recipient_ids'])
            if not recipients.exists():
                return Response(
                    {'error': 'No valid recipients found'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Send notifications
            notifications = NotificationService.bulk_notify(
                recipients=list(recipients),
                title=data['title'],
                message=data['message'],
                title_ar=data.get('title_ar', ''),
                message_ar=data.get('message_ar', ''),
                notification_type=data.get('notification_type', 'info'),
                priority=data.get('priority', 'medium'),
                action_url=data.get('action_url', ''),
                sender=request.user,
                send_email=data.get('send_email', False),
                send_push=data.get('send_push', True)
            )
            
            return Response({
                'status': f'Sent {len(notifications)} notifications',
                'notification_ids': [str(n.id) for n in notifications]
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class NotificationPreferenceViewSet(viewsets.ModelViewSet):
    """ViewSet for managing notification preferences"""
    serializer_class = NotificationPreferenceSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return NotificationPreference.objects.filter(user=self.request.user)
    
    def get_object(self):
        """Get or create notification preferences for the current user"""
        preferences, created = NotificationPreference.objects.get_or_create(
            user=self.request.user
        )
        return preferences
    
    @action(detail=False, methods=['get', 'put', 'patch'])
    def my_preferences(self, request):
        """Get or update current user's notification preferences"""
        preferences = self.get_object()
        
        if request.method == 'GET':
            serializer = self.get_serializer(preferences)
            return Response(serializer.data)
        
        elif request.method in ['PUT', 'PATCH']:
            partial = request.method == 'PATCH'
            serializer = self.get_serializer(preferences, data=request.data, partial=partial)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class NotificationTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for managing notification templates (admin only)"""
    queryset = NotificationTemplate.objects.all()
    serializer_class = NotificationTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_permissions(self):
        """Only allow admin users to modify templates"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [permissions.IsAdminUser]
        else:
            permission_classes = [permissions.IsAuthenticated]
        
        return [permission() for permission in permission_classes]

class NotificationLogViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for viewing notification logs (admin only)"""
    queryset = NotificationLog.objects.all()
    serializer_class = NotificationLogSerializer
    permission_classes = [permissions.IsAdminUser]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by notification ID
        notification_id = self.request.query_params.get('notification_id')
        if notification_id:
            queryset = queryset.filter(notification_id=notification_id)
        
        # Filter by delivery method
        delivery_method = self.request.query_params.get('delivery_method')
        if delivery_method:
            queryset = queryset.filter(delivery_method=delivery_method)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset.order_by('-created_at')
