from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid
import json

class Department(models.Model):
    name = models.CharField(max_length=100)
    name_ar = models.CharField(max_length=100, help_text="Arabic name")
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True, help_text="Arabic description")
    manager = models.ForeignKey('Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_departments')
    budget_amount = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    location = models.CharField(max_length=200, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']

# User Roles and Permissions
class Role(models.Model):
    ROLE_TYPES = [
        ('SUPERADMIN', 'Super Administrator'),
        ('ADMIN', 'Administrator'),
        ('HR_MANAGER', 'HR Manager'),
        ('DEPARTMENT_MANAGER', 'Department Manager'),
        ('PROJECT_MANAGER', 'Project Manager'),
        ('FINANCE_MANAGER', 'Finance Manager'),
        ('EMPLOYEE', 'Employee'),
        ('INTERN', 'Intern'),
    ]

    name = models.CharField(max_length=50, choices=ROLE_TYPES, unique=True)
    name_ar = models.CharField(max_length=50)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    permissions = models.JSONField(default=dict)  # Store permissions as JSON
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.get_name_display()

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True, blank=True)
    avatar = models.ImageField(upload_to='avatars/', null=True, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    date_of_birth = models.DateField(null=True, blank=True)
    emergency_contact_name = models.CharField(max_length=100, blank=True)
    emergency_contact_phone = models.CharField(max_length=20, blank=True)
    preferred_language = models.CharField(max_length=2, choices=[('ar', 'Arabic'), ('en', 'English')], default='ar')
    timezone = models.CharField(max_length=50, default='Asia/Riyadh')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.role}"

class Employee(models.Model):
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
    ]

    EMPLOYMENT_STATUS = [
        ('FULL_TIME', 'Full Time'),
        ('PART_TIME', 'Part Time'),
        ('CONTRACT', 'Contract'),
        ('INTERN', 'Intern'),
        ('CONSULTANT', 'Consultant'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE)
    employee_id = models.CharField(max_length=20, unique=True)
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    position = models.CharField(max_length=100)
    position_ar = models.CharField(max_length=100, help_text="Arabic position")
    # Arabic name fields
    first_name_ar = models.CharField(max_length=150, blank=True, help_text="Arabic first name")
    last_name_ar = models.CharField(max_length=150, blank=True, help_text="Arabic last name")
    phone = models.CharField(max_length=20, blank=True)
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES)
    hire_date = models.DateField()
    salary = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    employment_status = models.CharField(max_length=20, choices=EMPLOYMENT_STATUS, default='FULL_TIME')
    manager = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='subordinates')
    work_location = models.CharField(max_length=200, blank=True)
    contract_end_date = models.DateField(null=True, blank=True)
    probation_end_date = models.DateField(null=True, blank=True)
    national_id = models.CharField(max_length=20, blank=True)
    passport_number = models.CharField(max_length=20, blank=True)
    bank_account = models.CharField(max_length=50, blank=True)
    emergency_contact = models.CharField(max_length=100, blank=True)
    emergency_phone = models.CharField(max_length=20, blank=True)
    skills = models.TextField(blank=True)
    education = models.TextField(blank=True)
    certifications = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.employee_id} - {self.user.get_full_name()}"

    class Meta:
        ordering = ['employee_id']

# Leave Management Models
class LeaveType(models.Model):
    name = models.CharField(max_length=50)
    name_ar = models.CharField(max_length=50)
    days_allowed = models.IntegerField()
    is_paid = models.BooleanField(default=True)
    requires_approval = models.BooleanField(default=True)
    carry_forward = models.BooleanField(default=False)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

class LeaveRequest(models.Model):
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('CANCELLED', 'Cancelled'),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE)
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE)
    start_date = models.DateField()
    end_date = models.DateField()
    days_requested = models.IntegerField()
    reason = models.TextField()
    reason_ar = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_leaves')
    approval_date = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True)
    rejection_reason_ar = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.employee} - {self.leave_type} ({self.start_date} to {self.end_date})"

class Attendance(models.Model):
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE)
    date = models.DateField()
    check_in = models.TimeField(null=True, blank=True)
    check_out = models.TimeField(null=True, blank=True)
    break_start = models.TimeField(null=True, blank=True)
    break_end = models.TimeField(null=True, blank=True)
    total_hours = models.DecimalField(max_digits=4, decimal_places=2, null=True, blank=True)
    overtime_hours = models.DecimalField(max_digits=4, decimal_places=2, default=0)
    is_present = models.BooleanField(default=True)
    is_late = models.BooleanField(default=False)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['employee', 'date']
        ordering = ['-date']

    def __str__(self):
        return f"{self.employee} - {self.date}"

# Project Management Models
class Project(models.Model):
    STATUS_CHOICES = [
        ('PLANNING', 'Planning'),
        ('IN_PROGRESS', 'In Progress'),
        ('ON_HOLD', 'On Hold'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]

    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]

    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    project_manager = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, related_name='managed_projects')
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    team_members = models.ManyToManyField(Employee, related_name='projects', blank=True)
    client = models.CharField(max_length=200, blank=True)
    budget_amount = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    start_date = models.DateField()
    end_date = models.DateField()
    actual_start_date = models.DateField(null=True, blank=True)
    actual_end_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PLANNING')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='MEDIUM')
    progress_percentage = models.IntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class Task(models.Model):
    STATUS_CHOICES = [
        ('TODO', 'To Do'),
        ('IN_PROGRESS', 'In Progress'),
        ('REVIEW', 'Under Review'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    ]

    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('URGENT', 'Urgent'),
    ]

    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='tasks')
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    assigned_to = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, related_name='assigned_tasks')
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='created_tasks')
    due_date = models.DateTimeField()
    start_date = models.DateTimeField(null=True, blank=True)
    completion_date = models.DateTimeField(null=True, blank=True)
    estimated_hours = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)
    actual_hours = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='TODO')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='MEDIUM')
    progress_percentage = models.IntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])
    dependencies = models.ManyToManyField('self', blank=True, symmetrical=False)
    tags = models.CharField(max_length=500, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.project.name} - {self.title}"

# Financial Management Models
class Budget(models.Model):
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True, related_name='budgets')
    project = models.ForeignKey(Project, on_delete=models.CASCADE, null=True, blank=True, related_name='budgets')
    fiscal_year = models.IntegerField()
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    allocated_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    spent_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    remaining_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    start_date = models.DateField()
    end_date = models.DateField()
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.fiscal_year}"

class Expense(models.Model):
    CATEGORY_CHOICES = [
        ('TRAVEL', 'Travel'),
        ('OFFICE_SUPPLIES', 'Office Supplies'),
        ('EQUIPMENT', 'Equipment'),
        ('SOFTWARE', 'Software'),
        ('TRAINING', 'Training'),
        ('MARKETING', 'Marketing'),
        ('UTILITIES', 'Utilities'),
        ('RENT', 'Rent'),
        ('OTHER', 'Other'),
    ]

    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('PAID', 'Paid'),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE)
    budget = models.ForeignKey(Budget, on_delete=models.SET_NULL, null=True, blank=True)
    project = models.ForeignKey(Project, on_delete=models.SET_NULL, null=True, blank=True)
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='SAR')
    expense_date = models.DateField()
    receipt_file = models.FileField(upload_to='receipts/', null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_expenses')
    approval_date = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} - {self.amount} {self.currency}"

# Inventory & Asset Management Models
class AssetCategory(models.Model):
    name = models.CharField(max_length=100)
    name_ar = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Asset Categories"

class Asset(models.Model):
    STATUS_CHOICES = [
        ('AVAILABLE', 'Available'),
        ('IN_USE', 'In Use'),
        ('MAINTENANCE', 'Under Maintenance'),
        ('RETIRED', 'Retired'),
        ('LOST', 'Lost'),
        ('DAMAGED', 'Damaged'),
    ]

    asset_id = models.CharField(max_length=50, unique=True)
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    category = models.ForeignKey(AssetCategory, on_delete=models.CASCADE)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    serial_number = models.CharField(max_length=100, blank=True)
    model = models.CharField(max_length=100, blank=True)
    manufacturer = models.CharField(max_length=100, blank=True)
    purchase_date = models.DateField()
    purchase_price = models.DecimalField(max_digits=10, decimal_places=2)
    current_value = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    warranty_expiry = models.DateField(null=True, blank=True)
    assigned_to = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    location = models.CharField(max_length=200, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='AVAILABLE')
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.asset_id} - {self.name}"

class Supplier(models.Model):
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    contact_person = models.CharField(max_length=100, blank=True)
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    website = models.URLField(blank=True)
    tax_number = models.CharField(max_length=50, blank=True)
    payment_terms = models.CharField(max_length=100, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class PurchaseOrder(models.Model):
    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('PENDING', 'Pending Approval'),
        ('APPROVED', 'Approved'),
        ('ORDERED', 'Ordered'),
        ('RECEIVED', 'Received'),
        ('CANCELLED', 'Cancelled'),
    ]

    po_number = models.CharField(max_length=50, unique=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE)
    requested_by = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='requested_pos')
    approved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_pos')
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    project = models.ForeignKey(Project, on_delete=models.SET_NULL, null=True, blank=True)
    order_date = models.DateField()
    expected_delivery = models.DateField(null=True, blank=True)
    actual_delivery = models.DateField(null=True, blank=True)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    currency = models.CharField(max_length=3, default='SAR')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='DRAFT')
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"PO-{self.po_number} - {self.supplier.name}"

# Product Management Models
class ProductCategory(models.Model):
    name = models.CharField(max_length=100)
    name_ar = models.CharField(max_length=100, blank=True)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Product Categories"

class Product(models.Model):
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('discontinued', 'Discontinued'),
    ]

    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    sku = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    category = models.ForeignKey(ProductCategory, on_delete=models.CASCADE, related_name='products')
    brand = models.CharField(max_length=100, blank=True)
    brand_ar = models.CharField(max_length=100, blank=True)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    cost_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    quantity_in_stock = models.IntegerField(default=0)
    minimum_stock_level = models.IntegerField(default=0)
    maximum_stock_level = models.IntegerField(null=True, blank=True)
    reorder_point = models.IntegerField(default=0)
    unit_of_measure = models.CharField(max_length=50, default='piece')
    unit_of_measure_ar = models.CharField(max_length=50, blank=True)
    barcode = models.CharField(max_length=100, blank=True)
    weight = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    dimensions = models.CharField(max_length=100, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True)
    location = models.CharField(max_length=100, blank=True)
    location_ar = models.CharField(max_length=100, blank=True)
    expiry_date = models.DateField(null=True, blank=True)
    batch_number = models.CharField(max_length=50, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.sku})"

    class Meta:
        ordering = ['-created_at']

# Report Management Models
class Report(models.Model):
    REPORT_TYPES = [
        ('employee', 'Employee Report'),
        ('department', 'Department Report'),
        ('financial', 'Financial Report'),
        ('performance', 'Performance Report'),
        ('attendance', 'Attendance Report'),
        ('payroll', 'Payroll Report'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    type = models.CharField(max_length=20, choices=REPORT_TYPES)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_date = models.DateTimeField(auto_now_add=True)
    completed_date = models.DateTimeField(null=True, blank=True)
    file_size = models.CharField(max_length=50, blank=True)
    file_url = models.URLField(blank=True)
    parameters = models.JSONField(default=dict, blank=True)
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='created_reports', null=True, blank=True)

    def __str__(self):
        return f"{self.name} ({self.get_type_display()})"

    class Meta:
        ordering = ['-created_date']

# Sales Management Models
class SalesOrder(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('processing', 'Processing'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    order_number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey('Customer', on_delete=models.CASCADE, related_name='sales_orders')
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium')
    order_date = models.DateField()
    delivery_date = models.DateField(null=True, blank=True)
    items_count = models.IntegerField(default=0)
    discount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    tax = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_sales_orders')

    def __str__(self):
        return f"{self.order_number} - {self.customer.get_full_name()}"

    class Meta:
        ordering = ['-created_at']

# Customer Management Models
class Customer(models.Model):
    CUSTOMER_TYPES = [
        ('individual', 'Individual'),
        ('business', 'Business'),
        ('enterprise', 'Enterprise'),
    ]

    CUSTOMER_STATUS = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('suspended', 'Suspended'),
        ('prospect', 'Prospect'),
    ]

    # Basic Information
    customer_id = models.CharField(max_length=20, unique=True, editable=False)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100, blank=True)
    email = models.EmailField(unique=True)
    phone = models.CharField(max_length=20, blank=True)

    # Customer Details
    customer_type = models.CharField(max_length=20, choices=CUSTOMER_TYPES, default='individual')
    status = models.CharField(max_length=20, choices=CUSTOMER_STATUS, default='prospect')

    # Company Information (for business customers)
    company_name = models.CharField(max_length=200, blank=True)
    company_size = models.CharField(max_length=50, blank=True)
    industry = models.CharField(max_length=100, blank=True)

    # Address Information
    address_line1 = models.CharField(max_length=255, blank=True)
    address_line2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, blank=True)

    # Tracking
    source = models.CharField(max_length=50, blank=True, help_text='How customer found us')
    internal_notes = models.TextField(blank=True)
    tags = models.CharField(max_length=500, blank=True, help_text='Comma-separated tags')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_contact_date = models.DateTimeField(null=True, blank=True)

    # Statistics
    total_orders = models.IntegerField(default=0)
    total_spent = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    satisfaction_score = models.FloatField(default=0.0, help_text='Average satisfaction score')

    class Meta:
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if not self.customer_id:
            self.customer_id = self.generate_customer_id()
        super().save(*args, **kwargs)

    def generate_customer_id(self):
        """Generate unique customer ID"""
        import random
        import string
        from django.utils import timezone

        while True:
            # Format: CUST-YYYYMMDD-XXXX
            date_part = timezone.now().strftime('%Y%m%d')
            random_part = ''.join(random.choices(string.digits, k=4))
            customer_id = f"CUST-{date_part}-{random_part}"

            if not Customer.objects.filter(customer_id=customer_id).exists():
                return customer_id

    def get_full_name(self):
        """Get customer's full name"""
        if self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.first_name

    def __str__(self):
        return f"{self.customer_id} - {self.get_full_name()} ({self.email})"

# Communication & Collaboration Models
class Announcement(models.Model):
    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('NORMAL', 'Normal'),
        ('HIGH', 'High'),
        ('URGENT', 'Urgent'),
    ]

    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    content = models.TextField()
    content_ar = models.TextField(blank=True)
    author = models.ForeignKey(Employee, on_delete=models.CASCADE)
    target_departments = models.ManyToManyField(Department, blank=True)
    target_employees = models.ManyToManyField(Employee, blank=True, related_name='received_announcements')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='NORMAL')
    is_published = models.BooleanField(default=False)
    publish_date = models.DateTimeField(null=True, blank=True)
    expiry_date = models.DateTimeField(null=True, blank=True)
    attachment = models.FileField(upload_to='announcements/', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

class Message(models.Model):
    sender = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='sent_messages')
    recipient = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='received_messages')
    subject = models.CharField(max_length=200)
    content = models.TextField()
    is_read = models.BooleanField(default=False)
    is_important = models.BooleanField(default=False)
    parent_message = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)
    attachment = models.FileField(upload_to='messages/', null=True, blank=True)
    sent_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.sender} to {self.recipient}: {self.subject}"

    class Meta:
        ordering = ['-sent_at']

class Document(models.Model):
    CATEGORY_CHOICES = [
        ('POLICY', 'Policy'),
        ('PROCEDURE', 'Procedure'),
        ('FORM', 'Form'),
        ('MANUAL', 'Manual'),
        ('REPORT', 'Report'),
        ('CONTRACT', 'Contract'),
        ('OTHER', 'Other'),
    ]

    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    file = models.FileField(upload_to='documents/')
    version = models.CharField(max_length=20, default='1.0')
    uploaded_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    is_public = models.BooleanField(default=False)
    access_permissions = models.ManyToManyField(Employee, blank=True, related_name='accessible_documents')
    tags = models.CharField(max_length=500, blank=True)
    download_count = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

class Meeting(models.Model):
    STATUS_CHOICES = [
        ('SCHEDULED', 'Scheduled'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
        ('POSTPONED', 'Postponed'),
    ]

    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True)
    organizer = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='organized_meetings')
    attendees = models.ManyToManyField(Employee, related_name='meetings')
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    location = models.CharField(max_length=200, blank=True)
    meeting_link = models.URLField(blank=True)
    agenda = models.TextField(blank=True)
    agenda_ar = models.TextField(blank=True)
    minutes = models.TextField(blank=True)
    minutes_ar = models.TextField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='SCHEDULED')
    is_recurring = models.BooleanField(default=False)
    recurrence_pattern = models.CharField(max_length=50, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} - {self.start_time.strftime('%Y-%m-%d %H:%M')}"

# Enhanced Activity Model
class Activity(models.Model):
    ACTIVITY_TYPES = [
        ('LOGIN', 'User Login'),
        ('LOGOUT', 'User Logout'),
        ('CREATE', 'Record Created'),
        ('UPDATE', 'Record Updated'),
        ('DELETE', 'Record Deleted'),
        ('REPORT', 'Report Generated'),
        ('APPROVAL', 'Approval Action'),
        ('MESSAGE', 'Message Sent'),
        ('MEETING', 'Meeting Action'),
        ('PROJECT', 'Project Action'),
        ('TASK', 'Task Action'),
        ('EXPENSE', 'Expense Action'),
        ('LEAVE', 'Leave Action'),
        ('ASSET', 'Asset Action'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPES)
    description = models.TextField()
    description_ar = models.TextField(help_text="Arabic description")
    related_object_type = models.CharField(max_length=50, blank=True)  # e.g., 'project', 'task', 'employee'
    related_object_id = models.PositiveIntegerField(null=True, blank=True)
    metadata = models.JSONField(default=dict, blank=True)  # Additional data
    timestamp = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    def __str__(self):
        return f"{self.user.username} - {self.activity_type} - {self.timestamp}"

    class Meta:
        ordering = ['-timestamp']
        verbose_name_plural = "Activities"


# ============================================================================
# ENTERPRISE FEATURES - PHASE 3
# ============================================================================

# Multi-Tenant Architecture Models
class Tenant(models.Model):
    PLAN_CHOICES = [
        ('basic', 'Basic'),
        ('professional', 'Professional'),
        ('enterprise', 'Enterprise'),
        ('custom', 'Custom'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('trial', 'Trial'),
        ('expired', 'Expired'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    domain = models.CharField(max_length=100, unique=True)
    subdomain = models.CharField(max_length=50, unique=True)
    plan = models.CharField(max_length=20, choices=PLAN_CHOICES, default='basic')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='trial')

    # Settings stored as JSON
    settings = models.JSONField(default=dict)
    limits = models.JSONField(default=dict)
    features = models.JSONField(default=dict)
    billing_info = models.JSONField(default=dict)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    metadata = models.JSONField(default=dict)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']


class TenantUser(models.Model):
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, related_name='tenant_users')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=50)
    permissions = models.JSONField(default=list)
    is_active = models.BooleanField(default=True)
    last_login = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['tenant', 'user']


# Machine Learning Models
class MLModel(models.Model):
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('training', 'Training'),
        ('deprecated', 'Deprecated'),
    ]

    TYPE_CHOICES = [
        ('regression', 'Regression'),
        ('classification', 'Classification'),
        ('clustering', 'Clustering'),
        ('forecasting', 'Forecasting'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    type = models.CharField(max_length=20, choices=TYPE_CHOICES)
    version = models.CharField(max_length=20)
    accuracy = models.FloatField(validators=[MinValueValidator(0.0), MaxValueValidator(1.0)])
    features = models.JSONField(default=list)
    hyperparameters = models.JSONField(default=dict)
    training_data_info = models.JSONField(default=dict)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='training')
    last_trained = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} v{self.version}"


class MLPrediction(models.Model):
    PREDICTION_TYPES = [
        ('employee_performance', 'Employee Performance'),
        ('turnover_risk', 'Turnover Risk'),
        ('project_success', 'Project Success'),
        ('budget_forecast', 'Budget Forecast'),
        ('resource_demand', 'Resource Demand'),
        ('kpi_forecast', 'KPI Forecast'),
        ('kpi_anomaly', 'KPI Anomaly Detection'),
        ('kpi_optimization', 'KPI Optimization'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    model = models.ForeignKey(MLModel, on_delete=models.CASCADE)
    prediction_type = models.CharField(max_length=30, choices=PREDICTION_TYPES)
    input_data = models.JSONField()
    prediction_result = models.JSONField()
    confidence = models.FloatField(validators=[MinValueValidator(0.0), MaxValueValidator(1.0)])
    factors = models.JSONField(default=list)
    recommendations = models.JSONField(default=list)
    related_object_type = models.CharField(max_length=50, blank=True)
    related_object_id = models.PositiveIntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(default=dict)

    def __str__(self):
        return f"{self.prediction_type} - {self.confidence:.2f}"

    class Meta:
        ordering = ['-created_at']


# Automation Models
class AutomationRule(models.Model):
    CATEGORY_CHOICES = [
        ('hr', 'HR'),
        ('finance', 'Finance'),
        ('project', 'Project'),
        ('system', 'System'),
        ('custom', 'Custom'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    description = models.TextField()
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)

    # Trigger configuration
    trigger_config = models.JSONField()

    # Conditions and actions
    conditions = models.JSONField(default=list)
    actions = models.JSONField(default=list)

    is_active = models.BooleanField(default=True)
    execution_count = models.PositiveIntegerField(default=0)
    success_count = models.PositiveIntegerField(default=0)
    last_executed = models.DateTimeField(null=True, blank=True)

    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    @property
    def success_rate(self):
        if self.execution_count == 0:
            return 0.0
        return self.success_count / self.execution_count


class AutomationExecution(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    rule = models.ForeignKey(AutomationRule, on_delete=models.CASCADE, related_name='executions')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    start_time = models.DateTimeField(auto_now_add=True)
    end_time = models.DateTimeField(null=True, blank=True)
    context_data = models.JSONField(default=dict)
    result_data = models.JSONField(default=dict)
    error_message = models.TextField(blank=True)
    logs = models.JSONField(default=list)

    def __str__(self):
        return f"{self.rule.name} - {self.status}"

    class Meta:
        ordering = ['-start_time']


# Compliance Models
class ComplianceFramework(models.Model):
    TYPE_CHOICES = [
        ('data_protection', 'Data Protection'),
        ('financial', 'Financial'),
        ('industry', 'Industry'),
        ('security', 'Security'),
        ('labor', 'Labor'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    description = models.TextField()
    region = models.CharField(max_length=100)
    type = models.CharField(max_length=20, choices=TYPE_CHOICES)
    version = models.CharField(max_length=20, default='1.0')
    is_active = models.BooleanField(default=False)
    compliance_score = models.FloatField(default=0.0, validators=[MinValueValidator(0.0), MaxValueValidator(100.0)])
    last_assessment = models.DateTimeField(null=True, blank=True)
    next_assessment = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class ComplianceRequirement(models.Model):
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    STATUS_CHOICES = [
        ('compliant', 'Compliant'),
        ('non_compliant', 'Non-Compliant'),
        ('partial', 'Partial'),
        ('not_assessed', 'Not Assessed'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    framework = models.ForeignKey(ComplianceFramework, on_delete=models.CASCADE, related_name='requirements')
    title = models.CharField(max_length=300)
    description = models.TextField()
    category = models.CharField(max_length=100)
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='not_assessed')
    assigned_to = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    evidence_required = models.JSONField(default=list)
    controls = models.JSONField(default=list)
    last_review = models.DateTimeField(null=True, blank=True)
    next_review = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.framework.name} - {self.title}"


class ComplianceEvidence(models.Model):
    TYPE_CHOICES = [
        ('document', 'Document'),
        ('policy', 'Policy'),
        ('procedure', 'Procedure'),
        ('training', 'Training'),
        ('audit', 'Audit'),
        ('certification', 'Certification'),
    ]

    STATUS_CHOICES = [
        ('valid', 'Valid'),
        ('expired', 'Expired'),
        ('pending_review', 'Pending Review'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    requirement = models.ForeignKey(ComplianceRequirement, on_delete=models.CASCADE, related_name='evidence')
    type = models.CharField(max_length=20, choices=TYPE_CHOICES)
    title = models.CharField(max_length=200)
    description = models.TextField()
    file = models.FileField(upload_to='compliance/evidence/', null=True, blank=True)
    url = models.URLField(blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='valid')
    uploaded_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    expiry_date = models.DateTimeField(null=True, blank=True)
    metadata = models.JSONField(default=dict)

    def __str__(self):
        return self.title


class ComplianceAssessment(models.Model):
    TYPE_CHOICES = [
        ('self', 'Self Assessment'),
        ('internal', 'Internal Audit'),
        ('external', 'External Audit'),
        ('regulatory', 'Regulatory Inspection'),
    ]

    STATUS_CHOICES = [
        ('planned', 'Planned'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    framework = models.ForeignKey(ComplianceFramework, on_delete=models.CASCADE, related_name='assessments')
    assessment_type = models.CharField(max_length=20, choices=TYPE_CHOICES)
    assessor = models.CharField(max_length=200)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='planned')
    overall_score = models.FloatField(default=0.0, validators=[MinValueValidator(0.0), MaxValueValidator(100.0)])
    findings = models.JSONField(default=list)
    recommendations = models.JSONField(default=list)
    next_assessment = models.DateField(null=True, blank=True)
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.framework.name} - {self.assessment_type} - {self.start_date}"


class DataProtectionRecord(models.Model):
    LEGAL_BASIS_CHOICES = [
        ('consent', 'Consent'),
        ('contract', 'Contract'),
        ('legal_obligation', 'Legal Obligation'),
        ('vital_interests', 'Vital Interests'),
        ('public_task', 'Public Task'),
        ('legitimate_interests', 'Legitimate Interests'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    data_type = models.CharField(max_length=200)
    purpose = models.TextField()
    legal_basis = models.CharField(max_length=30, choices=LEGAL_BASIS_CHOICES)
    data_subjects = models.JSONField(default=list)
    retention_period = models.PositiveIntegerField(help_text="Retention period in years")
    processing_activities = models.JSONField(default=list)
    third_party_sharing = models.BooleanField(default=False)
    cross_border_transfer = models.BooleanField(default=False)
    security_measures = models.JSONField(default=list)
    data_controller = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='controlled_data')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.data_type


class AuditTrail(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    action = models.CharField(max_length=100)
    resource = models.CharField(max_length=100)
    resource_id = models.CharField(max_length=100, blank=True)
    old_values = models.JSONField(null=True, blank=True)
    new_values = models.JSONField(null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    compliance_relevant = models.BooleanField(default=False)
    retention_date = models.DateTimeField()
    metadata = models.JSONField(default=dict)

    def __str__(self):
        return f"{self.user.username} - {self.action} - {self.timestamp}"

    class Meta:
        ordering = ['-timestamp']


class DataSubjectRequest(models.Model):
    TYPE_CHOICES = [
        ('access', 'Data Access'),
        ('rectification', 'Data Rectification'),
        ('erasure', 'Data Erasure'),
        ('portability', 'Data Portability'),
        ('restriction', 'Processing Restriction'),
        ('objection', 'Processing Objection'),
    ]

    STATUS_CHOICES = [
        ('received', 'Received'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    request_type = models.CharField(max_length=20, choices=TYPE_CHOICES)
    requester_name = models.CharField(max_length=200)
    requester_email = models.EmailField()
    description = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='received')
    submitted_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    assigned_to = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    response = models.TextField(blank=True)
    documents = models.JSONField(default=list)
    metadata = models.JSONField(default=dict)

    def __str__(self):
        return f"{self.request_type} - {self.requester_email}"

    class Meta:
        ordering = ['-submitted_at']


# KPI Management Models
class KPICategory(models.Model):
    """Categories for organizing KPIs"""
    CATEGORY_TYPES = [
        ('FINANCIAL', 'Financial'),
        ('HR', 'Human Resources'),
        ('OPERATIONS', 'Operations'),
        ('SALES', 'Sales & Marketing'),
        ('CUSTOMER', 'Customer Service'),
        ('QUALITY', 'Quality Management'),
        ('INNOVATION', 'Innovation & Development'),
        ('COMPLIANCE', 'Compliance & Risk'),
        ('SUSTAINABILITY', 'Sustainability'),
        ('STRATEGIC', 'Strategic Goals'),
    ]

    name = models.CharField(max_length=50, choices=CATEGORY_TYPES, unique=True)
    name_ar = models.CharField(max_length=100, help_text="Arabic name")
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True, help_text="Arabic description")
    icon = models.CharField(max_length=50, default='BarChart3')  # Lucide icon name
    color = models.CharField(max_length=50, default='from-blue-500 to-blue-600')  # Tailwind gradient
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.get_name_display()

    class Meta:
        ordering = ['sort_order', 'name']
        verbose_name_plural = "KPI Categories"


class KPI(models.Model):
    """Key Performance Indicator definitions"""
    MEASUREMENT_TYPES = [
        ('NUMBER', 'Number'),
        ('PERCENTAGE', 'Percentage'),
        ('CURRENCY', 'Currency'),
        ('RATIO', 'Ratio'),
        ('SCORE', 'Score'),
        ('TIME', 'Time Duration'),
    ]

    FREQUENCY_CHOICES = [
        ('DAILY', 'Daily'),
        ('WEEKLY', 'Weekly'),
        ('MONTHLY', 'Monthly'),
        ('QUARTERLY', 'Quarterly'),
        ('YEARLY', 'Yearly'),
    ]

    TREND_DIRECTION = [
        ('UP', 'Higher is Better'),
        ('DOWN', 'Lower is Better'),
        ('TARGET', 'Target Value'),
    ]

    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('DRAFT', 'Draft'),
        ('ARCHIVED', 'Archived'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, help_text="Arabic name")
    description = models.TextField()
    description_ar = models.TextField(help_text="Arabic description")
    category = models.ForeignKey(KPICategory, on_delete=models.CASCADE, related_name='kpis')

    # Measurement details
    measurement_type = models.CharField(max_length=20, choices=MEASUREMENT_TYPES)
    unit = models.CharField(max_length=50, blank=True, help_text="e.g., %, $, hours")
    unit_ar = models.CharField(max_length=50, blank=True, help_text="Arabic unit")
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES)
    trend_direction = models.CharField(max_length=10, choices=TREND_DIRECTION)

    # Calculation details
    formula = models.TextField(blank=True, help_text="Calculation formula or description")
    data_source = models.CharField(max_length=200, blank=True, help_text="Source of data")
    calculation_method = models.TextField(blank=True)

    # Current value and target
    current_value = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True, help_text="Current KPI value")
    target_value = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    warning_threshold = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    critical_threshold = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)

    # Value metadata
    last_updated = models.DateTimeField(null=True, blank=True, help_text="When the current value was last updated")
    updated_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_kpis', help_text="Who last updated the value")

    # Access control
    visible_to_roles = models.ManyToManyField(Role, blank=True, help_text="Roles that can view this KPI")
    owner = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, related_name='owned_kpis')

    # Status and metadata
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ACTIVE')
    is_automated = models.BooleanField(default=False, help_text="Automatically calculated")
    automation_config = models.JSONField(default=dict, blank=True)

    # Algorithm and AI features
    use_ml_prediction = models.BooleanField(default=False, help_text="Use ML for KPI prediction")
    ml_model = models.ForeignKey('MLModel', on_delete=models.SET_NULL, null=True, blank=True, help_text="ML model for predictions")
    anomaly_detection = models.BooleanField(default=True, help_text="Enable anomaly detection")
    auto_recommendations = models.BooleanField(default=True, help_text="Generate AI recommendations")
    algorithm_config = models.JSONField(default=dict, blank=True, help_text="Algorithm configuration")

    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='created_kpis')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['category', 'name']
        verbose_name = "KPI"
        verbose_name_plural = "KPIs"


class KPIValue(models.Model):
    """Historical KPI values and measurements"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    kpi = models.ForeignKey(KPI, on_delete=models.CASCADE, related_name='values')
    value = models.DecimalField(max_digits=15, decimal_places=4)
    period_start = models.DateTimeField()
    period_end = models.DateTimeField()

    # Context and metadata
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    project = models.ForeignKey(Project, on_delete=models.SET_NULL, null=True, blank=True)
    employee = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)

    # Data quality
    data_quality_score = models.DecimalField(max_digits=5, decimal_places=2, default=100.00)
    confidence_level = models.DecimalField(max_digits=5, decimal_places=2, default=100.00)
    notes = models.TextField(blank=True)

    # Tracking
    recorded_by = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='recorded_kpi_values')
    recorded_at = models.DateTimeField(auto_now_add=True)
    is_estimated = models.BooleanField(default=False)
    source_data = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"{self.kpi.name} - {self.value} ({self.period_start.date()})"

    class Meta:
        ordering = ['-period_start']
        unique_together = ['kpi', 'period_start', 'department', 'project', 'employee']


class KPITarget(models.Model):
    """Target values for KPIs across different time periods"""
    TARGET_TYPES = [
        ('ABSOLUTE', 'Absolute Value'),
        ('PERCENTAGE_CHANGE', 'Percentage Change'),
        ('RELATIVE', 'Relative to Baseline'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    kpi = models.ForeignKey(KPI, on_delete=models.CASCADE, related_name='targets')
    target_value = models.DecimalField(max_digits=15, decimal_places=4)
    target_type = models.CharField(max_length=20, choices=TARGET_TYPES, default='ABSOLUTE')

    # Time period
    start_date = models.DateField()
    end_date = models.DateField()

    # Context
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    project = models.ForeignKey(Project, on_delete=models.SET_NULL, null=True, blank=True)
    employee = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)

    # Metadata
    description = models.TextField(blank=True)
    is_stretch_goal = models.BooleanField(default=False)
    weight = models.DecimalField(max_digits=5, decimal_places=2, default=1.00)

    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='created_kpi_targets')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.kpi.name} Target: {self.target_value} ({self.start_date} - {self.end_date})"

    class Meta:
        ordering = ['-start_date']


class KPIAlert(models.Model):
    """Alerts for KPI threshold breaches"""
    ALERT_TYPES = [
        ('THRESHOLD_BREACH', 'Threshold Breach'),
        ('TARGET_MISSED', 'Target Missed'),
        ('TREND_CHANGE', 'Trend Change'),
        ('DATA_QUALITY', 'Data Quality Issue'),
        ('MISSING_DATA', 'Missing Data'),
    ]

    SEVERITY_LEVELS = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]

    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('ACKNOWLEDGED', 'Acknowledged'),
        ('RESOLVED', 'Resolved'),
        ('DISMISSED', 'Dismissed'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    kpi = models.ForeignKey(KPI, on_delete=models.CASCADE, related_name='alerts')
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES)
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS)

    # Alert details
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200)
    message = models.TextField()
    message_ar = models.TextField()

    # Values
    current_value = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    threshold_value = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    target_value = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)

    # Status and resolution
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ACTIVE')
    acknowledged_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='acknowledged_alerts')
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_alerts')
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True)

    # Notifications
    notified_users = models.ManyToManyField(Employee, blank=True, related_name='kpi_alert_notifications')
    notification_sent = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.kpi.name} - {self.title} ({self.severity})"

    class Meta:
        ordering = ['-created_at']


# Performance Review Models
class PerformanceReview(models.Model):
    REVIEW_TYPES = [
        ('annual', 'Annual Review'),
        ('quarterly', 'Quarterly Review'),
        ('probation', 'Probation Review'),
        ('project', 'Project Review'),
    ]

    RATING_CHOICES = [
        (1, 'Poor'),
        (2, 'Below Average'),
        (3, 'Average'),
        (4, 'Good'),
        (5, 'Excellent'),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='performance_reviews')
    reviewer = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='conducted_reviews')
    review_period_start = models.DateField()
    review_period_end = models.DateField()
    review_type = models.CharField(max_length=20, choices=REVIEW_TYPES, default='annual')
    overall_rating = models.IntegerField(choices=RATING_CHOICES)
    goals_achievement = models.IntegerField(choices=RATING_CHOICES)
    communication_skills = models.IntegerField(choices=RATING_CHOICES)
    teamwork = models.IntegerField(choices=RATING_CHOICES)
    leadership = models.IntegerField(choices=RATING_CHOICES, null=True, blank=True)
    technical_skills = models.IntegerField(choices=RATING_CHOICES)
    strengths = models.TextField()
    areas_for_improvement = models.TextField()
    goals_for_next_period = models.TextField()
    reviewer_comments = models.TextField()
    employee_comments = models.TextField(blank=True)
    hr_comments = models.TextField(blank=True)
    is_final = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.review_type} ({self.review_period_start})"

    class Meta:
        ordering = ['-review_period_end']


# Payroll Models
class PayrollPeriod(models.Model):
    name = models.CharField(max_length=100)
    start_date = models.DateField()
    end_date = models.DateField()
    is_processed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"

    class Meta:
        ordering = ['-start_date']


class PayrollEntry(models.Model):
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE)
    payroll_period = models.ForeignKey(PayrollPeriod, on_delete=models.CASCADE)
    basic_salary = models.DecimalField(max_digits=10, decimal_places=2)
    allowances = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    overtime_hours = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    overtime_rate = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    bonuses = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    deductions = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    tax_deduction = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    insurance_deduction = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    net_salary = models.DecimalField(max_digits=10, decimal_places=2)
    is_paid = models.BooleanField(default=False)
    payment_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        # Calculate net salary
        gross_salary = self.basic_salary + self.allowances + (self.overtime_hours * self.overtime_rate) + self.bonuses
        total_deductions = self.deductions + self.tax_deduction + self.insurance_deduction
        self.net_salary = gross_salary - total_deductions
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.payroll_period.name}"

    class Meta:
        unique_together = ['employee', 'payroll_period']


# Recruitment Models
class JobPosting(models.Model):
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('closed', 'Closed'),
        ('on_hold', 'On Hold'),
    ]

    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    requirements = models.TextField()
    requirements_ar = models.TextField(blank=True)
    salary_min = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    salary_max = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    employment_type = models.CharField(max_length=50, choices=[
        ('full_time', 'Full Time'),
        ('part_time', 'Part Time'),
        ('contract', 'Contract'),
        ('internship', 'Internship'),
    ], default='full_time')
    location = models.CharField(max_length=200)
    location_ar = models.CharField(max_length=200, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    posted_date = models.DateField(auto_now_add=True)
    closing_date = models.DateField(null=True, blank=True)
    posted_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} - {self.department.name}"

    class Meta:
        ordering = ['-posted_date']


# Training Models
class TrainingProgram(models.Model):
    STATUS_CHOICES = [
        ('planned', 'Planned'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField()
    description_ar = models.TextField(blank=True)
    instructor = models.CharField(max_length=200)
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True)
    start_date = models.DateField()
    end_date = models.DateField()
    duration_hours = models.IntegerField()
    max_participants = models.IntegerField()
    cost_per_participant = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    location = models.CharField(max_length=200, blank=True)
    is_mandatory = models.BooleanField(default=False)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='planned')
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title

    class Meta:
        ordering = ['-start_date']


# Invoice Models
class Invoice(models.Model):
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('paid', 'Paid'),
        ('overdue', 'Overdue'),
        ('cancelled', 'Cancelled'),
    ]

    invoice_number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='invoices')
    issue_date = models.DateField()
    due_date = models.DateField()
    subtotal = models.DecimalField(max_digits=12, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    notes = models.TextField(blank=True)
    payment_terms = models.CharField(max_length=200, blank=True)
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()
        super().save(*args, **kwargs)

    def generate_invoice_number(self):
        from django.utils import timezone
        import random
        date_part = timezone.now().strftime('%Y%m')
        random_part = f"{random.randint(1000, 9999)}"
        return f"INV-{date_part}-{random_part}"

    def __str__(self):
        return f"{self.invoice_number} - {self.customer.get_full_name()}"

    class Meta:
        ordering = ['-created_at']


# Cost Center Models
class CostCenter(models.Model):
    code = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=200)
    name_ar = models.CharField(max_length=200, blank=True)
    description = models.TextField(blank=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='cost_centers')
    manager = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True)
    budget_allocated = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    budget_spent = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.code} - {self.name}"

    class Meta:
        ordering = ['code']
