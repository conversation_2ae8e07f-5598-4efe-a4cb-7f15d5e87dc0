from rest_framework import serializers
from django.contrib.auth.models import User
from .models import (
    Department, Employee, Activity, Role, UserProfile, LeaveType, LeaveRequest,
    Attendance, Project, Task, Budget, Expense, AssetCategory, Asset, Supplier,
    PurchaseOrder, Announcement, Message, Document, Meeting, Customer,
    ProductCategory, Product, Report, SalesOrder, KPICategory, KPI, KPIValue, KPITarget, KPIAlert
)

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'is_active', 'date_joined']

class DepartmentSerializer(serializers.ModelSerializer):
    employee_count = serializers.SerializerMethodField()

    class Meta:
        model = Department
        fields = ['id', 'name', 'name_ar', 'description', 'description_ar',
                 'employee_count', 'created_at', 'updated_at']

    def get_employee_count(self, obj):
        return obj.employee_set.filter(is_active=True).count()

class EmployeeSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    department_name_ar = serializers.CharField(source='department.name_ar', read_only=True)

    # Fields for creating user
    first_name = serializers.CharField(write_only=True, required=False)
    last_name = serializers.CharField(write_only=True, required=False)
    email = serializers.EmailField(write_only=True, required=False)
    username = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = Employee
        fields = ['id', 'user', 'employee_id', 'department', 'department_name',
                 'department_name_ar', 'position', 'position_ar', 'first_name_ar', 'last_name_ar',
                 'phone', 'gender', 'hire_date', 'salary', 'is_active', 'created_at', 'updated_at',
                 'first_name', 'last_name', 'email', 'username']

    def create(self, validated_data):
        # Extract user data
        first_name = validated_data.pop('first_name', '')
        last_name = validated_data.pop('last_name', '')
        email = validated_data.pop('email', '')
        username = validated_data.pop('username', '')

        # If no username provided, generate one from employee_id or email
        if not username:
            username = validated_data.get('employee_id', email.split('@')[0] if email else f"user_{validated_data.get('employee_id', '')}")

        # Create User first
        user = User.objects.create_user(
            username=username,
            email=email,
            first_name=first_name,
            last_name=last_name,
            is_active=validated_data.get('is_active', True)
        )

        # Create Employee with the user
        validated_data['user'] = user
        employee = Employee.objects.create(**validated_data)

        return employee

    def update(self, instance, validated_data):
        # Extract user data
        first_name = validated_data.pop('first_name', None)
        last_name = validated_data.pop('last_name', None)
        email = validated_data.pop('email', None)
        username = validated_data.pop('username', None)

        # Update user if user data provided
        if any([first_name, last_name, email, username]):
            user = instance.user
            if first_name is not None:
                user.first_name = first_name
            if last_name is not None:
                user.last_name = last_name
            if email is not None:
                user.email = email
            if username is not None:
                user.username = username
            user.save()

        # Update employee
        return super().update(instance, validated_data)

class ActivitySerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)

    class Meta:
        model = Activity
        fields = ['id', 'user', 'user_name', 'activity_type', 'description',
                 'description_ar', 'timestamp', 'ip_address']

class DashboardStatsSerializer(serializers.Serializer):
    total_employees = serializers.IntegerField()
    total_departments = serializers.IntegerField()
    active_employees = serializers.IntegerField()
    recent_activities = ActivitySerializer(many=True)

# Role and User Profile Serializers
class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ['id', 'name', 'name_ar', 'description', 'description_ar', 'permissions', 'created_at']

class UserProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    role = RoleSerializer(read_only=True)

    class Meta:
        model = UserProfile
        fields = ['id', 'user', 'role', 'avatar', 'phone', 'address', 'date_of_birth',
                 'emergency_contact_name', 'emergency_contact_phone', 'preferred_language',
                 'timezone', 'created_at', 'updated_at']

# HR Management Serializers
class LeaveTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = LeaveType
        fields = ['id', 'name', 'name_ar', 'days_allowed', 'is_paid', 'requires_approval',
                 'carry_forward', 'description', 'description_ar', 'is_active', 'created_at']

class LeaveRequestSerializer(serializers.ModelSerializer):
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)
    leave_type_name = serializers.CharField(source='leave_type.name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.user.get_full_name', read_only=True)

    class Meta:
        model = LeaveRequest
        fields = ['id', 'employee', 'employee_name', 'leave_type', 'leave_type_name',
                 'start_date', 'end_date', 'days_requested', 'reason', 'reason_ar',
                 'status', 'approved_by', 'approved_by_name', 'approval_date',
                 'rejection_reason', 'rejection_reason_ar', 'created_at', 'updated_at']

class AttendanceSerializer(serializers.ModelSerializer):
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)

    class Meta:
        model = Attendance
        fields = ['id', 'employee', 'employee_name', 'date', 'check_in', 'check_out',
                 'break_start', 'break_end', 'total_hours', 'overtime_hours',
                 'is_present', 'is_late', 'notes', 'created_at', 'updated_at']

# Project Management Serializers
class ProjectSerializer(serializers.ModelSerializer):
    project_manager_name = serializers.CharField(source='project_manager.user.get_full_name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    team_members_count = serializers.SerializerMethodField()
    tasks_count = serializers.SerializerMethodField()

    class Meta:
        model = Project
        fields = ['id', 'name', 'name_ar', 'description', 'description_ar',
                 'project_manager', 'project_manager_name', 'department', 'department_name',
                 'team_members', 'team_members_count', 'client', 'budget_amount',
                 'start_date', 'end_date', 'actual_start_date', 'actual_end_date',
                 'status', 'priority', 'progress_percentage', 'tasks_count',
                 'is_active', 'created_at', 'updated_at']

    def get_team_members_count(self, obj):
        return obj.team_members.count()

    def get_tasks_count(self, obj):
        return obj.tasks.count()

class TaskSerializer(serializers.ModelSerializer):
    project_name = serializers.CharField(source='project.name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.user.get_full_name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)

    class Meta:
        model = Task
        fields = ['id', 'project', 'project_name', 'title', 'title_ar', 'description',
                 'description_ar', 'assigned_to', 'assigned_to_name', 'created_by',
                 'created_by_name', 'due_date', 'start_date', 'completion_date',
                 'estimated_hours', 'actual_hours', 'status', 'priority',
                 'progress_percentage', 'dependencies', 'tags', 'created_at', 'updated_at']

# Financial Management Serializers
class BudgetSerializer(serializers.ModelSerializer):
    department_name = serializers.CharField(source='department.name', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)

    class Meta:
        model = Budget
        fields = ['id', 'name', 'name_ar', 'department', 'department_name',
                 'project', 'project_name', 'fiscal_year', 'total_amount',
                 'allocated_amount', 'spent_amount', 'remaining_amount',
                 'start_date', 'end_date', 'is_active', 'created_by',
                 'created_by_name', 'created_at', 'updated_at']

class ExpenseSerializer(serializers.ModelSerializer):
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)
    budget_name = serializers.CharField(source='budget.name', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.user.get_full_name', read_only=True)

    class Meta:
        model = Expense
        fields = ['id', 'employee', 'employee_name', 'budget', 'budget_name',
                 'project', 'project_name', 'title', 'title_ar', 'description',
                 'description_ar', 'category', 'amount', 'currency', 'expense_date',
                 'receipt_file', 'status', 'approved_by', 'approved_by_name',
                 'approval_date', 'rejection_reason', 'created_at', 'updated_at']

# Asset Management Serializers
class AssetCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = AssetCategory
        fields = ['id', 'name', 'name_ar', 'description', 'description_ar', 'created_at']

class AssetSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.user.get_full_name', read_only=True)

    class Meta:
        model = Asset
        fields = ['id', 'asset_id', 'name', 'name_ar', 'category', 'category_name',
                 'description', 'description_ar', 'serial_number', 'model',
                 'manufacturer', 'purchase_date', 'purchase_price', 'current_value',
                 'warranty_expiry', 'assigned_to', 'assigned_to_name', 'location',
                 'status', 'notes', 'created_at', 'updated_at']

class SupplierSerializer(serializers.ModelSerializer):
    class Meta:
        model = Supplier
        fields = ['id', 'name', 'name_ar', 'contact_person', 'email', 'phone',
                 'address', 'website', 'tax_number', 'payment_terms', 'is_active',
                 'created_at', 'updated_at']

class PurchaseOrderSerializer(serializers.ModelSerializer):
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    requested_by_name = serializers.CharField(source='requested_by.user.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.user.get_full_name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)

    class Meta:
        model = PurchaseOrder
        fields = ['id', 'po_number', 'supplier', 'supplier_name', 'requested_by',
                 'requested_by_name', 'approved_by', 'approved_by_name', 'department',
                 'department_name', 'project', 'project_name', 'order_date',
                 'expected_delivery', 'actual_delivery', 'total_amount', 'currency',
                 'status', 'notes', 'created_at', 'updated_at']

# Communication & Collaboration Serializers
class AnnouncementSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.user.get_full_name', read_only=True)
    target_departments_names = serializers.SerializerMethodField()
    target_employees_count = serializers.SerializerMethodField()

    class Meta:
        model = Announcement
        fields = ['id', 'title', 'title_ar', 'content', 'content_ar', 'author',
                 'author_name', 'target_departments', 'target_departments_names',
                 'target_employees', 'target_employees_count', 'priority',
                 'is_published', 'publish_date', 'expiry_date', 'attachment',
                 'created_at', 'updated_at']

    def get_target_departments_names(self, obj):
        return [dept.name for dept in obj.target_departments.all()]

    def get_target_employees_count(self, obj):
        return obj.target_employees.count()

class MessageSerializer(serializers.ModelSerializer):
    sender_name = serializers.CharField(source='sender.user.get_full_name', read_only=True)
    recipient_name = serializers.CharField(source='recipient.user.get_full_name', read_only=True)

    class Meta:
        model = Message
        fields = ['id', 'sender', 'sender_name', 'recipient', 'recipient_name',
                 'subject', 'content', 'is_read', 'is_important', 'parent_message',
                 'attachment', 'sent_at', 'read_at']

class DocumentSerializer(serializers.ModelSerializer):
    uploaded_by_name = serializers.CharField(source='uploaded_by.user.get_full_name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    access_permissions_count = serializers.SerializerMethodField()

    class Meta:
        model = Document
        fields = ['id', 'title', 'title_ar', 'description', 'description_ar',
                 'category', 'file', 'version', 'uploaded_by', 'uploaded_by_name',
                 'department', 'department_name', 'is_public', 'access_permissions',
                 'access_permissions_count', 'tags', 'download_count',
                 'created_at', 'updated_at']

    def get_access_permissions_count(self, obj):
        return obj.access_permissions.count()

class MeetingSerializer(serializers.ModelSerializer):
    organizer_name = serializers.CharField(source='organizer.user.get_full_name', read_only=True)
    attendees_names = serializers.SerializerMethodField()
    attendees_count = serializers.SerializerMethodField()

    class Meta:
        model = Meeting
        fields = ['id', 'title', 'title_ar', 'description', 'description_ar',
                 'organizer', 'organizer_name', 'attendees', 'attendees_names',
                 'attendees_count', 'start_time', 'end_time', 'location',
                 'meeting_link', 'agenda', 'agenda_ar', 'minutes', 'minutes_ar',
                 'status', 'is_recurring', 'recurrence_pattern', 'created_at', 'updated_at']

    def get_attendees_names(self, obj):
        return [emp.user.get_full_name() for emp in obj.attendees.all()]

    def get_attendees_count(self, obj):
        return obj.attendees.count()

# Customer Management Serializers
class CustomerSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = Customer
        fields = [
            'id', 'customer_id', 'first_name', 'last_name', 'full_name', 'email', 'phone',
            'customer_type', 'status', 'company_name', 'company_size', 'industry',
            'address_line1', 'address_line2', 'city', 'state', 'postal_code', 'country',
            'source', 'internal_notes', 'tags', 'total_orders', 'total_spent',
            'satisfaction_score', 'created_at', 'updated_at', 'last_contact_date'
        ]
        read_only_fields = ['customer_id', 'created_at', 'updated_at']

    def get_full_name(self, obj):
        return obj.get_full_name()

# Product Management Serializers
class ProductCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductCategory
        fields = ['id', 'name', 'name_ar', 'description', 'description_ar', 'is_active', 'created_at', 'updated_at']

class ProductSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'name_ar', 'sku', 'description', 'description_ar',
            'category', 'category_name', 'brand', 'brand_ar', 'unit_price', 'cost_price',
            'quantity_in_stock', 'minimum_stock_level', 'maximum_stock_level', 'reorder_point',
            'unit_of_measure', 'unit_of_measure_ar', 'barcode', 'weight', 'dimensions',
            'status', 'supplier', 'supplier_name', 'location', 'location_ar',
            'expiry_date', 'batch_number', 'created_at', 'updated_at'
        ]

# Report Management Serializers
class ReportSerializer(serializers.ModelSerializer):
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)

    class Meta:
        model = Report
        fields = [
            'id', 'name', 'name_ar', 'type', 'description', 'description_ar',
            'status', 'created_date', 'completed_date', 'file_size', 'file_url',
            'parameters', 'created_by', 'created_by_name'
        ]
        read_only_fields = ['created_date', 'completed_date', 'created_by']

# Sales Management Serializers
class SalesOrderSerializer(serializers.ModelSerializer):
    customer_name = serializers.SerializerMethodField()
    customer_id = serializers.IntegerField(source='customer.id', read_only=True)

    class Meta:
        model = SalesOrder
        fields = [
            'id', 'order_number', 'customer', 'customer_id', 'customer_name',
            'total_amount', 'status', 'priority', 'order_date', 'delivery_date',
            'items_count', 'discount', 'tax', 'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'customer_id', 'customer_name']
        depth = 0  # Prevent nested serialization

    def get_customer_name(self, obj):
        if obj.customer:
            return obj.customer.get_full_name()
        return None

    def to_representation(self, instance):
        # Get the standard representation
        data = super().to_representation(instance)
        # Ensure customer field is just the ID, not the full object
        if 'customer' in data and isinstance(data['customer'], dict):
            data['customer'] = instance.customer.id if instance.customer else None
        return data

# KPI Serializers
class KPICategorySerializer(serializers.ModelSerializer):
    kpi_count = serializers.SerializerMethodField()

    class Meta:
        model = KPICategory
        fields = [
            'id', 'name', 'name_ar', 'description', 'description_ar',
            'icon', 'color', 'is_active', 'sort_order', 'kpi_count',
            'created_at', 'updated_at'
        ]

    def get_kpi_count(self, obj):
        return obj.kpis.filter(status='ACTIVE').count()


class KPISerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.get_name_display', read_only=True)
    category_name_ar = serializers.CharField(source='category.name_ar', read_only=True)
    owner_name = serializers.CharField(source='owner.user.get_full_name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)
    current_value = serializers.SerializerMethodField()
    target_achievement = serializers.SerializerMethodField()
    trend = serializers.SerializerMethodField()

    class Meta:
        model = KPI
        fields = [
            'id', 'name', 'name_ar', 'description', 'description_ar',
            'category', 'category_name', 'category_name_ar',
            'measurement_type', 'unit', 'unit_ar', 'frequency', 'trend_direction',
            'formula', 'data_source', 'calculation_method',
            'target_value', 'warning_threshold', 'critical_threshold',
            'visible_to_roles', 'owner', 'owner_name',
            'status', 'is_automated', 'automation_config',
            'created_by', 'created_by_name', 'created_at', 'updated_at',
            'current_value', 'target_achievement', 'trend'
        ]

    def get_current_value(self, obj):
        """Get the most recent KPI value"""
        latest_value = obj.values.first()
        if latest_value:
            return {
                'value': float(latest_value.value),
                'period_start': latest_value.period_start,
                'period_end': latest_value.period_end,
                'recorded_at': latest_value.recorded_at
            }
        return None

    def get_target_achievement(self, obj):
        """Calculate achievement percentage against target"""
        current_value = self.get_current_value(obj)
        if current_value and obj.target_value:
            achievement = (current_value['value'] / float(obj.target_value)) * 100
            return round(achievement, 2)
        return None

    def get_trend(self, obj):
        """Calculate trend based on recent values"""
        recent_values = obj.values.order_by('-period_start')[:2]
        if len(recent_values) >= 2:
            current = float(recent_values[0].value)
            previous = float(recent_values[1].value)
            change = ((current - previous) / previous) * 100
            return {
                'change_percentage': round(change, 2),
                'direction': 'up' if change > 0 else 'down' if change < 0 else 'stable'
            }
        return None


class KPIValueSerializer(serializers.ModelSerializer):
    kpi_name = serializers.CharField(source='kpi.name', read_only=True)
    kpi_unit = serializers.CharField(source='kpi.unit', read_only=True)
    recorded_by_name = serializers.CharField(source='recorded_by.user.get_full_name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)

    class Meta:
        model = KPIValue
        fields = [
            'id', 'kpi', 'kpi_name', 'kpi_unit', 'value',
            'period_start', 'period_end',
            'department', 'department_name',
            'project', 'project_name',
            'employee', 'employee_name',
            'data_quality_score', 'confidence_level', 'notes',
            'recorded_by', 'recorded_by_name', 'recorded_at',
            'is_estimated', 'source_data'
        ]


class KPITargetSerializer(serializers.ModelSerializer):
    kpi_name = serializers.CharField(source='kpi.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    employee_name = serializers.CharField(source='employee.user.get_full_name', read_only=True)

    class Meta:
        model = KPITarget
        fields = [
            'id', 'kpi', 'kpi_name', 'target_value', 'target_type',
            'start_date', 'end_date',
            'department', 'department_name',
            'project', 'project_name',
            'employee', 'employee_name',
            'description', 'is_stretch_goal', 'weight',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]


class KPIAlertSerializer(serializers.ModelSerializer):
    kpi_name = serializers.CharField(source='kpi.name', read_only=True)
    acknowledged_by_name = serializers.CharField(source='acknowledged_by.user.get_full_name', read_only=True)
    resolved_by_name = serializers.CharField(source='resolved_by.user.get_full_name', read_only=True)

    class Meta:
        model = KPIAlert
        fields = [
            'id', 'kpi', 'kpi_name', 'alert_type', 'severity',
            'title', 'title_ar', 'message', 'message_ar',
            'current_value', 'threshold_value', 'target_value',
            'status', 'acknowledged_by', 'acknowledged_by_name', 'acknowledged_at',
            'resolved_by', 'resolved_by_name', 'resolved_at', 'resolution_notes',
            'notified_users', 'notification_sent',
            'created_at', 'updated_at'
        ]


class KPIDashboardSerializer(serializers.Serializer):
    """Serializer for KPI dashboard data"""
    categories = KPICategorySerializer(many=True, read_only=True)
    recent_alerts = KPIAlertSerializer(many=True, read_only=True)
    top_performing_kpis = KPISerializer(many=True, read_only=True)
    underperforming_kpis = KPISerializer(many=True, read_only=True)
    kpi_summary = serializers.DictField(read_only=True)


class KPISummarySerializer(serializers.Serializer):
    """Summary statistics for KPIs"""
    total_kpis = serializers.IntegerField()
    active_kpis = serializers.IntegerField()
    kpis_on_target = serializers.IntegerField()
    kpis_above_target = serializers.IntegerField()
    kpis_below_target = serializers.IntegerField()
    active_alerts = serializers.IntegerField()
    critical_alerts = serializers.IntegerField()
    categories_count = serializers.IntegerField()
    last_updated = serializers.DateTimeField()


class KPITrendDataSerializer(serializers.Serializer):
    """Serializer for KPI trend data"""
    kpi_id = serializers.UUIDField()
    kpi_name = serializers.CharField()
    data_points = serializers.ListField(
        child=serializers.DictField()
    )
    trend_direction = serializers.CharField()
    change_percentage = serializers.FloatField()


class RoleBasedKPISerializer(serializers.Serializer):
    """Role-based KPI data for dashboards"""
    role = serializers.CharField()
    accessible_kpis = KPISerializer(many=True, read_only=True)
    role_specific_metrics = serializers.DictField(read_only=True)
    alerts = KPIAlertSerializer(many=True, read_only=True)
