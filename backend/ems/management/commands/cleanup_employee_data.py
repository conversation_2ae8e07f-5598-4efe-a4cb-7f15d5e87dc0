"""
Management command to clean up corrupted employee data
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from ems.models import Employee, User, Department
from decimal import Decimal
import re


class Command(BaseCommand):
    help = 'Clean up corrupted employee data and fix data quality issues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be changed without making actual changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        self.stdout.write('Starting employee data cleanup...')
        
        # Get all employees
        employees = Employee.objects.all()
        issues_found = 0
        issues_fixed = 0
        
        for employee in employees:
            employee_issues = []
            
            # Check for future hire dates
            if employee.hire_date and employee.hire_date > timezone.now().date():
                employee_issues.append(f"Future hire date: {employee.hire_date}")
                if not dry_run:
                    # Set hire date to a random date in the past year
                    days_ago = 30 + (hash(employee.employee_id) % 335)  # 30-365 days ago
                    employee.hire_date = timezone.now().date() - timedelta(days=days_ago)
                    issues_fixed += 1
            
            # Check for garbled names
            user = employee.user
            if user:
                # Check for non-alphabetic characters in names (except spaces and Arabic)
                name_pattern = re.compile(r'^[a-zA-Z\u0600-\u06FF\s\-\.]+$')
                
                # Enhanced check for garbled first names
                garbled_names = ['gngn', 'edcx', 'qwd', 'wqd', 'test', 'audit', 'demo']
                if (not name_pattern.match(user.first_name or '') or
                    any(garbled in user.first_name.lower() for garbled in garbled_names) or
                    len(user.first_name) < 2):
                    employee_issues.append(f"Invalid first name: {user.first_name}")
                    if not dry_run:
                        # Generate realistic names
                        first_names = ['Ahmed', 'Mohammed', 'Omar', 'Ali', 'Hassan', 'Khalid', 'Youssef', 'Mahmoud', 'Amr', 'Tarek']
                        user.first_name = first_names[hash(employee.employee_id) % len(first_names)]
                        issues_fixed += 1
                
                # Enhanced check for garbled last names
                if (not name_pattern.match(user.last_name or '') or
                    any(garbled in user.last_name.lower() for garbled in garbled_names) or
                    len(user.last_name) < 2):
                    employee_issues.append(f"Invalid last name: {user.last_name}")
                    if not dry_run:
                        last_names = ['Al-Rashid', 'Al-Zahra', 'Hassan', 'Johnson', 'Smith', 'Chen', 'Rodriguez', 'Kim', 'Al-Mahmoud', 'Al-Sayed']
                        user.last_name = last_names[hash(employee.employee_id) % len(last_names)]
                        issues_fixed += 1
                
                # Check for test/audit usernames and clean them up
                if any(test_word in user.username.lower() for test_word in ['test', 'audit', 'demo']):
                    employee_issues.append(f"Test username: {user.username}")
                    if not dry_run:
                        # Create a proper username
                        base_username = f"emp{employee.employee_id[-3:]}"
                        counter = 1
                        new_username = base_username
                        while User.objects.filter(username=new_username).exclude(id=user.id).exists():
                            new_username = f"{base_username}_{counter}"
                            counter += 1
                        user.username = new_username
                        issues_fixed += 1
                
                # Check for invalid email addresses
                if '@example.com' in user.email or 'test' in user.email.lower():
                    employee_issues.append(f"Test email: {user.email}")
                    if not dry_run:
                        user.email = f"{user.username}@company.com"
                        issues_fixed += 1
                
                if not dry_run and employee_issues:
                    user.save()
            
            # Check for invalid positions - Enhanced detection
            invalid_position_patterns = ['test', 'audit', 'demo', 'qwd', 'wqd', 'xxx', 'yyy', 'zzz', 'abc', '123']
            if (employee.position and
                (any(pattern in employee.position.lower() for pattern in invalid_position_patterns) or
                 len(employee.position) < 3 or
                 not re.match(r'^[a-zA-Z\u0600-\u06FF\s\-\.]+$', employee.position))):
                employee_issues.append(f"Invalid position: {employee.position}")
                if not dry_run:
                    # Assign realistic positions based on department
                    if employee.department:
                        dept_name = employee.department.name.lower()
                        if 'hr' in dept_name or 'موارد' in dept_name:
                            employee.position = "HR Specialist"
                            employee.position_ar = "أخصائي موارد بشرية"
                        elif 'it' in dept_name or 'تكنولوجيا' in dept_name:
                            employee.position = "Software Developer"
                            employee.position_ar = "مطور برمجيات"
                        elif 'finance' in dept_name or 'مالية' in dept_name:
                            employee.position = "Financial Analyst"
                            employee.position_ar = "محلل مالي"
                        elif 'sales' in dept_name or 'مبيعات' in dept_name:
                            employee.position = "Sales Representative"
                            employee.position_ar = "مندوب مبيعات"
                        else:
                            employee.position = "Employee"
                            employee.position_ar = "موظف"
                    else:
                        employee.position = "Employee"
                        employee.position_ar = "موظف"
                    issues_fixed += 1
            
            # Check for invalid phone numbers
            if employee.phone and not re.match(r'^\+?[\d\-\s\(\)]+$', employee.phone):
                employee_issues.append(f"Invalid phone: {employee.phone}")
                if not dry_run:
                    # Generate a proper phone number
                    employee.phone = f"******-{employee.employee_id[-4:]}"
                    issues_fixed += 1
            
            # Check for unrealistic salaries
            if employee.salary and (employee.salary < 30000 or employee.salary > 500000):
                employee_issues.append(f"Unrealistic salary: {employee.salary}")
                if not dry_run:
                    # Set a reasonable salary based on position
                    if 'manager' in employee.position.lower():
                        employee.salary = Decimal('75000.00')
                    elif 'senior' in employee.position.lower():
                        employee.salary = Decimal('65000.00')
                    else:
                        employee.salary = Decimal('55000.00')
                    issues_fixed += 1
            
            if employee_issues:
                issues_found += len(employee_issues)
                self.stdout.write(
                    self.style.WARNING(
                        f"Employee {employee.employee_id} ({user.get_full_name() if user else 'No User'}):"
                    )
                )
                for issue in employee_issues:
                    self.stdout.write(f"  - {issue}")
                
                if not dry_run:
                    employee.save()
                    self.stdout.write(self.style.SUCCESS("  ✓ Fixed"))
        
        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f'\nCleanup completed:\n'
                f'- Issues found: {issues_found}\n'
                f'- Issues fixed: {issues_fixed if not dry_run else 0}\n'
            )
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    'This was a dry run. Use --no-dry-run to apply changes.'
                )
            )
