"""
Django management command to create comprehensive sample data
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import datetime, timedelta, date
import random
from decimal import Decimal

from ems.models import (
    Role, UserProfile, Department, Employee, Project, Task,
    LeaveType, LeaveRequest, Attendance, Budget, Expense,
    AssetCategory, Asset, Supplier, PurchaseOrder,
    Announcement, Message, Document, Meeting
)


class Command(BaseCommand):
    help = 'Create comprehensive sample data for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before creating new data',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing data...')
            # Clear in reverse dependency order
            Meeting.objects.all().delete()
            Document.objects.all().delete()
            Message.objects.all().delete()
            Announcement.objects.all().delete()
            PurchaseOrder.objects.all().delete()
            Supplier.objects.all().delete()
            Asset.objects.all().delete()
            AssetCategory.objects.all().delete()
            Expense.objects.all().delete()
            Budget.objects.all().delete()
            Attendance.objects.all().delete()
            LeaveRequest.objects.all().delete()
            LeaveType.objects.all().delete()
            Task.objects.all().delete()
            Project.objects.all().delete()
            Employee.objects.all().delete()
            Department.objects.all().delete()
            UserProfile.objects.all().delete()
            User.objects.filter(is_superuser=False).delete()
            Role.objects.all().delete()

        self.stdout.write('Creating sample data...')

        # Create Roles
        roles_data = [
            {'name': 'SUPERADMIN', 'name_ar': 'مدير النظام الأعلى', 'description': 'Super Administrator with full system access'},
            {'name': 'ADMIN', 'name_ar': 'المدير العام', 'description': 'Administrator with full access'},
            {'name': 'HR_MANAGER', 'name_ar': 'مدير الموارد البشرية', 'description': 'HR Manager with HR module access'},
            {'name': 'FINANCE_MANAGER', 'name_ar': 'مدير المالية', 'description': 'Finance Manager with financial access'},
            {'name': 'DEPARTMENT_MANAGER', 'name_ar': 'مدير القسم', 'description': 'Department Manager'},
            {'name': 'PROJECT_MANAGER', 'name_ar': 'مدير المشروع', 'description': 'Project Manager'},
            {'name': 'EMPLOYEE', 'name_ar': 'موظف', 'description': 'Regular Employee'},
            {'name': 'INTERN', 'name_ar': 'متدرب', 'description': 'Intern'},
        ]

        roles = {}
        for role_data in roles_data:
            role, created = Role.objects.get_or_create(
                name=role_data['name'],
                defaults=role_data
            )
            roles[role_data['name']] = role
            if created:
                self.stdout.write(f'Created role: {role.get_name_display()}')

        # Create Departments
        departments_data = [
            {'name': 'Human Resources', 'name_ar': 'الموارد البشرية', 'description': 'Manages employee relations and policies'},
            {'name': 'Finance', 'name_ar': 'المالية', 'description': 'Handles financial operations and budgeting'},
            {'name': 'Information Technology', 'name_ar': 'تكنولوجيا المعلومات', 'description': 'Manages IT infrastructure and systems'},
            {'name': 'Sales', 'name_ar': 'المبيعات', 'description': 'Drives revenue through sales activities'},
            {'name': 'Marketing', 'name_ar': 'التسويق', 'description': 'Promotes products and brand awareness'},
            {'name': 'Operations', 'name_ar': 'العمليات', 'description': 'Manages day-to-day business operations'},
            {'name': 'Customer Service', 'name_ar': 'خدمة العملاء', 'description': 'Provides customer support and service'},
            {'name': 'Research & Development', 'name_ar': 'البحث والتطوير', 'description': 'Develops new products and innovations'},
        ]

        departments = {}
        for dept_data in departments_data:
            dept, created = Department.objects.get_or_create(
                name=dept_data['name'],
                defaults=dept_data
            )
            departments[dept_data['name']] = dept
            if created:
                self.stdout.write(f'Created department: {dept.name}')

        # Create Test Users and Employees
        users_data = [
            # Super Admin
            {
                'username': 'superadmin',
                'email': '<EMAIL>',
                'first_name': 'Super',
                'last_name': 'Admin',
                'first_name_ar': 'المدير',
                'last_name_ar': 'العام',
                'role': 'SUPERADMIN',
                'department': 'Information Technology',
                'position': 'Chief Executive Officer',
                'position_ar': 'الرئيس التنفيذي',
                'phone': '******-0001',
                'salary': Decimal('150000.00')
            },
            # Admin
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'first_name': 'System',
                'last_name': 'Administrator',
                'first_name_ar': 'مدير',
                'last_name_ar': 'النظام',
                'role': 'ADMIN',
                'department': 'Information Technology',
                'position': 'System Administrator',
                'position_ar': 'مدير النظام',
                'phone': '******-0000',
                'salary': Decimal('120000.00')
            },
            # HR Manager
            {
                'username': 'hrmanager',
                'email': '<EMAIL>',
                'first_name': 'Sarah',
                'last_name': 'Johnson',
                'first_name_ar': 'سارة',
                'last_name_ar': 'جونسون',
                'role': 'HR_MANAGER',
                'department': 'Human Resources',
                'position': 'HR Manager',
                'position_ar': 'مدير الموارد البشرية',
                'phone': '******-0002',
                'salary': Decimal('85000.00')
            },
            # Finance Manager
            {
                'username': 'financemanager',
                'email': '<EMAIL>',
                'first_name': 'Michael',
                'last_name': 'Chen',
                'first_name_ar': 'مايكل',
                'last_name_ar': 'تشين',
                'role': 'FINANCE_MANAGER',
                'department': 'Finance',
                'position': 'Finance Manager',
                'position_ar': 'مدير المالية',
                'phone': '******-0003',
                'salary': Decimal('90000.00')
            },
            # Department Manager - IT
            {
                'username': 'itmanager',
                'email': '<EMAIL>',
                'first_name': 'Ahmed',
                'last_name': 'Al-Rashid',
                'first_name_ar': 'أحمد',
                'last_name_ar': 'الراشد',
                'role': 'DEPARTMENT_MANAGER',
                'department': 'Information Technology',
                'position': 'IT Manager',
                'position_ar': 'مدير تكنولوجيا المعلومات',
                'phone': '******-0004',
                'salary': Decimal('95000.00')
            },
            # Sales Manager
            {
                'username': 'salesmanager',
                'email': '<EMAIL>',
                'first_name': 'Emily',
                'last_name': 'Rodriguez',
                'first_name_ar': 'إيميلي',
                'last_name_ar': 'رودريغيز',
                'role': 'DEPARTMENT_MANAGER',
                'department': 'Sales',
                'position': 'Sales Manager',
                'position_ar': 'مدير المبيعات',
                'phone': '******-0005',
                'salary': Decimal('88000.00')
            },
            # Project Manager
            {
                'username': 'projectmanager',
                'email': '<EMAIL>',
                'first_name': 'David',
                'last_name': 'Kim',
                'first_name_ar': 'ديفيد',
                'last_name_ar': 'كيم',
                'role': 'PROJECT_MANAGER',
                'department': 'Operations',
                'position': 'Project Manager',
                'position_ar': 'مدير المشروع',
                'phone': '******-0006',
                'salary': Decimal('82000.00')
            },
            # Team Lead
            {
                'username': 'teamlead',
                'email': '<EMAIL>',
                'first_name': 'Fatima',
                'last_name': 'Al-Zahra',
                'first_name_ar': 'فاطمة',
                'last_name_ar': 'الزهراء',
                'role': 'EMPLOYEE',
                'department': 'Information Technology',
                'position': 'Senior Developer',
                'position_ar': 'مطور أول',
                'phone': '******-0007',
                'salary': Decimal('75000.00')
            },
            # Regular Employees
            {
                'username': 'employee1',
                'email': '<EMAIL>',
                'first_name': 'John',
                'last_name': 'Doe',
                'first_name_ar': 'جون',
                'last_name_ar': 'دو',
                'role': 'EMPLOYEE',
                'department': 'Information Technology',
                'position': 'Software Developer',
                'position_ar': 'مطور برمجيات',
                'phone': '******-0008',
                'salary': Decimal('65000.00')
            },
            {
                'username': 'employee2',
                'email': '<EMAIL>',
                'first_name': 'Jane',
                'last_name': 'Smith',
                'first_name_ar': 'جين',
                'last_name_ar': 'سميث',
                'role': 'EMPLOYEE',
                'department': 'Marketing',
                'position': 'Marketing Specialist',
                'position_ar': 'أخصائي تسويق',
                'phone': '******-0009',
                'salary': Decimal('58000.00')
            },
            {
                'username': 'salesrep1',
                'email': '<EMAIL>',
                'first_name': 'Omar',
                'last_name': 'Hassan',
                'first_name_ar': 'عمر',
                'last_name_ar': 'حسن',
                'role': 'EMPLOYEE',
                'department': 'Sales',
                'position': 'Sales Representative',
                'position_ar': 'مندوب مبيعات',
                'phone': '******-0010',
                'salary': Decimal('52000.00')
            }
        ]

        employees = {}
        for user_data in users_data:
            # Create User
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'email': user_data['email'],
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'is_active': True,
                    'is_staff': user_data['role'] in ['ADMIN'],
                    'is_superuser': user_data['role'] == 'ADMIN'
                }
            )

            if created:
                # Set specific passwords for admin accounts, default for others
                if user_data['username'] == 'superadmin':
                    user.set_password('superadmin123')
                elif user_data['username'] == 'admin':
                    user.set_password('admin123')
                else:
                    user.set_password('password123')  # Default password for all test users
                user.save()
                self.stdout.write(f'Created user: {user.username}')

            # Create UserProfile
            profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'role': roles[user_data['role']],
                    'phone': user_data['phone'],
                    'preferred_language': 'en'
                }
            )

            # Create Employee
            employee, created = Employee.objects.get_or_create(
                user=user,
                defaults={
                    'employee_id': f'EMP{str(len(employees) + 1).zfill(3)}',
                    'department': departments[user_data['department']],
                    'position': user_data['position'],
                    'position_ar': user_data['position_ar'],
                    'phone': user_data['phone'],
                    'gender': 'M',  # Default gender
                    'hire_date': timezone.now().date() - timedelta(days=random.randint(30, 1000)),
                    'salary': user_data['salary'],
                    'is_active': True
                }
            )

            employees[user_data['username']] = employee
            if created:
                self.stdout.write(f'Created employee: {employee.user.get_full_name()}')

        # Create Leave Types
        leave_types_data = [
            {'name': 'Annual Leave', 'name_ar': 'إجازة سنوية', 'days_allowed': 21, 'is_paid': True},
            {'name': 'Sick Leave', 'name_ar': 'إجازة مرضية', 'days_allowed': 10, 'is_paid': True},
            {'name': 'Maternity Leave', 'name_ar': 'إجازة أمومة', 'days_allowed': 90, 'is_paid': True},
            {'name': 'Emergency Leave', 'name_ar': 'إجازة طارئة', 'days_allowed': 5, 'is_paid': False},
            {'name': 'Study Leave', 'name_ar': 'إجازة دراسية', 'days_allowed': 30, 'is_paid': False},
        ]

        leave_types = {}
        for lt_data in leave_types_data:
            leave_type, created = LeaveType.objects.get_or_create(
                name=lt_data['name'],
                defaults=lt_data
            )
            leave_types[lt_data['name']] = leave_type
            if created:
                self.stdout.write(f'Created leave type: {leave_type.name}')

        # Create Projects
        projects_data = [
            {
                'name': 'EMS System Upgrade',
                'name_ar': 'ترقية نظام إدارة المؤسسة',
                'description': 'Upgrading the enterprise management system',
                'department': 'Information Technology',
                'manager': 'projectmanager',
                'budget': Decimal('250000.00'),
                'status': 'in_progress'
            },
            {
                'name': 'Marketing Campaign Q4',
                'name_ar': 'حملة تسويقية الربع الرابع',
                'description': 'Q4 marketing campaign for new products',
                'department': 'Marketing',
                'manager': 'salesmanager',
                'budget': Decimal('150000.00'),
                'status': 'planning'
            },
            {
                'name': 'HR Policy Review',
                'name_ar': 'مراجعة سياسات الموارد البشرية',
                'description': 'Annual review of HR policies and procedures',
                'department': 'Human Resources',
                'manager': 'hrmanager',
                'budget': Decimal('50000.00'),
                'status': 'in_progress'
            }
        ]

        projects = {}
        for proj_data in projects_data:
            project, created = Project.objects.get_or_create(
                name=proj_data['name'],
                defaults={
                    'name_ar': proj_data['name_ar'],
                    'description': proj_data['description'],
                    'department': departments[proj_data['department']],
                    'project_manager': employees[proj_data['manager']],
                    'start_date': timezone.now().date(),
                    'end_date': timezone.now().date() + timedelta(days=180),
                    'budget_amount': proj_data['budget'],
                    'status': proj_data['status']
                }
            )
            projects[proj_data['name']] = project
            if created:
                self.stdout.write(f'Created project: {project.name}')

        # Create Sample Attendance Records (last 30 days)
        self.stdout.write('Creating attendance records...')
        for employee in employees.values():
            for i in range(30):  # Last 30 days
                date = timezone.now().date() - timedelta(days=i)
                # Skip weekends
                if date.weekday() < 5:  # Monday = 0, Sunday = 6
                    # 90% attendance rate
                    if random.random() < 0.9:
                        check_in = timezone.now().replace(
                            year=date.year,
                            month=date.month,
                            day=date.day,
                            hour=random.randint(8, 9),
                            minute=random.randint(0, 59)
                        )
                        check_out = check_in + timedelta(hours=8, minutes=random.randint(0, 60))

                        Attendance.objects.get_or_create(
                            employee=employee,
                            date=date,
                            defaults={
                                'check_in': check_in.time(),
                                'check_out': check_out.time(),
                                'is_present': True
                            }
                        )

        # Create Sample Leave Requests
        self.stdout.write('Creating leave requests...')
        for employee in list(employees.values())[:5]:  # First 5 employees
            for i in range(2):  # 2 leave requests each
                start_date = timezone.now().date() + timedelta(days=random.randint(10, 60))
                days_count = random.randint(1, 5)
                end_date = start_date + timedelta(days=days_count)
                leave_type = random.choice(list(leave_types.values()))

                LeaveRequest.objects.create(
                    employee=employee,
                    leave_type=leave_type,
                    start_date=start_date,
                    end_date=end_date,
                    days_requested=days_count,
                    reason=f'Personal {leave_type.name.lower()}',
                    status=random.choice(['PENDING', 'APPROVED', 'REJECTED'])
                )

        # Create Sample Announcements
        announcements_data = [
            {
                'title': 'Company Holiday Schedule',
                'title_ar': 'جدول العطل الرسمية للشركة',
                'content': 'Please note the upcoming holiday schedule for the year.',
                'content_ar': 'يرجى ملاحظة جدول العطل الرسمية القادمة للسنة.',
                'priority': 'high'
            },
            {
                'title': 'New Employee Orientation',
                'title_ar': 'توجيه الموظفين الجدد',
                'content': 'Welcome session for new employees will be held next week.',
                'content_ar': 'جلسة ترحيب للموظفين الجدد ستعقد الأسبوع القادم.',
                'priority': 'medium'
            }
        ]

        for ann_data in announcements_data:
            Announcement.objects.create(
                title=ann_data['title'],
                title_ar=ann_data['title_ar'],
                content=ann_data['content'],
                content_ar=ann_data['content_ar'],
                author=employees['superadmin'],
                priority=ann_data['priority'].upper(),
                is_published=True
            )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created comprehensive sample data:\n'
                f'- {len(roles)} roles\n'
                f'- {len(departments)} departments\n'
                f'- {len(employees)} users and employees\n'
                f'- {len(leave_types)} leave types\n'
                f'- {len(projects)} projects\n'
                f'- {Attendance.objects.count()} attendance records\n'
                f'- {LeaveRequest.objects.count()} leave requests\n'
                f'- {Announcement.objects.count()} announcements\n\n'
                f'Test user credentials:\n'
                f'- Super Admin: superadmin / superadmin123\n'
                f'- Admin: admin / admin123\n'
                f'- HR Manager: hrmanager / password123\n'
                f'- Finance Manager: financemanager / password123\n'
                f'- IT Manager: itmanager / password123\n'
                f'- Sales Manager: salesmanager / password123\n'
                f'- Project Manager: projectmanager / password123\n'
                f'- Team Lead: teamlead / password123\n'
                f'- Employee: employee1 / password123\n'
                f'- Employee: employee2 / password123\n'
                f'- Sales Rep: salesrep1 / password123'
            )
        )
