#!/usr/bin/env python3
"""
Comprehensive Test of Notification and Audit Logging Systems
Tests both systems working together
"""

import requests
import json
import time

# Configuration
BACKEND_URL = "http://localhost:8000"

def test_complete_systems():
    """Test notification and audit logging systems together"""
    
    print("🧪 Testing Complete Notification & Audit Logging Systems")
    print("=" * 70)
    
    # Get authentication token
    try:
        response = requests.post(
            f"{BACKEND_URL}/api/auth/login/",
            json={"username": "superadmin", "password": "password123"},
            timeout=5
        )
        if response.status_code != 200:
            print("❌ Authentication failed")
            return
        
        token = response.json().get('access')
        headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
        print("✅ Authentication successful")
    except Exception as e:
        print(f"❌ Authentication error: {str(e)}")
        return
    
    # Test 1: Notification System
    print("\n📢 Testing Notification System...")
    
    # Get initial notification stats
    notif_stats = requests.get(f"{BACKEND_URL}/api/notifications/notifications/stats/", headers=headers)
    if notif_stats.status_code == 200:
        initial_notif_count = notif_stats.json().get('total_notifications', 0)
        print(f"✅ Initial notifications: {initial_notif_count}")
    else:
        print("❌ Failed to get notification stats")
        return
    
    # Create a test notification
    notification_data = {
        'recipient': 1,  # superadmin user ID
        'title': 'System Integration Test',
        'title_ar': 'اختبار تكامل النظام',
        'message': 'Testing notification and audit logging integration.',
        'message_ar': 'اختبار تكامل الإشعارات وسجلات التدقيق.',
        'notification_type': 'system_alert',
        'priority': 'high',
        'action_url': 'http://localhost:5174/admin/dashboard'
    }
    
    notif_response = requests.post(f"{BACKEND_URL}/api/notifications/notifications/", 
                                  json=notification_data, headers=headers)
    
    if notif_response.status_code == 201:
        notification = notif_response.json()
        print(f"✅ Notification created: {notification.get('title')}")
    else:
        print(f"❌ Failed to create notification: {notif_response.text}")
        return
    
    # Test 2: Audit Logging System
    print("\n📋 Testing Audit Logging System...")
    
    # Get initial audit stats
    audit_stats = requests.get(f"{BACKEND_URL}/api/audit/logs/stats/", headers=headers)
    if audit_stats.status_code == 200:
        initial_audit_count = audit_stats.json().get('total_logs', 0)
        print(f"✅ Initial audit logs: {initial_audit_count}")
    else:
        print("❌ Failed to get audit stats")
        return
    
    # Test 3: Create Employee (should trigger both notifications and audit logs)
    print("\n👤 Testing Employee Creation (triggers both systems)...")
    
    import random
    unique_id = f'TEST{random.randint(1000, 9999)}'
    employee_data = {
        'firstName': 'Integration',
        'lastName': 'Test',
        'email': f'integration.test{random.randint(100, 999)}@example.com',
        'employee_id': unique_id,
        'position': 'Test Engineer',
        'position_ar': 'مهندس اختبار',
        'department': 24,  # Human Resources
        'gender': 'F',
        'hire_date': '2024-01-15',
        'salary': 6000
    }
    
    emp_response = requests.post(f"{BACKEND_URL}/api/employees/", 
                                json=employee_data, headers=headers)
    
    if emp_response.status_code == 201:
        employee = emp_response.json()
        print(f"✅ Employee created: {employee.get('firstName')} {employee.get('lastName')}")
        
        # Wait a moment for signals to process
        time.sleep(1)
        
        # Check if audit log was created
        audit_stats_after = requests.get(f"{BACKEND_URL}/api/audit/logs/stats/", headers=headers)
        if audit_stats_after.status_code == 200:
            new_audit_count = audit_stats_after.json().get('total_logs', 0)
            audit_increase = new_audit_count - initial_audit_count
            print(f"✅ Audit logs increased by: {audit_increase}")
            
            # Check action types
            actions = audit_stats_after.json().get('actions_by_type', {})
            print(f"✅ Actions captured: {actions}")
        
        # Check if notification was created for HR managers
        notif_stats_after = requests.get(f"{BACKEND_URL}/api/notifications/notifications/stats/", headers=headers)
        if notif_stats_after.status_code == 200:
            new_notif_count = notif_stats_after.json().get('total_notifications', 0)
            notif_increase = new_notif_count - initial_notif_count
            print(f"✅ Notifications increased by: {notif_increase}")
        
        # Test 4: Update Employee (should trigger audit log)
        print("\n📝 Testing Employee Update...")
        
        update_data = {'salary': 6500, 'position': 'Senior Test Engineer'}
        update_response = requests.patch(f"{BACKEND_URL}/api/employees/{employee['id']}/", 
                                       json=update_data, headers=headers)
        
        if update_response.status_code == 200:
            print("✅ Employee updated successfully")
            
            # Wait for audit log
            time.sleep(1)
            
            # Check audit logs for update
            audit_final = requests.get(f"{BACKEND_URL}/api/audit/logs/stats/", headers=headers)
            if audit_final.status_code == 200:
                final_audit_count = audit_final.json().get('total_logs', 0)
                total_increase = final_audit_count - initial_audit_count
                print(f"✅ Total audit logs created: {total_increase}")
        
        # Test 5: Security Event Simulation
        print("\n🔒 Testing Security Event Detection...")
        
        # Simulate multiple failed login attempts (should trigger security event)
        for i in range(3):
            requests.post(f"{BACKEND_URL}/api/auth/login/", 
                         json={"username": "nonexistent", "password": "wrongpassword"})
        
        time.sleep(1)
        
        # Check for security events
        security_events = requests.get(f"{BACKEND_URL}/api/audit/security-events/", headers=headers)
        if security_events.status_code == 200:
            events = security_events.json().get('results', [])
            print(f"✅ Security events detected: {len(events)}")
            if events:
                latest_event = events[0]
                print(f"✅ Latest event type: {latest_event.get('event_type')}")
                print(f"✅ Risk score: {latest_event.get('risk_score')}")
        
        # Clean up - delete test employee
        delete_response = requests.delete(f"{BACKEND_URL}/api/employees/{employee['id']}/", headers=headers)
        if delete_response.status_code == 204:
            print("✅ Test employee cleaned up")
        
    else:
        print(f"❌ Failed to create employee: {emp_response.text}")
        return
    
    # Test 6: Final System Status
    print("\n📊 Final System Status...")
    
    # Get final stats
    final_notif_stats = requests.get(f"{BACKEND_URL}/api/notifications/notifications/stats/", headers=headers)
    final_audit_stats = requests.get(f"{BACKEND_URL}/api/audit/logs/stats/", headers=headers)
    
    if final_notif_stats.status_code == 200 and final_audit_stats.status_code == 200:
        notif_data = final_notif_stats.json()
        audit_data = final_audit_stats.json()
        
        print(f"📢 Notification System:")
        print(f"  Total notifications: {notif_data.get('total_notifications')}")
        print(f"  Unread notifications: {notif_data.get('unread_notifications')}")
        print(f"  Notifications today: {notif_data.get('notifications_today')}")
        
        print(f"📋 Audit Logging System:")
        print(f"  Total audit logs: {audit_data.get('total_logs')}")
        print(f"  Failed actions: {audit_data.get('failed_actions')}")
        print(f"  Security events: {audit_data.get('security_events')}")
        print(f"  Unique users: {audit_data.get('unique_users')}")
        print(f"  Actions by type: {audit_data.get('actions_by_type')}")
    
    print("\n" + "=" * 70)
    print("🎉 COMPREHENSIVE SYSTEM TEST COMPLETED!")
    print("✅ Notification System: WORKING")
    print("✅ Audit Logging System: WORKING") 
    print("✅ System Integration: WORKING")
    print("✅ Security Monitoring: WORKING")
    print("=" * 70)

if __name__ == "__main__":
    test_complete_systems()
