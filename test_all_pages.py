#!/usr/bin/env python3
"""
Comprehensive End-to-End Page Testing
Tests every single page in the EMS application to find all issues
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:5174"

class ComprehensivePageTester:
    def __init__(self):
        self.all_issues = []
        self.pages_tested = []
        self.tokens = {}
        
    def log_issue(self, page, issue_type, description, severity="HIGH"):
        self.all_issues.append({
            'page': page,
            'issue_type': issue_type,
            'description': description,
            'severity': severity,
            'timestamp': datetime.now().isoformat()
        })
        print(f"❌ {severity}: {page} - {issue_type}: {description}")

    def log_page_tested(self, page, status):
        self.pages_tested.append({
            'page': page,
            'status': status,
            'timestamp': datetime.now().isoformat()
        })
        if status == "SUCCESS":
            print(f"✅ {page}: Working")
        else:
            print(f"⚠️ {page}: {status}")

    def get_auth_tokens(self):
        """Get authentication tokens for different user roles"""
        users = [
            ("superadmin", "password123"),
            ("hrmanager", "password123"),
            ("financemanager", "password123"),
            ("employee1", "password123")
        ]
        
        for username, password in users:
            try:
                response = requests.post(
                    f"{BACKEND_URL}/api/auth/login/",
                    json={"username": username, "password": password},
                    timeout=5
                )
                if response.status_code == 200:
                    data = response.json()
                    self.tokens[username] = {
                        'access': data.get('access'),
                        'user': data.get('user'),
                        'role': data.get('user', {}).get('role', {}).get('id')
                    }
                    print(f"✅ Got token for {username} (role: {self.tokens[username]['role']})")
                else:
                    self.log_issue("Authentication", "Login Failed", f"Failed to login as {username}")
            except Exception as e:
                self.log_issue("Authentication", "Network Error", f"Cannot login as {username}: {str(e)}")

    def test_api_endpoint(self, endpoint, token, page_name):
        """Test if an API endpoint is working"""
        try:
            headers = {"Authorization": f"Bearer {token}"} if token else {}
            response = requests.get(f"{BACKEND_URL}{endpoint}", headers=headers, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, dict) and 'results' in data:
                    count = data.get('count', 0)
                    return True, f"API working - {count} records"
                elif isinstance(data, list):
                    return True, f"API working - {len(data)} records"
                else:
                    return True, "API responding with data"
            elif response.status_code == 401:
                return False, "Authentication required"
            elif response.status_code == 403:
                return False, "Permission denied"
            elif response.status_code == 404:
                return False, "Endpoint not found"
            else:
                return False, f"HTTP {response.status_code}"
        except Exception as e:
            return False, f"Network error: {str(e)}"

    def test_superadmin_pages(self):
        """Test all Super Admin pages"""
        if 'superadmin' not in self.tokens:
            self.log_issue("Super Admin", "No Token", "Cannot test - no authentication token")
            return
            
        token = self.tokens['superadmin']['access']
        
        # Super Admin specific pages and their API endpoints
        pages = [
            ("Super Admin Dashboard", "/api/dashboard-stats/"),
            ("System Stats", "/api/superadmin/system-stats/"),
            ("User Management", "/api/auth/users/"),
            ("Employee Management", "/api/employees/"),
            ("Department Management", "/api/departments/"),
            ("Project Management", "/api/projects/"),
            ("Task Management", "/api/tasks/"),
            ("Leave Management", "/api/leave-requests/"),
            ("Finance Management", "/api/expenses/"),
            ("Budget Management", "/api/budgets/"),
            ("Asset Management", "/api/assets/"),
            ("Inventory Management", "/api/inventory/"),
            ("Customer Service", "/api/customer-service/tickets/"),
            ("AI Management", "/api/customer-service/ai-assistants/"),
            ("Analytics", "/api/analytics/reports/"),
            ("Compliance", "/api/compliance/audits/"),
            ("Security Center", "/api/security/logs/"),
            ("System Settings", "/api/settings/"),
        ]
        
        print(f"\n🔧 Testing Super Admin Pages ({len(pages)} pages)...")
        for page_name, endpoint in pages:
            success, message = self.test_api_endpoint(endpoint, token, page_name)
            if success:
                self.log_page_tested(page_name, "SUCCESS")
            else:
                self.log_page_tested(page_name, f"FAILED: {message}")
                self.log_issue(page_name, "API Error", message)

    def test_hr_manager_pages(self):
        """Test all HR Manager pages"""
        if 'hrmanager' not in self.tokens:
            self.log_issue("HR Manager", "No Token", "Cannot test - no authentication token")
            return
            
        token = self.tokens['hrmanager']['access']
        
        pages = [
            ("HR Dashboard", "/api/dashboard-stats/"),
            ("Employee Records", "/api/employees/"),
            ("Department Management", "/api/departments/"),
            ("Leave Requests", "/api/leave-requests/"),
            ("Attendance", "/api/attendance/"),
            ("Performance Reviews", "/api/performance-reviews/"),
            ("Payroll", "/api/payroll/"),
            ("Recruitment", "/api/recruitment/"),
            ("Training", "/api/training/"),
            ("HR Reports", "/api/hr/reports/"),
        ]
        
        print(f"\n👥 Testing HR Manager Pages ({len(pages)} pages)...")
        for page_name, endpoint in pages:
            success, message = self.test_api_endpoint(endpoint, token, page_name)
            if success:
                self.log_page_tested(page_name, "SUCCESS")
            else:
                self.log_page_tested(page_name, f"FAILED: {message}")
                self.log_issue(page_name, "API Error", message)

    def test_finance_manager_pages(self):
        """Test all Finance Manager pages"""
        if 'financemanager' not in self.tokens:
            self.log_issue("Finance Manager", "No Token", "Cannot test - no authentication token")
            return
            
        token = self.tokens['financemanager']['access']
        
        pages = [
            ("Finance Dashboard", "/api/dashboard-stats/"),
            ("Budget Management", "/api/budgets/"),
            ("Expense Management", "/api/expenses/"),
            ("Financial Reports", "/api/finance/reports/"),
            ("Purchase Orders", "/api/purchase-orders/"),
            ("Invoices", "/api/invoices/"),
            ("Asset Tracking", "/api/assets/"),
            ("Cost Centers", "/api/cost-centers/"),
            ("Financial Analytics", "/api/finance/analytics/"),
        ]
        
        print(f"\n💰 Testing Finance Manager Pages ({len(pages)} pages)...")
        for page_name, endpoint in pages:
            success, message = self.test_api_endpoint(endpoint, token, page_name)
            if success:
                self.log_page_tested(page_name, "SUCCESS")
            else:
                self.log_page_tested(page_name, f"FAILED: {message}")
                self.log_issue(page_name, "API Error", message)

    def test_employee_pages(self):
        """Test all Employee pages"""
        if 'employee1' not in self.tokens:
            self.log_issue("Employee", "No Token", "Cannot test - no authentication token")
            return
            
        token = self.tokens['employee1']['access']
        
        pages = [
            ("Employee Dashboard", "/api/dashboard-stats/"),
            ("My Profile", "/api/auth/user/"),
            ("My Tasks", "/api/tasks/"),
            ("My Leave Requests", "/api/leave-requests/"),
            ("My Attendance", "/api/attendance/"),
            ("My Messages", "/api/messages/"),
            ("My Calendar", "/api/calendar/events/"),
            ("My Performance", "/api/performance-reviews/"),
            ("Company Directory", "/api/employees/"),
        ]
        
        print(f"\n👤 Testing Employee Pages ({len(pages)} pages)...")
        for page_name, endpoint in pages:
            success, message = self.test_api_endpoint(endpoint, token, page_name)
            if success:
                self.log_page_tested(page_name, "SUCCESS")
            else:
                self.log_page_tested(page_name, f"FAILED: {message}")
                self.log_issue(page_name, "API Error", message)

    def test_public_pages(self):
        """Test public pages that don't require authentication"""
        pages = [
            ("Landing Page", None),
            ("Login Page", None),
            ("How It Works", None),
        ]
        
        print(f"\n🌐 Testing Public Pages ({len(pages)} pages)...")
        
        # Test frontend accessibility
        try:
            response = requests.get(FRONTEND_URL, timeout=5)
            if response.status_code == 200:
                self.log_page_tested("Landing Page", "SUCCESS")
            else:
                self.log_page_tested("Landing Page", f"HTTP {response.status_code}")
                self.log_issue("Landing Page", "HTTP Error", f"Status code: {response.status_code}")
        except Exception as e:
            self.log_page_tested("Landing Page", f"FAILED: {str(e)}")
            self.log_issue("Landing Page", "Network Error", str(e))

    def run_comprehensive_test(self):
        """Run comprehensive test of all pages"""
        print("🧪 Starting Comprehensive End-to-End Page Testing...")
        print("=" * 70)
        
        # Get authentication tokens
        print("\n🔐 Getting Authentication Tokens...")
        self.get_auth_tokens()
        
        # Test all page categories
        self.test_public_pages()
        self.test_superadmin_pages()
        self.test_hr_manager_pages()
        self.test_finance_manager_pages()
        self.test_employee_pages()
        
        # Generate comprehensive report
        self.generate_comprehensive_report()

    def generate_comprehensive_report(self):
        """Generate detailed report of all issues found"""
        print("\n" + "=" * 70)
        print("📋 COMPREHENSIVE END-TO-END TESTING REPORT")
        print("=" * 70)
        
        # Summary statistics
        total_pages = len(self.pages_tested)
        successful_pages = len([p for p in self.pages_tested if p['status'] == 'SUCCESS'])
        failed_pages = total_pages - successful_pages
        
        print(f"\n📊 TESTING SUMMARY:")
        print(f"Total Pages Tested: {total_pages}")
        print(f"✅ Successful: {successful_pages}")
        print(f"❌ Failed: {failed_pages}")
        if total_pages > 0:
            print(f"🎯 Success Rate: {(successful_pages/total_pages)*100:.1f}%")
        
        # Issues by category
        if self.all_issues:
            print(f"\n🚨 ISSUES FOUND ({len(self.all_issues)} total):")
            
            # Group by issue type
            issue_types = {}
            for issue in self.all_issues:
                issue_type = issue['issue_type']
                if issue_type not in issue_types:
                    issue_types[issue_type] = []
                issue_types[issue_type].append(issue)
            
            for issue_type, issues in issue_types.items():
                print(f"\n📂 {issue_type} ({len(issues)} issues):")
                for issue in issues:
                    print(f"  • {issue['page']}: {issue['description']}")
        else:
            print("\n🎉 NO ISSUES FOUND!")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if failed_pages > 0:
            print("1. Fix API endpoints that are returning errors")
            print("2. Implement missing backend endpoints")
            print("3. Add proper error handling for failed API calls")
            print("4. Test frontend components with missing data")
        else:
            print("All pages are working correctly!")

if __name__ == "__main__":
    tester = ComprehensivePageTester()
    tester.run_comprehensive_test()
