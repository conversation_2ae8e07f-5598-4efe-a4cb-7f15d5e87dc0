#!/usr/bin/env python3
"""
Test Frontend Navigation
Tests actual navigation to pages to find which ones are broken
"""

import requests
import json
import time

# Configuration
FRONTEND_URL = "http://localhost:5174"
BACKEND_URL = "http://localhost:8000"

class FrontendNavigationTester:
    def __init__(self):
        self.broken_pages = []
        self.working_pages = []
        self.token = None

    def get_auth_token(self):
        """Get authentication token"""
        try:
            response = requests.post(
                f"{BACKEND_URL}/api/auth/login/",
                json={"username": "superadmin", "password": "password123"},
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('access')
                return True
            return False
        except Exception as e:
            print(f"❌ Authentication failed: {str(e)}")
            return False

    def test_frontend_pages(self):
        """Test specific frontend routes that are commonly problematic"""
        
        # Define the pages that should exist based on the routing
        test_pages = [
            # Super Admin Pages
            ("/admin/dashboard", "Super Admin Dashboard"),
            ("/admin/system", "System Settings"),
            ("/admin/security", "Security Center"),
            ("/admin/ai", "AI Management"),
            ("/admin/analytics", "Advanced Analytics"),
            ("/admin/compliance", "Compliance Center"),
            ("/admin/users", "User Management"),
            
            # HR Manager Pages
            ("/hr/dashboard", "HR Dashboard"),
            ("/hr/employees", "HR Employees"),
            ("/hr/departments", "HR Departments"),
            ("/hr/leave", "Leave Management"),
            ("/hr/attendance", "Attendance"),
            ("/hr/performance", "Performance"),
            ("/hr/payroll", "Payroll"),
            ("/hr/reports", "HR Reports"),
            
            # Finance Manager Pages
            ("/finance/dashboard", "Finance Dashboard"),
            ("/finance/budgets", "Finance Budgets"),
            ("/finance/expenses", "Expenses"),
            ("/finance/reports", "Finance Reports"),
            
            # Employee Pages
            ("/employee/dashboard", "Employee Dashboard"),
            ("/employee/profile", "Employee Profile"),
            ("/employee/tasks", "Employee Tasks"),
            ("/employee/leave", "Employee Leave"),
            ("/employee/attendance", "Employee Attendance"),
            
            # Common Pages
            ("/employees", "Employees"),
            ("/departments", "Departments"),
            ("/projects", "Projects"),
            ("/tasks", "Tasks"),
            ("/reports", "Reports"),
            ("/settings", "Settings"),
            
            # Customer Service Pages
            ("/customer-service/dashboard", "Support Dashboard"),
            ("/customer-service/tickets", "Ticket Management"),
            ("/customer-service/knowledge-base", "Knowledge Base"),
            ("/customer-service/live-chat", "Live Chat"),
            ("/customer-service/feedback", "Customer Feedback"),
            ("/customer-service/agents", "Agent Performance"),
            
            # Sales Pages
            ("/sales/dashboard", "Sales Dashboard"),
            ("/sales/orders", "Sales Orders"),
            ("/sales/customers", "Sales Customers"),
            ("/sales/quotations", "Quotations"),
            ("/sales/pipeline", "Sales Pipeline"),
            
            # Asset Management
            ("/assets", "Assets"),
            ("/suppliers", "Suppliers"),
            ("/purchase-orders", "Purchase Orders"),
            ("/inventory", "Inventory"),
            
            # Communication
            ("/messages", "Messages"),
            ("/announcements", "Announcements"),
            ("/documents", "Documents"),
            ("/meetings", "Meetings"),
            ("/calendar", "Calendar"),
            
            # Personal
            ("/personal/profile", "Personal Profile"),
            ("/personal/messages", "Personal Messages"),
            ("/personal/calendar", "Personal Calendar"),
            
            # KPI
            ("/kpi/dashboard", "KPI Dashboard"),
            ("/kpi/management", "KPI Management"),
        ]

        print(f"\n🧪 Testing {len(test_pages)} frontend pages...")
        
        for route, page_name in test_pages:
            try:
                # Test if the frontend can serve the page
                # Note: This tests if the route exists, not if it renders correctly
                full_url = f"{FRONTEND_URL}{route}"
                response = requests.get(full_url, timeout=3)
                
                if response.status_code == 200:
                    # Check if it's actually serving the React app (not a 404 page)
                    if 'react' in response.text.lower() or 'vite' in response.text.lower() or len(response.text) > 1000:
                        self.working_pages.append((route, page_name, "Frontend route exists"))
                        print(f"✅ {page_name}: {route}")
                    else:
                        self.broken_pages.append((route, page_name, "Route returns empty/invalid content"))
                        print(f"⚠️ {page_name}: {route} - Invalid content")
                else:
                    self.broken_pages.append((route, page_name, f"HTTP {response.status_code}"))
                    print(f"❌ {page_name}: {route} - HTTP {response.status_code}")
                    
            except Exception as e:
                self.broken_pages.append((route, page_name, f"Error: {str(e)}"))
                print(f"❌ {page_name}: {route} - {str(e)}")

    def test_api_endpoints_for_pages(self):
        """Test API endpoints that pages depend on"""
        if not self.token:
            print("❌ No authentication token - skipping API tests")
            return

        headers = {"Authorization": f"Bearer {self.token}"}
        
        # Critical API endpoints that pages depend on
        api_endpoints = [
            ("/api/dashboard-stats/", "Dashboard Stats"),
            ("/api/employees/", "Employees API"),
            ("/api/departments/", "Departments API"),
            ("/api/projects/", "Projects API"),
            ("/api/tasks/", "Tasks API"),
            ("/api/leave-requests/", "Leave Requests API"),
            ("/api/attendance/", "Attendance API"),
            ("/api/expenses/", "Expenses API"),
            ("/api/budgets/", "Budgets API"),
            ("/api/assets/", "Assets API"),
            ("/api/suppliers/", "Suppliers API"),
            ("/api/purchase-orders/", "Purchase Orders API"),
            ("/api/messages/", "Messages API"),
            ("/api/announcements/", "Announcements API"),
            ("/api/documents/", "Documents API"),
            ("/api/meetings/", "Meetings API"),
            ("/api/customers/", "Customers API"),
            ("/api/products/", "Products API"),
            ("/api/reports/", "Reports API"),
            ("/api/sales-orders/", "Sales Orders API"),
        ]

        print(f"\n🔌 Testing {len(api_endpoints)} API endpoints...")
        
        api_working = []
        api_broken = []
        
        for endpoint, name in api_endpoints:
            try:
                response = requests.get(f"{BACKEND_URL}{endpoint}", headers=headers, timeout=5)
                if response.status_code == 200:
                    api_working.append((endpoint, name))
                    print(f"✅ {name}: {endpoint}")
                else:
                    api_broken.append((endpoint, name, response.status_code))
                    print(f"❌ {name}: {endpoint} - HTTP {response.status_code}")
            except Exception as e:
                api_broken.append((endpoint, name, str(e)))
                print(f"❌ {name}: {endpoint} - {str(e)}")

        return api_working, api_broken

    def generate_report(self):
        """Generate comprehensive report"""
        print("\n" + "=" * 70)
        print("📋 FRONTEND NAVIGATION TEST REPORT")
        print("=" * 70)
        
        total_pages = len(self.working_pages) + len(self.broken_pages)
        
        print(f"\n📊 FRONTEND PAGES SUMMARY:")
        print(f"Total pages tested: {total_pages}")
        print(f"✅ Working pages: {len(self.working_pages)}")
        print(f"❌ Broken pages: {len(self.broken_pages)}")
        
        if total_pages > 0:
            success_rate = (len(self.working_pages) / total_pages) * 100
            print(f"🎯 Success rate: {success_rate:.1f}%")
        
        if self.broken_pages:
            print(f"\n🚨 BROKEN PAGES ({len(self.broken_pages)}):")
            for route, page_name, error in self.broken_pages:
                print(f"  ❌ {page_name}")
                print(f"     Route: {route}")
                print(f"     Issue: {error}")
                print()
        
        if len(self.broken_pages) == 0:
            print("\n🎉 ALL FRONTEND PAGES ARE WORKING!")
        else:
            print(f"\n💡 RECOMMENDATIONS:")
            print("1. Check if the broken routes are properly defined in the routing files")
            print("2. Verify that the corresponding page components exist")
            print("3. Check for any JavaScript errors in the browser console")
            print("4. Ensure all required dependencies are installed")

def main():
    tester = FrontendNavigationTester()
    
    print("🧪 Starting Frontend Navigation Testing...")
    print("=" * 70)
    
    # Get authentication
    print("\n🔐 Getting authentication token...")
    auth_success = tester.get_auth_token()
    
    if auth_success:
        print("✅ Authentication successful")
    else:
        print("⚠️ Authentication failed - some tests may not work")
    
    # Test frontend pages
    tester.test_frontend_pages()
    
    # Test API endpoints
    if auth_success:
        api_working, api_broken = tester.test_api_endpoints_for_pages()
    
    # Generate report
    tester.generate_report()

if __name__ == "__main__":
    main()
